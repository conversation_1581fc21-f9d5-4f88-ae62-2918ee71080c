import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Define CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Simplified representation of the expected session data structure
interface PartialFullOnboardingSessionData {
    onboardingSession?: {
        id?: string;
        [key: string]: unknown;
    };
    [key: string]: unknown;
}

Deno.serve(async (req: Request) => {
    // Handle OPTIONS request for CORS preflight
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders });
    }

    try {
        const authorization = req.headers.get('Authorization');
        if (!authorization) {
            console.warn('Authorization header missing');
            return new Response(JSON.stringify({ error: 'Authorization header missing' }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 401,
            });
        }

        // Initialize Supabase client with the user's auth context
        const supabaseClient = createClient(
            Deno.env.get('SUPABASE_URL') ?? '',
            Deno.env.get('SUPABASE_ANON_KEY') ?? '',
            { global: { headers: { Authorization: authorization } } }
        );

        // Get the user from the JWT
        const { data: { user }, error: userError } = await supabaseClient.auth.getUser();

        if (userError || !user) {
            console.error('User authentication error:', userError?.message || 'User not found.');
            return new Response(JSON.stringify({ error: 'Authentication failed', details: userError?.message }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 401,
            });
        }

        console.log(`[load-or-create-session] Authenticated user: ${user.id}`);

        // 1. Attempt to load existing session data by calling the existing RPC
        console.log(`[load-or-create-session] Attempting to load session for user: ${user.id} via get_onboarding_session_data`);
        const { data: existingSessionData, error: loadError } = await supabaseClient
            .schema('public')
            .rpc('get_onboarding_session_data');

        if (loadError) {
            console.error(`[load-or-create-session] Error calling get_onboarding_session_data for user ${user.id}:`, loadError);
            return new Response(JSON.stringify({ error: 'Failed to load onboarding session data', details: loadError.message }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 500,
            });
        }

        // If loadError is null, the RPC call succeeded. Check if data represents an active session.
        const typedExistingSession = existingSessionData as PartialFullOnboardingSessionData | null;

        if (typedExistingSession && typedExistingSession.onboardingSession?.id) {
            console.log(`[load-or-create-session] Existing session found for user ${user.id}: ${typedExistingSession.onboardingSession.id}`);
            return new Response(JSON.stringify(typedExistingSession), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 200,
            });
        }

        // 2. If no session, create a new one by calling the new RPC function
        console.log(`[load-or-create-session] No existing session for user ${user.id}. Attempting to create new one via create_user_onboarding_session_with_steps.`);
        const { data: newSessionData, error: createError } = await supabaseClient.schema('public').rpc('create_user_onboarding_session_with_steps');

        if (createError) {
            console.error(`[load-or-create-session] Error calling create_user_onboarding_session_with_steps for user ${user.id}:`, createError);
            return new Response(JSON.stringify({ error: 'Failed to create new onboarding session', details: createError.message }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 500,
            });
        }

        if (!newSessionData) {
            console.error(`[load-or-create-session] create_user_onboarding_session_with_steps returned no data for user ${user.id}.`);
            return new Response(JSON.stringify({ error: 'Failed to create new onboarding session', details: 'Creation function returned no data.' }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 500,
            });
        }

        const typedNewSession = newSessionData as PartialFullOnboardingSessionData | null;
        console.log(`[load-or-create-session] New session created successfully for user ${user.id}: ${typedNewSession?.onboardingSession?.id || 'Unknown ID'}`);
        return new Response(JSON.stringify(typedNewSession), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 201,
        });

    } catch (e: unknown) {
        const error = e as Error;
        console.error('[load-or-create-session] Unexpected error in Edge Function:', error.message, error.stack);
        return new Response(JSON.stringify({ error: 'An unexpected error occurred', details: error.message }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
        });
    }
}); 