import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { v4 as uuidv4 } from 'https://deno.land/std@0.200.0/uuid/mod.ts'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// List of valid agreement types (must match the enum in the schema)
const VALID_AGREEMENT_TYPES = [
    'Service Agreement',
    'Privacy Policy',
    'Direct Debit',
    'Terms and Conditions'
]

// Helper to validate base64 data
function isValidBase64(str: string): boolean {
    if (!str.startsWith('data:image/')) {
        return false
    }

    const base64Data = str.split(',')[1]
    if (!base64Data) {
        return false
    }

    try {
        atob(base64Data)
        return true
    } catch (e) {
        return false
    }
}

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        // Get Supabase credentials from environment variables
        const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string

        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing Supabase environment variables')
        }

        // Create Supabase client with service role key (admin privileges)
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        // Get authorization header from request
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
            return new Response(
                JSON.stringify({ error: 'Missing authorization header' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Authenticate the user from the JWT token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabase.auth.getUser(token)

        if (authError || !user) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Parse request payload
        const { sessionId, agreementType, signatureData } = await req.json()

        // Validate input
        if (!sessionId || !agreementType || !signatureData) {
            return new Response(
                JSON.stringify({ error: 'Missing required fields: sessionId, agreementType, or signatureData' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Validate agreement type against the enum
        if (!VALID_AGREEMENT_TYPES.includes(agreementType)) {
            return new Response(
                JSON.stringify({
                    error: 'Invalid agreement type',
                    validTypes: VALID_AGREEMENT_TYPES
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Validate signature data
        if (!isValidBase64(signatureData)) {
            return new Response(
                JSON.stringify({ error: 'Invalid signature data format. Expected base64 image data.' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Verify user owns the session and get step_4_id using hierarchical structure
        const { data: sessionData, error: sessionError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .select(`
                id,
                user_id,
                step_4_agreements ( id )
            `)
            .eq('id', sessionId)
            .single()

        if (sessionError || !sessionData) {
            console.error('Session fetch error:', sessionError)
            return new Response(
                JSON.stringify({ error: 'Session not found' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
            )
        }

        if (sessionData.user_id !== user.id) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized: You do not own this session' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
            )
        }

        if (!sessionData.step_4_agreements || sessionData.step_4_agreements.length === 0) {
            return new Response(
                JSON.stringify({ error: 'Setup error: Step 4 agreements has not been initialized for this session' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        const step4Id = sessionData.step_4_agreements[0].id

        // Process the signature image
        // 1. Convert base64 to binary
        const base64Data = signatureData.split(',')[1]
        const binaryData = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))

        // 2. Generate unique filename and path using the hierarchical structure
        const uniqueId = uuidv4()
        const sanitizedAgreementType = agreementType.toLowerCase().replace(/\s+/g, '-')
        const filePath = `onboarding_sessions/${sessionId}/agreements/${step4Id}/${uniqueId}-signature-${sanitizedAgreementType}.png`

        // 3. Upload the signature to the 'farms' bucket
        const { data: uploadData, error: uploadError } = await supabase
            .storage
            .from('farms')
            .upload(filePath, binaryData, {
                contentType: 'image/png',
                upsert: false
            })

        if (uploadError) {
            console.error('Upload error:', uploadError)
            throw new Error(`Failed to upload signature: ${uploadError.message}`)
        }

        console.log('Signature uploaded successfully to:', filePath)

        // 4. Update the agreement record using the hierarchical structure
        const agreementData = {
            step_4_id: step4Id,
            agreement_type: agreementType,
            is_agreed: true,
            signature_storage_path: filePath,
            agreed_at: new Date().toISOString(),
            agreement_version: '1.0'
        }

        const { data: upsertData, error: upsertError } = await supabase
            .schema('farms')
            .from('agreements')
            .upsert(agreementData, {
                onConflict: 'step_4_id,agreement_type'
            })
            .select()

        if (upsertError) {
            console.error('Agreement upsert error:', upsertError)
            throw new Error(`Failed to record consent: ${upsertError.message}`)
        }

        console.log('Agreement recorded successfully:', upsertData)

        // Return success response
        return new Response(
            JSON.stringify({
                success: true,
                message: `${agreementType} signed and recorded successfully`,
                signatureStoragePath: filePath,
                agreementId: upsertData?.[0]?.id
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        console.error('Error processing signature:', error)
        return new Response(
            JSON.stringify({
                error: `Error processing signature: ${error.message}`
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
    }
}) 