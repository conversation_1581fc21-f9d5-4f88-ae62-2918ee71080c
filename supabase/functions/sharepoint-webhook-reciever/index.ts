// File: supabase/functions/sharepoint-webhook-receiver/index.ts
import { createClient } from 'npm:@supabase/supabase-js@2';
import { Buff<PERSON> } from 'node:buffer';
import { webcrypto } from 'node:crypto';
// Helper to decrypt the payload from Microsoft Graph
async function decryptPayload(encryptedContent) {
  const privateKeyPem = Deno.env.get('SHAREPOINT_WEBHOOK_PRIVATE_KEY');
  // 1. Import the private key
  const privateKey = await webcrypto.subtle.importKey('pkcs8', Buffer.from(privateKeyPem.replace(/-----BEGIN PRIVATE KEY-----|-----E<PERSON> PRIVATE KEY-----|\n/g, ''), 'base64'), {
    name: 'RSA-<PERSON><PERSON><PERSON>',
    hash: 'SHA-1'
  }, true, [
    'decrypt'
  ]);
  // 2. Decrypt the symmetric key using the private key (RSA-OAEP)
  const decryptedSymmetricKey = await webcrypto.subtle.decrypt({
    name: 'RSA-OAEP'
  }, privateKey, Buffer.from(encryptedContent.dataKey, 'base64'));
  // 3. Import the decrypted symmetric key for AES-CBC
  const aesKey = await webcrypto.subtle.importKey('raw', decryptedSymmetricKey, {
    name: 'AES-CBC',
    length: 256
  }, false, [
    'decrypt'
  ]);
  // 4. Decrypt the actual resource data using the symmetric key (AES-CBC)
  const iv = decryptedSymmetricKey.slice(0, 16);
  const decryptedDataBuffer = await webcrypto.subtle.decrypt({
    name: 'AES-CBC',
    iv
  }, aesKey, Buffer.from(encryptedContent.data, 'base64'));
  // 5. Decode and parse the decrypted data
  const decryptedText = new TextDecoder().decode(decryptedDataBuffer);
  return JSON.parse(decryptedText);
}
Deno.serve(async (req)=>{
  const url = new URL(req.url);
  // 1. Handle the one-time subscription validation request from Microsoft Graph
  if (url.searchParams.has('validationToken')) {
    const validationToken = decodeURIComponent(url.searchParams.get('validationToken'));
    console.log('Received validation request. Responding with token.');
    return new Response(validationToken, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain'
      }
    });
  }
  try {
    const notification = await req.json();
    const clientState = Deno.env.get('SHAREPOINT_WEBHOOK_CLIENT_STATE'); // Store your clientState as a secret
    // Process each notification in the batch
    for (const notif of notification.value){
      // 2. Security: Validate the clientState to ensure the notification is legitimate
      if (notif.clientState !== clientState) {
        console.warn('Received notification with invalid clientState. Ignoring.');
        continue; // Skip this notification
      }
      // 3. Decrypt the resource data payload
      const decryptedResource = await decryptPayload(notif.encryptedContent);
      console.log('Decrypted resource:', decryptedResource);
      // 4. CRITICAL: Loop Prevention Check
      // Check if the file has the 'syncSource' field set to 'Supabase'.
      if (decryptedResource.fields?.syncSource === 'Supabase') {
        console.log(`Loop detected for file: ${decryptedResource.name}. Change originated from Supabase. Ignoring.`);
        continue; // Skip to the next notification
      }
      // 5. Connect to Supabase with admin privileges
      const supabaseAdmin = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'));
      const bucketName = 'farms';
      const filePath = decryptedResource.parentReference.path.split('root:') + '/' + decryptedResource.name;
      const cleanFilePath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
      // 6. Perform storage operation based on resource state
      if (decryptedResource['@removed']?.reason === 'deleted') {
        // Handle DELETE operation
        console.log(`Deleting file from Supabase: ${cleanFilePath}`);
        const { error } = await supabaseAdmin.storage.from(bucketName).remove([
          cleanFilePath
        ]);
        if (error) console.error('Supabase delete error:', error.message);
      } else {
        // Handle CREATE or UPDATE operation (upsert)
        const downloadUrl = decryptedResource['@microsoft.graph.downloadUrl'];
        if (!downloadUrl) {
          console.warn(`No download URL for file: ${cleanFilePath}. Skipping.`);
          continue;
        }
        console.log(`Upserting file to Supabase: ${cleanFilePath}`);
        const fileResponse = await fetch(downloadUrl);
        if (!fileResponse.ok) {
          console.error(`Failed to download file from SharePoint: ${fileResponse.statusText}`);
          continue;
        }
        const fileBlob = await fileResponse.blob();
        const { error } = await supabaseAdmin.storage.from(bucketName).upload(cleanFilePath, fileBlob, {
          upsert: true
        });
        if (error) console.error('Supabase upload error:', error.message);
      }
    }
    // 7. Acknowledge receipt to Microsoft Graph immediately
    return new Response(null, {
      status: 202
    });
  } catch (error) {
    console.error('Error processing webhook notification:', error.message);
    // Return a 500 to indicate failure, but Graph might retry.
    // It's often better to return 202 and handle errors internally.
    return new Response('Error processing notification', {
      status: 500
    });
  }
});
