// supabase/functions/cleanup-orphan-storage-files/index.ts
/// <reference types="https://deno.land/x/deno/cli/types/dts/lib.deno.d.ts" />

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient, SupabaseClient, FileObject } from "https://esm.sh/@supabase/supabase-js@2.43.4";
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

console.log("Edge Function `cleanup-orphan-storage-files` invoked.");

// Define a type for the response
interface CleanupResult {
  scannedItemsCount: number;
  actualFilesListedCount: number;
  dbDocumentPathsCount: number;
  orphansFoundCount: number;
  orphansDeletedCount: number;
  errors: string[];
  deletedOrphanPaths?: string[]; // Optional: list paths of deleted files
}

// Interface for items in the deleteData array, which might include an error property
interface DeletionAttempt extends FileObject { // Extends FileObject for known props
  error?: { // Based on common Supabase error object structure
    name: string;
    message: string;
    status?: number; // from StorageError
    // statusCode?: string; // another common Supabase error property
  } | null;
}

async function getSupabaseAdminClient(): Promise<SupabaseClient> {
  const supabaseUrl = Deno.env.get("SUPABASE_URL");
  const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error(
      "Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables.",
    );
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
  });
}

serve(async (req: Request) => {
  // Handle OPTIONS request for CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  const result: CleanupResult = {
    scannedItemsCount: 0,
    actualFilesListedCount: 0,
    dbDocumentPathsCount: 0,
    orphansFoundCount: 0,
    orphansDeletedCount: 0,
    errors: [],
    // deletedOrphanPaths: [], // Uncomment if you want to return paths
  };

  try {
    const supabaseAdmin = await getSupabaseAdminClient();
    const bucketName = "farms";

    // Use FileObject from supabase-js, and make metadata: Record<string, unknown>
    let allStorageFileObjects: FileObject[] = [];
    let offset = 0;
    const limit = 100;
    let hasMore = true;
    let totalItemsListedByApi = 0;

    console.log(`Fetching files from bucket: ${bucketName}...`);
    while (hasMore) {
      const { data: storageItems, error: listError } = await supabaseAdmin.storage
        .from(bucketName)
        .list(undefined, {
          limit: limit,
          offset: offset,
          sortBy: { column: 'name', order: 'asc' },
        });

      if (listError) {
        throw new Error(`Error listing storage items: ${listError.message}`);
      }

      if (storageItems && storageItems.length > 0) {
        totalItemsListedByApi += storageItems.length;
        const filesOnly = storageItems.filter(item => item.id !== null); // id is null for folders
        allStorageFileObjects = allStorageFileObjects.concat(filesOnly);
        offset += storageItems.length;
      } else {
        hasMore = false;
      }

      if (storageItems === null || storageItems.length < limit) {
        hasMore = false;
      }
    }
    result.scannedItemsCount = totalItemsListedByApi;
    result.actualFilesListedCount = allStorageFileObjects.length;
    console.log(`Storage API listed ${result.scannedItemsCount} items. Found ${result.actualFilesListedCount} actual files.`);

    // 2. Fetch all document paths from the database
    console.log("Fetching document paths from 'farms.documents' table...");
    const { data: dbDocuments, error: dbError } = await supabaseAdmin
      .from("documents")
      .select("storage_bucket_path")
      .schema("farms");

    if (dbError) {
      throw new Error(
        `Error fetching document paths from DB: ${dbError.message}`,
      );
    }

    const dbDocumentPaths = new Set(
      dbDocuments?.map((doc: { storage_bucket_path: string }) => doc.storage_bucket_path) || [],
    );
    result.dbDocumentPathsCount = dbDocumentPaths.size;
    console.log(`Found ${result.dbDocumentPathsCount} unique document paths in database.`);

    // 3. Identify orphans
    const orphanFilePaths: string[] = [];
    for (const storageFile of allStorageFileObjects) {
      const storageFilePath = storageFile.name; // `name` is the full path from bucket root here
      if (!dbDocumentPaths.has(storageFilePath)) {
        orphanFilePaths.push(storageFilePath);
      }
    }
    result.orphansFoundCount = orphanFilePaths.length;
    console.log(`Identified ${result.orphansFoundCount} orphan files.`);

    // 4. Delete orphans
    if (orphanFilePaths.length > 0) {
      console.log(`Attempting to delete ${orphanFilePaths.length} orphan files...`);
      const { data: deleteData, error: deleteError } = await supabaseAdmin.storage
        .from(bucketName)
        .remove(orphanFilePaths);

      if (deleteError) {
        result.errors.push(`Error deleting files: ${deleteError.message}`);
        console.error("Error deleting orphan files:", deleteError);
        let successfulDeletes = 0;
        if (deleteData) { // deleteData is FileObject[] | null
          // Check each item for an error property
          successfulDeletes = deleteData.filter(item => !(item as DeletionAttempt).error).length;
        }
        result.orphansDeletedCount = successfulDeletes;
      } else {
        result.orphansDeletedCount = orphanFilePaths.length; // All attempted were successful
        console.log(`${result.orphansDeletedCount} orphan files successfully deleted.`);
      }
    } else {
      console.log("No orphan files to delete.");
    }
  } catch (error: unknown) {
    console.error("Critical error in cleanup-orphan-storage-files:", error);
    result.errors.push(
      error instanceof Error ? error.message : String(error),
    );
  }

  return new Response(JSON.stringify(result, null, 2), {
    headers: { ...corsHeaders, "Content-Type": "application/json" },
    status: result.errors.length > 0 ? 500 : 200,
  });
});