// validate-australian-business-number.ts
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        // Parse request JSON
        const { abn } = await req.json()

        // Input validation
        if (!abn || typeof abn !== 'string') {
            return new Response(
                JSON.stringify({ isValid: false, error: 'ABN is required' }),
                {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                    status: 400
                }
            )
        }

        // Format validation
        const sanitizedABN = abn.replace(/\s/g, '')
        if (!sanitizedABN.match(/^\d{11}$/)) {
            return new Response(
                JSON.stringify({ isValid: false, error: 'Invalid ABN format. Expected format: XX XXX XXX XXX' }),
                {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                    status: 400
                }
            )
        }

        // Get API credentials from environment
        const ASIC_API_KEY = Deno.env.get('ASIC_API_KEY')
        const ASIC_API_ENDPOINT = Deno.env.get('ASIC_API_ENDPOINT') || 'https://abr.business.gov.au/json/AbnDetails.aspx'

        if (!ASIC_API_KEY) {
            console.error('ASIC_API_KEY environment variable is not set')
            return new Response(
                JSON.stringify({ isValid: false, error: 'ABN validation service is not configured' }),
                {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                    status: 503
                }
            )
        }

        // Make request to ABR API with timeout
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

        try {
            const apiUrl = `${ASIC_API_ENDPOINT}?abn=${sanitizedABN}&guid=${ASIC_API_KEY}`
            console.log(`Making ABN validation request for: ${sanitizedABN}`)

            const response = await fetch(apiUrl, {
                signal: controller.signal,
                headers: {
                    'User-Agent': 'NewTerra-Onboarding/1.0'
                }
            })

            clearTimeout(timeoutId)

            if (!response.ok) {
                console.error(`ABR API returned status: ${response.status} ${response.statusText}`)
                return new Response(
                    JSON.stringify({ isValid: false, error: 'Error contacting ABR service' }),
                    {
                        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                        status: 503
                    }
                )
            }

            const data = await response.json()
            console.log(`ABR API response for ${sanitizedABN}:`, data)

            // Process ABR response
            // The API typically returns a structure with "AbnStatus": "Active" for valid ABNs
            const isValid = data.AbnStatus === 'Active'

            if (!isValid) {
                // If ABN is not active (e.g., not registered, cancelled), return as invalid
                return new Response(
                    JSON.stringify({
                        isValid: false,
                        error: 'This ABN is not registered or not active.',
                        businessName: data.BusinessName || null,
                        entityType: data.EntityType || null,
                        businessState: data.AddressState || null
                    }),
                    {
                        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                        status: 200 // Changed from 404 to 200 as this is expected behavior
                    }
                )
            }

            return new Response(
                JSON.stringify({
                    isValid: true,
                    businessName: data.BusinessName,
                    entityType: data.EntityType,
                    businessState: data.AddressState
                }),
                {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                }
            )

        } catch (fetchError: unknown) {
            clearTimeout(timeoutId)

            if (fetchError instanceof Error && fetchError.name === 'AbortError') {
                console.error('ABN validation request timed out')
                return new Response(
                    JSON.stringify({
                        isValid: false,
                        error: 'ABN validation request timed out. Please try again.'
                    }),
                    {
                        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                        status: 504
                    }
                )
            }

            throw fetchError
        }

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        console.error('Error validating ABN:', errorMessage)

        // Handle errors gracefully
        return new Response(
            JSON.stringify({
                isValid: false,
                error: 'Error validating ABN: ' + errorMessage
            }),
            {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 500
            }
        )
    }
}) 