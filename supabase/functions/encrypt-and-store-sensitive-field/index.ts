import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import * as CryptoJS from 'https://esm.sh/crypto-js@4.2.0'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// List of tables that can contain sensitive fields.
// V2 Schema: Use the new, simplified table names.
const ALLOWED_TABLES = [
    'bookkeeping',
    'payments',
    'payroll'
]

// List of fields that can be encrypted
const ALLOWED_FIELDS = [
    'access_credentials',
    'bank_account_details',
    'encrypted_access_credentials'
]

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        // Get Supabase credentials from environment variables
        const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string

        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing Supabase environment variables')
        }

        // Create Supabase client with service role key (admin privileges)
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        // Get authorization header from request
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
            return new Response(
                JSON.stringify({ error: 'Missing authorization header' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Authenticate the user from the JWT token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabase.auth.getUser(token)

        if (authError || !user) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Parse request payload
        const { tableName, sessionId, fieldName, plainTextValue } = await req.json()

        // Validate input
        if (!tableName || !sessionId || !fieldName || plainTextValue === undefined) {
            return new Response(
                JSON.stringify({ error: 'Missing required fields: tableName, sessionId, fieldName, or plainTextValue' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Validate table name
        if (!ALLOWED_TABLES.includes(tableName)) {
            return new Response(
                JSON.stringify({ error: 'Invalid table name. Cannot encrypt data for this table.' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Validate field name
        if (!ALLOWED_FIELDS.includes(fieldName)) {
            return new Response(
                JSON.stringify({ error: 'Invalid field name. Cannot encrypt this field.' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Verify the user owns the session
        const { data: sessionData, error: sessionError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .select('id, user_id')
            .eq('id', sessionId)
            .single()

        if (sessionError || !sessionData) {
            return new Response(
                JSON.stringify({ error: 'Session not found' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
            )
        }

        if (sessionData.user_id !== user.id) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized: You do not own this session' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
            )
        }

        // Get encryption key from environment
        const encryptionKey = Deno.env.get('PGCRYPTO_KEY')
        if (!encryptionKey) {
            throw new Error('Missing encryption key in environment variables')
        }

        // Encrypt the plaintext value using CryptoJS
        // We use AES encryption to be compatible with pgcrypto's decrypt function
        const encrypted = CryptoJS.AES.encrypt(plainTextValue, encryptionKey).toString()

        // Convert the encrypted string to a Uint8Array for Postgres BYTEA column
        const encryptedBytes = new TextEncoder().encode(encrypted)

        // The RPC function expects the clean table name without a schema prefix.
        // The validation above against ALLOWED_TABLES ensures this is safe.
        const { data: updateData, error: updateError } = await supabase.rpc(
            'update_encrypted_field',
            {
                p_table_name: tableName,
                p_session_id: sessionId,
                p_field_name: fieldName,
                p_encrypted_value: Array.from(encryptedBytes) // Convert Uint8Array to regular array for JSON
            }
        )

        if (updateError) {
            throw new Error(`Failed to update encrypted field: ${updateError.message}`)
        }

        // Return success response
        return new Response(
            JSON.stringify({
                success: true,
                message: `Successfully encrypted and stored sensitive data in ${fieldName}`
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        // Handle errors gracefully
        return new Response(
            JSON.stringify({ error: `Error encrypting sensitive data: ${error.message}` }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
    }
}) 