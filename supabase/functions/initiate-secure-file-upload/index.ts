import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Helper function to sanitize file names
function sanitizeFileName(fileName: string): string {
    if (!fileName || typeof fileName !== 'string') {
        throw new Error('Invalid fileName: must be a non-empty string')
    }

    // Remove special characters, path traversals, and limit length
    const sanitized = fileName
        .replace(/[^a-z0-9_.-]/gi, '_')
        .replace(/(\.\.)|(\/)/g, '') // Remove .. and /
        .substring(0, 100)

    if (sanitized.length === 0) {
        throw new Error('Invalid fileName: resulted in empty string after sanitization')
    }

    return sanitized
}

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        console.log('Starting initiate-secure-file-upload function')

        // Get Supabase credentials from environment variables
        const supabaseUrl = Deno.env.get('SUPABASE_URL')
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

        if (!supabaseUrl || !supabaseServiceKey) {
            console.error('Missing Supabase environment variables')
            return new Response(
                JSON.stringify({ error: 'Server configuration error.' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
        }

        // Parse request payload with enhanced error handling
        let requestBody
        try {
            console.log('Parsing request body')
            requestBody = await req.json()
            console.log('Request body parsed successfully')
        } catch (jsonError) {
            console.error('Failed to parse JSON request body:', jsonError)
            return new Response(
                JSON.stringify({
                    error: 'Invalid JSON in request body',
                    details: jsonError instanceof Error ? jsonError.message : 'Unknown JSON parsing error'
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        const { sessionId, fileName, relatedToEntity, relatedToId } = requestBody

        // Enhanced input validation with detailed error reporting
        console.log('Validating input fields:', { sessionId, fileName, relatedToEntity, relatedToId })

        const validationErrors: string[] = []

        if (!sessionId || typeof sessionId !== 'string' || sessionId.trim() === '') {
            validationErrors.push('sessionId is required and must be a non-empty string')
        }

        if (!fileName || typeof fileName !== 'string' || fileName.trim() === '') {
            validationErrors.push('fileName is required and must be a non-empty string')
        }

        if (!relatedToEntity || typeof relatedToEntity !== 'string' || relatedToEntity.trim() === '') {
            validationErrors.push('relatedToEntity is required and must be a non-empty string')
        }

        if (!relatedToId || typeof relatedToId !== 'string' || relatedToId.trim() === '') {
            validationErrors.push('relatedToId is required and must be a non-empty string')
        }

        // UUID format validation for sessionId and relatedToId
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

        if (sessionId && !uuidRegex.test(sessionId)) {
            validationErrors.push('sessionId must be a valid UUID format')
        }

        if (relatedToId && !uuidRegex.test(relatedToId)) {
            validationErrors.push('relatedToId must be a valid UUID format')
        }

        if (validationErrors.length > 0) {
            console.error('Validation errors:', validationErrors)
            return new Response(
                JSON.stringify({
                    error: 'Input validation failed',
                    details: validationErrors.join('; '),
                    validationErrors
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        console.log('Input validation passed')

        // Create Supabase Admin Client
        const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

        // Authenticate User and Verify Session Ownership
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
            console.error('Missing authorization header')
            return new Response(
                JSON.stringify({ error: 'Missing authorization header' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        const token = authHeader.replace('Bearer ', '')
        console.log('Authenticating user with token length:', token.length)

        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

        if (authError || !user) {
            console.error('Auth error:', authError)
            return new Response(
                JSON.stringify({ error: 'Unauthorized' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        console.log('User authenticated successfully:', user.id)

        // Verify the user owns the session
        console.log('Verifying session ownership for sessionId:', sessionId)

        const { data: sessionData, error: sessionError } = await supabaseAdmin
            .schema('farms')
            .from('onboarding_sessions')
            .select('user_id')
            .eq('id', sessionId)
            .single()

        if (sessionError) {
            console.error('Error fetching session:', sessionError)
            return new Response(
                JSON.stringify({
                    error: 'Session not found or server error.',
                    details: sessionError.message
                }),
                {
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                    status: sessionError.code === 'PGRST116' ? 404 : 500, // PGRST116: "Requested range not satisfiable" -> single() found no rows
                }
            )
        }

        if (!sessionData || sessionData.user_id !== user.id) {
            console.error('Session ownership verification failed:', {
                sessionExists: !!sessionData,
                sessionUserId: sessionData?.user_id,
                authenticatedUserId: user.id
            })
            return new Response(
                JSON.stringify({ error: 'Forbidden: You do not own this session' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
            )
        }

        console.log('Session ownership verified successfully')

        // Sanitize filename and construct the path with enhanced error handling
        let sanitizedFileName, uniqueFileName, filePath
        try {
            console.log('Sanitizing filename:', fileName)
            sanitizedFileName = sanitizeFileName(fileName)
            // Generate a simple unique filename using timestamp and random number
            const timestamp = Date.now()
            const random = Math.floor(Math.random() * 100000)
            uniqueFileName = `${timestamp}_${random}_${sanitizedFileName}`
            filePath = `onboarding_sessions/${sessionId}/${relatedToEntity}/${relatedToId}/${uniqueFileName}`
            console.log('File path constructed:', filePath)
        } catch (sanitizationError) {
            console.error('Error during filename sanitization:', sanitizationError)
            return new Response(
                JSON.stringify({
                    error: 'Invalid filename',
                    details: sanitizationError instanceof Error ? sanitizationError.message : 'Filename sanitization failed'
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Create Signed Upload URL with enhanced error handling
        console.log('Creating signed upload URL for bucket: farms, path:', filePath)

        let signedUrlData, storageError
        try {
            const result = await supabaseAdmin.storage
                .from('farms')
                .createSignedUploadUrl(filePath)

            signedUrlData = result.data
            storageError = result.error

            console.log('Storage operation completed:', { hasData: !!signedUrlData, hasError: !!storageError })

        } catch (storageCallError) {
            console.error('Exception during storage call:', storageCallError)
            return new Response(
                JSON.stringify({
                    error: 'Failed to create upload URL',
                    details: storageCallError instanceof Error ? storageCallError.message : 'Unknown storage error'
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
        }

        if (storageError) {
            console.error('Storage error creating signed URL:', storageError)
            return new Response(
                JSON.stringify({
                    error: 'Failed to create signed upload URL',
                    details: storageError.message
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
        }

        if (!signedUrlData?.signedUrl) {
            console.error('No signed URL returned from storage')
            return new Response(
                JSON.stringify({
                    error: 'Storage operation succeeded but no signed URL was returned',
                    details: 'This indicates a potential issue with the storage service'
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
        }

        console.log('Signed URL created successfully, URL length:', signedUrlData.signedUrl.length)

        // Return the signed URL and path to the client
        return new Response(
            JSON.stringify({
                signedUrl: signedUrlData.signedUrl,
                filePath
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        console.error('Unexpected error in initiate-secure-file-upload:', error)
        console.error('Error details:', {
            name: error instanceof Error ? error.name : 'Unknown',
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
        })

        return new Response(
            JSON.stringify({
                error: 'An unexpected error occurred during upload initialization',
                details: error instanceof Error ? error.message : 'Unknown error type'
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
    }
}) 