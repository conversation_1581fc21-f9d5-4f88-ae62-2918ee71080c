// @deno-types="https://esm.sh/@supabase/supabase-js@2/dist/module/index.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Starting finalize-secure-file-upload function')

    // Get Supabase credentials from environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return new Response(
        JSON.stringify({ error: 'Server configuration error.' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Create Supabase client with service role key (admin privileges)
    // Use an admin client to call the SECURITY DEFINER SQL function `finalize_document_upload`
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

    // Get authorization header from request to identify the user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('Missing authorization header')
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    console.log('Authenticating user with token length:', token.length)

    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      console.error('Auth error:', authError)
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    console.log('User authenticated successfully:', user.id)

    // Parse request payload with enhanced error handling
    let requestBody
    try {
      console.log('Parsing request body')
      requestBody = await req.json()
      console.log('Request body parsed successfully')
    } catch (jsonError) {
      console.error('Failed to parse JSON request body:', jsonError)
      return new Response(
        JSON.stringify({
          error: 'Invalid JSON in request body',
          details: jsonError instanceof Error ? jsonError.message : 'Unknown JSON parsing error'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    const { sessionId, documentName, relatedToEntity, relatedToId, filePath } = requestBody

    // Enhanced input validation with detailed error reporting
    console.log('Validating input fields:', { sessionId, documentName, relatedToEntity, relatedToId, filePath })

    const validationErrors: string[] = []

    if (!sessionId || typeof sessionId !== 'string' || sessionId.trim() === '') {
      validationErrors.push('sessionId is required and must be a non-empty string')
    }

    if (!documentName || typeof documentName !== 'string' || documentName.trim() === '') {
      validationErrors.push('documentName is required and must be a non-empty string')
    }

    if (!relatedToEntity || typeof relatedToEntity !== 'string' || relatedToEntity.trim() === '') {
      validationErrors.push('relatedToEntity is required and must be a non-empty string')
    }

    if (!relatedToId || typeof relatedToId !== 'string' || relatedToId.trim() === '') {
      validationErrors.push('relatedToId is required and must be a non-empty string')
    }

    if (!filePath || typeof filePath !== 'string' || filePath.trim() === '') {
      validationErrors.push('filePath is required and must be a non-empty string')
    }

    // UUID format validation for sessionId and relatedToId
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

    if (sessionId && !uuidRegex.test(sessionId)) {
      validationErrors.push('sessionId must be a valid UUID format')
    }

    if (relatedToId && !uuidRegex.test(relatedToId)) {
      validationErrors.push('relatedToId must be a valid UUID format')
    }

    if (validationErrors.length > 0) {
      console.error('Validation errors:', validationErrors)
      return new Response(
        JSON.stringify({
          error: 'Input validation failed',
          details: validationErrors.join('; '),
          validationErrors
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    console.log('Input validation passed')

    // Verify the user owns the session (this check is also done in the SQL function, but good for early exit)
    const { data: sessionData, error: sessionFetchError } = await supabaseAdmin
      .schema('farms')
      .from('onboarding_sessions')
      .select('user_id')
      .eq('id', sessionId)
      .single()

    if (sessionFetchError) {
      console.error('Error fetching session:', sessionFetchError)
      return new Response(JSON.stringify({ error: 'Session not found or server error.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: sessionFetchError.code === 'PGRST116' ? 404 : 500, // PGRST116: "Requested range not satisfiable" -> single() found no rows
      });
    }

    if (!sessionData || sessionData.user_id !== user.id) {
      return new Response(
        JSON.stringify({ error: 'Forbidden: You do not own this session' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
      )
    }

    // Call the PostgreSQL function `public.finalize_document_upload`
    // The SQL function itself performs the crucial ownership check and inserts into farms.documents
    console.log('Calling finalize_document_upload RPC with payload:', {
      sessionId,
      documentName,
      relatedToEntity,
      relatedToId,
      filePath,
    })

    let rpcData, rpcError
    try {
      const rpcResult = await supabaseAdmin.rpc('finalize_document_upload', {
        payload: { // The SQL function expects a single JSON payload argument
          sessionId,
          documentName,
          relatedToEntity,
          relatedToId,
          filePath,
        },
      })

      rpcData = rpcResult.data
      rpcError = rpcResult.error

      console.log('RPC call completed:', { hasData: !!rpcData, hasError: !!rpcError })

    } catch (rpcCallError) {
      console.error('Exception during RPC call:', rpcCallError)
      return new Response(
        JSON.stringify({
          error: 'Failed to call finalize_document_upload function',
          details: rpcCallError instanceof Error ? rpcCallError.message : 'Unknown RPC call error'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    if (rpcError) {
      console.error('RPC error calling finalize_document_upload:', rpcError)
      console.error('RPC error details:', {
        code: rpcError.code,
        message: rpcError.message,
        details: rpcError.details,
        hint: rpcError.hint
      })

      // Check for specific errors with more robust pattern matching
      const errorMessage = rpcError.message || ''
      const errorDetails = rpcError.details || ''
      const combinedErrorText = `${errorMessage} ${errorDetails}`.toLowerCase()

      if (combinedErrorText.includes('unauthorized') || combinedErrorText.includes('forbidden')) {
        return new Response(
          JSON.stringify({
            error: 'Forbidden: Could not finalize document upload.',
            details: rpcError.message
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
        )
      }

      if (combinedErrorText.includes('session not found')) {
        return new Response(
          JSON.stringify({
            error: 'Session not found',
            details: rpcError.message
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
        )
      }

      if (combinedErrorText.includes('invalid payload') || combinedErrorText.includes('invalid') || combinedErrorText.includes('missing')) {
        return new Response(
          JSON.stringify({
            error: 'Invalid payload or missing required fields',
            details: rpcError.message
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
        )
      }

      // Generic error handling for other cases
      return new Response(
        JSON.stringify({
          error: 'Failed to finalize document upload',
          details: rpcError.message,
          code: rpcError.code
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // The SQL function `finalize_document_upload` returns the new document's UUID
    const documentId = rpcData
    console.log('Document finalized successfully, ID:', documentId)

    // Validate that we received a document ID
    if (!documentId) {
      console.error('RPC succeeded but no document ID returned')
      return new Response(
        JSON.stringify({
          error: 'Document finalization succeeded but no document ID was returned',
          details: 'This indicates a potential issue with the database function'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Return success response
    console.log('Returning success response')
    return new Response(
      JSON.stringify({
        success: true,
        documentId: documentId,
        message: 'Document upload finalized successfully.'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error in finalize-secure-file-upload:', error)
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })

    return new Response(
      JSON.stringify({
        error: 'An unexpected error occurred during document finalization',
        details: error instanceof Error ? error.message : 'Unknown error type'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
}) 