// @deno-types="https://esm.sh/@supabase/supabase-js@2/dist/module/index.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://terra-farm-digital-boost.lovable.app',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DeleteDocumentRequest {
  documentId: string;
  sessionId: string;
}

interface DeleteDocumentResponse {
  success: boolean;
  documentId?: string;
  message?: string;
  error?: string;
  details?: string;
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Starting delete-secure-document function')

    // Get Supabase credentials from environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return new Response(
        JSON.stringify({ error: 'Server configuration error.' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Create Supabase client with service role key (admin privileges)
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

    // Get authorization header from request to identify the user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('Missing authorization header')
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    console.log('Authenticating user with token length:', token.length)

    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      console.error('Auth error:', authError)
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    console.log('User authenticated successfully:', user.id)

    // Parse request payload with enhanced error handling
    let requestBody: DeleteDocumentRequest
    try {
      console.log('Parsing request body')
      requestBody = await req.json()
      console.log('Request body parsed successfully')
    } catch (jsonError) {
      console.error('Failed to parse JSON request body:', jsonError)
      return new Response(
        JSON.stringify({
          error: 'Invalid JSON in request body',
          details: jsonError instanceof Error ? jsonError.message : 'Unknown JSON parsing error'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    const { documentId, sessionId } = requestBody

    // Enhanced input validation with detailed error reporting
    console.log('Validating input fields:', { documentId, sessionId })

    const validationErrors: string[] = []

    if (!documentId || typeof documentId !== 'string' || documentId.trim() === '') {
      validationErrors.push('documentId is required and must be a non-empty string')
    }

    if (!sessionId || typeof sessionId !== 'string' || sessionId.trim() === '') {
      validationErrors.push('sessionId is required and must be a non-empty string')
    }

    // UUID format validation for documentId and sessionId
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

    if (documentId && !uuidRegex.test(documentId)) {
      validationErrors.push('documentId must be a valid UUID format')
    }

    if (sessionId && !uuidRegex.test(sessionId)) {
      validationErrors.push('sessionId must be a valid UUID format')
    }

    if (validationErrors.length > 0) {
      console.error('Validation errors:', validationErrors)
      return new Response(
        JSON.stringify({
          error: 'Input validation failed',
          details: validationErrors.join('; '),
          validationErrors
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    console.log('Input validation passed')

    // Verify the user owns the session
    const { data: sessionData, error: sessionFetchError } = await supabaseAdmin
      .schema('farms')
      .from('onboarding_sessions')
      .select('user_id')
      .eq('id', sessionId)
      .single()

    if (sessionFetchError) {
      console.error('Error fetching session:', sessionFetchError)
      return new Response(JSON.stringify({ error: 'Session not found or server error.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: sessionFetchError.code === 'PGRST116' ? 404 : 500,
      });
    }

    if (!sessionData || sessionData.user_id !== user.id) {
      return new Response(
        JSON.stringify({ error: 'Forbidden: You do not own this session' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
      )
    }

    // Fetch the document to get storage path before deletion
    const { data: documentData, error: documentFetchError } = await supabaseAdmin
      .schema('farms')
      .from('documents')
      .select('storage_bucket_path, onboarding_session_id')
      .eq('id', documentId)
      .single()

    if (documentFetchError) {
      console.error('Error fetching document:', documentFetchError)
      return new Response(JSON.stringify({ error: 'Document not found or server error.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: documentFetchError.code === 'PGRST116' ? 404 : 500,
      });
    }

    // Verify document belongs to the session
    if (documentData.onboarding_session_id !== sessionId) {
      return new Response(
        JSON.stringify({ error: 'Forbidden: Document does not belong to this session' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
      )
    }

    console.log('Document found with storage path:', documentData.storage_bucket_path)

    // Delete the file from Supabase Storage first
    let storageDeleteSuccessful = false
    try {
      console.log('Attempting to delete file from storage:', documentData.storage_bucket_path)
      
      const { error: storageError } = await supabaseAdmin.storage
        .from('farms')
        .remove([documentData.storage_bucket_path])

      if (storageError) {
        console.error('Storage deletion error:', storageError)
        // Don't fail the entire operation if storage deletion fails - log it for manual cleanup
        console.warn('Storage deletion failed but continuing with database deletion. Manual cleanup may be required.')
      } else {
        console.log('File successfully deleted from storage')
        storageDeleteSuccessful = true
      }
    } catch (storageDeleteError) {
      console.error('Exception during storage deletion:', storageDeleteError)
      console.warn('Storage deletion failed but continuing with database deletion. Manual cleanup may be required.')
    }

    // Call the PostgreSQL function to delete the document record
    console.log('Calling delete_document RPC with:', { documentId, sessionId })

    let rpcData, rpcError
    try {
      const rpcResult = await supabaseAdmin.rpc('delete_document', {
        p_document_id: documentId,
        p_session_id: sessionId,
      })

      rpcData = rpcResult.data
      rpcError = rpcResult.error

      console.log('RPC call completed:', { hasData: !!rpcData, hasError: !!rpcError })

    } catch (rpcCallError) {
      console.error('Exception during RPC call:', rpcCallError)
      return new Response(
        JSON.stringify({
          error: 'Failed to call delete_document function',
          details: rpcCallError instanceof Error ? rpcCallError.message : 'Unknown RPC call error'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    if (rpcError) {
      console.error('RPC error calling delete_document:', rpcError)
      console.error('RPC error details:', {
        code: rpcError.code,
        message: rpcError.message,
        details: rpcError.details,
        hint: rpcError.hint
      })

      // Check for specific errors
      const errorMessage = rpcError.message || ''
      const errorDetails = rpcError.details || ''
      const combinedErrorText = `${errorMessage} ${errorDetails}`.toLowerCase()

      if (combinedErrorText.includes('unauthorized') || combinedErrorText.includes('forbidden') || 
          combinedErrorText.includes('does not belong')) {
        return new Response(
          JSON.stringify({
            error: 'Forbidden: Could not delete document.',
            details: rpcError.message
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
        )
      }

      if (combinedErrorText.includes('not found')) {
        return new Response(
          JSON.stringify({
            error: 'Document not found',
            details: rpcError.message
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
        )
      }

      // Generic error handling for other cases
      return new Response(
        JSON.stringify({
          error: 'Failed to delete document',
          details: rpcError.message,
          code: rpcError.code
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // The SQL function returns the deleted document's UUID
    const deletedDocumentId = rpcData
    console.log('Document deleted successfully, ID:', deletedDocumentId)

    // Validate that we received a document ID
    if (!deletedDocumentId) {
      console.error('RPC succeeded but no document ID returned')
      return new Response(
        JSON.stringify({
          error: 'Document deletion succeeded but no document ID was returned',
          details: 'This indicates a potential issue with the database function'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Return success response
    console.log('Returning success response')
    const response: DeleteDocumentResponse = {
      success: true,
      documentId: deletedDocumentId,
      message: `Document deleted successfully${storageDeleteSuccessful ? ' including file from storage' : ' (database record only - file may require manual cleanup)'}.`
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error in delete-secure-document:', error)
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })

    return new Response(
      JSON.stringify({
        error: 'An unexpected error occurred during document deletion',
        details: error instanceof Error ? error.message : 'Unknown error type'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})