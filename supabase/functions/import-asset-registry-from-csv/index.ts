import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { parse as parseCSV } from 'https://deno.land/std@0.200.0/csv/parse.ts'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Define expected CSV column headers and their mapping to database fields
const COLUMN_MAPPING = {
    'asset_category': 'asset_category',
    'asset_type': 'asset_type',
    'make_or_provider': 'make_or_provider',
    'registration_or_policy_number': 'registration_or_policy_number',
    'renewal_date': 'renewal_date',
    'model_year': 'model_year',
    'serial_number': 'serial_number',
    'purchase_date': 'purchase_date',
    'purchase_price': 'purchase_price',
    'policy_type': 'policy_type',
    'coverage_amount': 'coverage_amount',
    'excess_amount': 'excess_amount'
}

// Validate a single row from the CSV
function validateRow(row: Record<string, string>, rowIndex: number) {
    const errors: string[] = []

    // Check required fields
    if (!row.asset_category) {
        errors.push(`Row ${rowIndex}: Asset Category is required`)
    } else if (!['Vehicle', 'Equipment', 'Insurance'].includes(row.asset_category)) {
        errors.push(`Row ${rowIndex}: Asset Category must be 'Vehicle', 'Equipment', or 'Insurance'`)
    }

    if (!row.asset_type) {
        errors.push(`Row ${rowIndex}: Asset Type is required`)
    }

    // Category-specific validation
    if (row.asset_category === 'Insurance') {
        if (!row.renewal_date) {
            errors.push(`Row ${rowIndex}: Renewal Date is required for Insurance assets`)
        }
        if (!row.policy_type) {
            errors.push(`Row ${rowIndex}: Policy Type is required for Insurance assets`)
        }
    } else if (['Vehicle', 'Equipment'].includes(row.asset_category)) {
        if (!row.registration_or_policy_number) {
            errors.push(`Row ${rowIndex}: Registration/Policy Number is required for ${row.asset_category} assets`)
        }
    }

    // Validate date formats if present (YYYY-MM-DD)
    if (row.renewal_date && !/^\d{4}-\d{2}-\d{2}$/.test(row.renewal_date)) {
        errors.push(`Row ${rowIndex}: Renewal Date must be in format YYYY-MM-DD`)
    }
    if (row.purchase_date && !/^\d{4}-\d{2}-\d{2}$/.test(row.purchase_date)) {
        errors.push(`Row ${rowIndex}: Purchase Date must be in format YYYY-MM-DD`)
    }

    // Validate numeric fields
    if (row.model_year && (!/^\d{4}$/.test(row.model_year) || parseInt(row.model_year) < 1900 || parseInt(row.model_year) > new Date().getFullYear() + 1)) {
        errors.push(`Row ${rowIndex}: Model Year must be a valid 4-digit year`)
    }
    if (row.purchase_price && (isNaN(parseFloat(row.purchase_price)) || parseFloat(row.purchase_price) < 0)) {
        errors.push(`Row ${rowIndex}: Purchase Price must be a positive number`)
    }
    if (row.coverage_amount && (isNaN(parseFloat(row.coverage_amount)) || parseFloat(row.coverage_amount) < 0)) {
        errors.push(`Row ${rowIndex}: Coverage Amount must be a positive number`)
    }
    if (row.excess_amount && (isNaN(parseFloat(row.excess_amount)) || parseFloat(row.excess_amount) < 0)) {
        errors.push(`Row ${rowIndex}: Excess Amount must be a positive number`)
    }

    return errors
}

// Process the multipart form data to extract the CSV file
async function extractCSVFromRequest(req: Request) {
    const contentType = req.headers.get('content-type') || ''

    // Ensure we're dealing with multipart form data
    if (!contentType.includes('multipart/form-data')) {
        throw new Error('Request must be multipart/form-data')
    }

    const formData = await req.formData()
    const csvFile = formData.get('file')

    if (!csvFile || !(csvFile instanceof File)) {
        throw new Error('No CSV file provided in the request')
    }

    const csvText = await csvFile.text()
    return csvText
}

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        // Get Supabase credentials from environment variables
        const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string

        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing Supabase environment variables')
        }

        // Create Supabase client with service role key (admin privileges)
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        // Get authorization header from request
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
            return new Response(
                JSON.stringify({ error: 'Missing authorization header' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Authenticate the user from the JWT token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabase.auth.getUser(token)

        if (authError || !user) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Get the CSV file from the request
        const csvText = await extractCSVFromRequest(req)

        // Parse the CSV - allow all possible columns
        const parsedCSV = parseCSV(csvText, {
            skipFirstRow: true,
            columns: Object.keys(COLUMN_MAPPING)
        })

        // Get user's onboarding session and step_3_id using hierarchical structure
        const { data: sessionData, error: sessionError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .select(`
                id,
                step_3_financial_systems ( id )
            `)
            .eq('user_id', user.id)
            .limit(1)
            .single()

        if (sessionError || !sessionData || !sessionData.step_3_financial_systems || sessionData.step_3_financial_systems.length === 0) {
            return new Response(
                JSON.stringify({ error: 'No active onboarding session or Step 3 financial systems record found' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
            )
        }

        const step3Id = sessionData.step_3_financial_systems[0].id

        // Validate all rows and collect any errors
        const validationErrors: string[] = []
        interface AssetRowData {
            step_3_id: string
            asset_category: string
            asset_type: string
            make_or_provider?: string | null
            registration_or_policy_number?: string | null
            renewal_date?: string | null
            model_year?: number | null
            serial_number?: string | null
            purchase_date?: string | null
            purchase_price?: number | null
            policy_type?: string | null
            coverage_amount?: number | null
            excess_amount?: number | null
        }
        const validRows: AssetRowData[] = []

        for (let i = 0; i < parsedCSV.length; i++) {
            const row = parsedCSV[i] as Record<string, string>
            const rowErrors = validateRow(row, i + 2) // +2 because we skip header (row 1) and 0-indexed

            if (rowErrors.length > 0) {
                validationErrors.push(...rowErrors)
            } else {
                // Prepare row data with proper type conversion
                const assetData: AssetRowData = {
                    step_3_id: step3Id,
                    asset_category: row.asset_category,
                    asset_type: row.asset_type,
                    make_or_provider: row.make_or_provider || null,
                    registration_or_policy_number: row.registration_or_policy_number || null,
                    renewal_date: row.renewal_date || null,
                    model_year: row.model_year ? parseInt(row.model_year) : null,
                    serial_number: row.serial_number || null,
                    purchase_date: row.purchase_date || null,
                    purchase_price: row.purchase_price ? parseFloat(row.purchase_price) : null,
                    policy_type: row.policy_type || null,
                    coverage_amount: row.coverage_amount ? parseFloat(row.coverage_amount) : null,
                    excess_amount: row.excess_amount ? parseFloat(row.excess_amount) : null
                }

                validRows.push(assetData)
            }
        }

        // If there are validation errors, return them without inserting any rows
        if (validationErrors.length > 0) {
            return new Response(
                JSON.stringify({
                    success: false,
                    importedCount: 0,
                    errorCount: validationErrors.length,
                    errors: validationErrors
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // No validation errors, proceed with batch insert into the assets table
        const { data: insertData, error: insertError } = await supabase
            .schema('farms')
            .from('assets')
            .insert(validRows)

        if (insertError) {
            throw new Error(`Failed to import assets: ${insertError.message}`)
        }

        // Return success response
        return new Response(
            JSON.stringify({
                success: true,
                importedCount: validRows.length,
                errorCount: 0,
                message: `Successfully imported ${validRows.length} assets`
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        console.error('Error importing assets:', error)
        return new Response(
            JSON.stringify({
                success: false,
                error: `Error importing assets: ${error.message}`
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
    }
}) 