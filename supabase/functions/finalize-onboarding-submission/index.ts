import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        // Get Supabase credentials from environment variables
        const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string

        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing Supabase environment variables')
        }

        // Create Supabase client with service role key (admin privileges)
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        // Get authorization header from request
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
            return new Response(
                JSON.stringify({ error: 'Missing authorization header' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Authenticate the user from the JWT token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabase.auth.getUser(token)

        if (authError || !user) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Parse request payload
        const { sessionId } = await req.json()

        // Validate input
        if (!sessionId) {
            return new Response(
                JSON.stringify({ error: 'Missing required field: sessionId' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Fetch full onboarding session data using the hierarchical structure
        const { data: sessionData, error: sessionError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .select('id, user_id, status, current_step')
            .eq('id', sessionId)
            .eq('user_id', user.id)
            .single()

        if (sessionError || !sessionData) {
            return new Response(
                JSON.stringify({ error: 'Session not found or access denied' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
            )
        }

        // Use the RPC function to get all session data for validation
        const { data: fullSessionData, error: rpcError } = await supabase.rpc('get_onboarding_session_data')

        if (rpcError || !fullSessionData) {
            console.error('Error fetching session data via RPC:', rpcError)
            return new Response(
                JSON.stringify({ error: 'Failed to fetch complete session data for validation' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
        }

        // Data Validation using the hierarchical structure
        const validationErrors: string[] = []

        // Validate Step 1: Business Profile
        const step1Data = fullSessionData.step1_businessProfile
        if (!step1Data?.businessRegistration) {
            validationErrors.push("Step 1: Business registration details are required")
        } else {
            const bizReg = step1Data.businessRegistration
            if (!bizReg.full_business_name?.trim()) {
                validationErrors.push("Step 1: Full Business Name is required")
            }
            if (!bizReg.abn?.trim()) {
                validationErrors.push("Step 1: ABN is required")
            }
            if (!bizReg.business_structure?.trim()) {
                validationErrors.push("Step 1: Business Structure is required")
            }
        }

        if (!step1Data?.contacts || step1Data.contacts.length === 0) {
            validationErrors.push("Step 1: At least one contact is required")
        } else {
            const mainContact = step1Data.contacts.find(c => c.contact_type === 'Main Contact')
            if (!mainContact) {
                validationErrors.push("Step 1: A 'Main Contact' is required")
            } else {
                if (!mainContact.name?.trim()) {
                    validationErrors.push("Step 1: Main Contact name is required")
                }
                if (!mainContact.email?.trim() || !/^[\S]+@[\S]+\.[\S]+$/.test(mainContact.email)) {
                    validationErrors.push("Step 1: Main Contact requires a valid email")
                }
            }
        }

        if (!step1Data?.addresses || step1Data.addresses.length === 0) {
            validationErrors.push("Step 1: At least one address is required")
        } else {
            const propertyAddress = step1Data.addresses.find(a => a.address_type === 'Property')
            if (!propertyAddress?.full_address_text?.trim()) {
                validationErrors.push("Step 1: Property address is required")
            }
        }

        // Optional: Validate key staff if business requires it
        if (step1Data?.keyStaff && step1Data.keyStaff.length > 0) {
            const invalidStaff = step1Data.keyStaff.filter(staff => !staff.staff_name?.trim())
            if (invalidStaff.length > 0) {
                validationErrors.push("Step 1: All key staff members must have a name")
            }
        }

        // Validate Step 2: Farm Operations
        const step2Data = fullSessionData.step2_farmOperations
        if (!step2Data?.activities || step2Data.activities.length === 0) {
            validationErrors.push("Step 2: At least one farming activity is required")
        }

        // Validate Step 3: Financial Systems
        const step3Data = fullSessionData.step3_financialSystems
        if (!step3Data?.bookkeeping) {
            validationErrors.push("Step 3: Bookkeeping information is required")
        } else {
            if (!step3Data.bookkeeping.current_software?.trim()) {
                validationErrors.push("Step 3: Current bookkeeping software is required")
            }
            if (!step3Data.bookkeeping.bas_lodgement_frequency?.trim()) {
                validationErrors.push("Step 3: BAS lodgement frequency is required")
            }
        }

        if (!step3Data?.payroll) {
            validationErrors.push("Step 3: Payroll information is required")
        }

        // Validate Step 4: Agreements
        const step4Data = fullSessionData.step4_agreements
        if (!step4Data?.dataMigration) {
            validationErrors.push("Step 4: Data migration information is required")
        } else {
            if (!step4Data.dataMigration.primary_cloud_storage?.trim()) {
                validationErrors.push("Step 4: Primary cloud storage selection is required")
            }
            if (!step4Data.dataMigration.filing_system_description?.trim()) {
                validationErrors.push("Step 4: Filing system description is required")
            }
        }

        if (!step4Data?.communicationPreferences) {
            validationErrors.push("Step 4: Communication preferences are required")
        } else {
            const commPrefs = step4Data.communicationPreferences
            if (!commPrefs.preferred_methods || commPrefs.preferred_methods.length === 0) {
                validationErrors.push("Step 4: At least one communication method must be selected")
            }
            if (!commPrefs.preferred_contact_times?.trim()) {
                validationErrors.push("Step 4: Preferred contact times are required")
            }
            if (!commPrefs.reporting_frequency?.trim()) {
                validationErrors.push("Step 4: Reporting frequency is required")
            }
        }

        if (!step4Data?.payments?.bank_account_details) {
            validationErrors.push("Step 4: Bank account details are required")
        }

        // Check for Service Agreement signature
        if (!step4Data?.agreements || step4Data.agreements.length === 0) {
            validationErrors.push("Step 4: Service Agreement must be signed")
        } else {
            const serviceAgreement = step4Data.agreements.find(a => a.agreement_type === 'Service Agreement')
            if (!serviceAgreement?.is_agreed || !serviceAgreement?.signature_storage_path) {
                validationErrors.push("Step 4: Service Agreement must be agreed to and signed")
            }
        }

        if (validationErrors.length > 0) {
            return new Response(
                JSON.stringify({
                    success: false,
                    error: 'Validation failed',
                    errors: validationErrors,
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Check if already completed
        if (sessionData.status === 'completed') {
            return new Response(
                JSON.stringify({
                    success: true,
                    message: 'Onboarding session was already completed'
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            )
        }

        // Update session status to completed
        const { error: updateError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .update({
                status: 'completed',
                current_step: 5
            })
            .eq('id', sessionId)

        if (updateError) {
            throw new Error(`Failed to update session status: ${updateError.message}`)
        }

        return new Response(
            JSON.stringify({
                success: true,
                message: 'Onboarding session submitted and completed successfully'
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        console.error('Error in finalize-onboarding-submission:', error)
        return new Response(
            JSON.stringify({
                success: false,
                error: `Error finalizing onboarding submission: ${error.message}`
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
    }
}) 