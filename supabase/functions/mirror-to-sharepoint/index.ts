import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface MirrorPayload {
  supabaseFilePath: string
  sessionId: string
  relatedToEntity: string
  relatedToId: string
  originalFileName: string
}

interface SharePointAuthResponse {
  access_token: string
  token_type: string
  expires_in: number
}

interface SharePointFolderResponse {
  value: {
    id: string
    name: string
    parentReference: {
      path: string
    }
  }[]
}

interface SharePointCreateFolderResponse {
  id: string
  name: string
  parentReference: {
    path: string
  }
}

/**
 * Authenticate with Microsoft Graph API using OAuth2 client credentials flow
 */
async function authenticateWithGraph(): Promise<string> {
  const clientId = Deno.env.get('SHAREPOINT_CLIENT_ID')
  const clientSecret = Deno.env.get('SHAREPOINT_CLIENT_SECRET')
  const tenantId = Deno.env.get('SHAREPOINT_TENANT_ID')

  if (!clientId || !clientSecret || !tenantId) {
    throw new Error('Missing SharePoint authentication environment variables')
  }

  const tokenUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`
  
  const body = new URLSearchParams({
    client_id: clientId,
    client_secret: clientSecret,
    scope: 'https://graph.microsoft.com/.default',
    grant_type: 'client_credentials'
  })

  const response = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: body
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`Graph authentication failed: ${response.status} ${response.statusText} - ${errorText}`)
  }

  const authData: SharePointAuthResponse = await response.json()
  return authData.access_token
}

/**
 * Create folder structure in SharePoint if it doesn't exist
 * Returns the ID of the final parent folder
 */
async function ensureSharePointFolder(
  accessToken: string,
  folderPath: string
): Promise<string> {
  const siteId = Deno.env.get('SHAREPOINT_SITE_ID')
  const driveId = Deno.env.get('SHAREPOINT_DRIVE_ID')
  const rootFolderName = Deno.env.get('SHAREPOINT_ROOT_FOLDER') || 'MirroredSupabaseFiles'

  if (!siteId || !driveId) {
    throw new Error('Missing SharePoint site or drive configuration')
  }

  // Parse the folder path (sessionId/relatedToEntity/relatedToId)
  const pathParts = folderPath.split('/').filter(part => part.length > 0)
  const fullPath = [rootFolderName, ...pathParts]
  
  let currentParentId = 'root'
  
  // Iterate through each folder level and ensure it exists
  for (let i = 0; i < fullPath.length; i++) {
    const folderName = fullPath[i]
    const currentPath = fullPath.slice(0, i + 1).join('/')
    
    // Check if folder exists
    const searchUrl = `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${driveId}/items/${currentParentId}/children?$filter=name eq '${encodeURIComponent(folderName)}' and folder ne null`
    
    const searchResponse = await fetch(searchUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })

    if (!searchResponse.ok) {
      throw new Error(`Failed to search for folder '${folderName}': ${searchResponse.status} ${searchResponse.statusText}`)
    }

    const searchData: SharePointFolderResponse = await searchResponse.json()
    
    if (searchData.value.length > 0) {
      // Folder exists, use its ID as the parent for the next iteration
      currentParentId = searchData.value[0].id
      console.log(`Found existing folder: ${folderName} (ID: ${currentParentId})`)
    } else {
      // Folder doesn't exist, create it
      const createUrl = `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${driveId}/items/${currentParentId}/children`
      
      const createBody = {
        name: folderName,
        folder: {},
        '@microsoft.graph.conflictBehavior': 'rename'
      }

      const createResponse = await fetch(createUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(createBody)
      })

      if (!createResponse.ok) {
        const errorText = await createResponse.text()
        throw new Error(`Failed to create folder '${folderName}': ${createResponse.status} ${createResponse.statusText} - ${errorText}`)
      }

      const createData: SharePointCreateFolderResponse = await createResponse.json()
      currentParentId = createData.id
      console.log(`Created folder: ${folderName} (ID: ${currentParentId})`)
    }
  }

  return currentParentId
}

/**
 * Upload file to SharePoint
 */
async function uploadToSharePoint(
  accessToken: string,
  parentFolderId: string,
  fileName: string,
  fileBuffer: ArrayBuffer
): Promise<string> {
  const siteId = Deno.env.get('SHAREPOINT_SITE_ID')
  const driveId = Deno.env.get('SHAREPOINT_DRIVE_ID')

  if (!siteId || !driveId) {
    throw new Error('Missing SharePoint site or drive configuration')
  }

  // Use simple upload for files smaller than 4MB
  const uploadUrl = `https://graph.microsoft.com/v1.0/sites/${siteId}/drives/${driveId}/items/${parentFolderId}:/${encodeURIComponent(fileName)}:/content`

  const uploadResponse = await fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/octet-stream'
    },
    body: fileBuffer
  })

  if (!uploadResponse.ok) {
    const errorText = await uploadResponse.text()
    throw new Error(`Failed to upload file to SharePoint: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`)
  }

  const uploadData = await uploadResponse.json()
  return uploadData.webUrl || `File uploaded successfully to SharePoint (ID: ${uploadData.id})`
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Supabase credentials from environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables')
      return new Response(
        JSON.stringify({ success: false, error: 'Server configuration error.' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Create Supabase admin client
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

    // Get authorization header from request to identify the user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing authorization header' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      console.error('Auth error:', authError)
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    // Parse request payload
    const payload: MirrorPayload = await req.json()
    const { supabaseFilePath, sessionId, relatedToEntity, relatedToId, originalFileName } = payload

    // Input validation
    if (!supabaseFilePath || !sessionId || !relatedToEntity || !relatedToId || !originalFileName) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required fields: supabaseFilePath, sessionId, relatedToEntity, relatedToId, or originalFileName' 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Verify the user owns the session
    const { data: sessionData, error: sessionFetchError } = await supabaseAdmin
      .schema('farms')
      .from('onboarding_sessions')
      .select('user_id')
      .eq('id', sessionId)
      .single()

    if (sessionFetchError) {
      console.error('Error fetching session:', sessionFetchError)
      return new Response(
        JSON.stringify({ success: false, error: 'Session not found or server error.' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
      )
    }

    if (!sessionData || sessionData.user_id !== user.id) {
      return new Response(
        JSON.stringify({ success: false, error: 'Forbidden: You do not own this session' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
      )
    }

    console.log(`Starting SharePoint mirroring for file: ${supabaseFilePath}`)

    // Step 1: Authenticate with Microsoft Graph
    console.log('Authenticating with Microsoft Graph...')
    const accessToken = await authenticateWithGraph()

    // Step 2: Download file from Supabase Storage
    console.log('Downloading file from Supabase Storage...')
    const { data: fileData, error: downloadError } = await supabaseAdmin.storage
      .from('farms')
      .download(supabaseFilePath)

    if (downloadError) {
      throw new Error(`Failed to download file from Supabase: ${downloadError.message}`)
    }

    if (!fileData) {
      throw new Error('Downloaded file data is null')
    }

    // Convert blob to ArrayBuffer
    const fileBuffer = await fileData.arrayBuffer()

    // Step 3: Create SharePoint folder structure
    console.log('Ensuring SharePoint folder structure...')
    const folderPath = `${sessionId}/${relatedToEntity}/${relatedToId}`
    const parentFolderId = await ensureSharePointFolder(accessToken, folderPath)

    // Step 4: Upload file to SharePoint
    console.log('Uploading file to SharePoint...')
    const sharepointUrl = await uploadToSharePoint(
      accessToken,
      parentFolderId,
      originalFileName,
      fileBuffer
    )

    console.log(`Successfully mirrored file to SharePoint: ${sharepointUrl}`)

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: 'File mirrored successfully',
        sharepointUrl: sharepointUrl
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in mirror-to-sharepoint:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: `SharePoint mirroring failed: ${error.message}` 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})