/*
--------------------------------------------------------------------------------
-- File: storage_bucket_setup_and_rls.sql
--
-- Version: 2.0 - Fixed storage bucket setup
--
-- Description:
-- Complete setup for Supabase Storage bucket with proper RLS policies
-- Note: Storage bucket must be created via Supabase Dashboard first
--------------------------------------------------------------------------------
*/

--==============================================================================
-- 1. HEL<PERSON>ER FUNCTION FOR PATH PARSING
--==============================================================================

CREATE OR REPLACE FUNCTION public.get_session_id_from_path(p_path_name TEXT)
RETURNS UUID AS $$
DECLARE
    v_session_id_str TEXT;
    v_path_parts TEXT[];
BEGIN
    IF p_path_name IS NULL OR p_path_name = '' THEN
        RETURN NULL;
    END IF;
    
    v_path_parts := string_to_array(p_path_name, '/');
    
    IF array_length(v_path_parts, 1) < 2 THEN
        RETURN NULL;
    END IF;
    
    v_session_id_str := v_path_parts[2];
    
    IF v_session_id_str IS NULL OR v_session_id_str = '' THEN
        RETURN NULL;
    END IF;
    
    RETURN v_session_id_str::UUID;
EXCEPTION
    WHEN invalid_text_representation THEN
        RETURN NULL;
    WHEN others THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.get_session_id_from_path(TEXT) IS 'Extracts the onboarding_session_id UUID from a storage object path for the farms bucket.';

--==============================================================================
-- 2. STORAGE BUCKET CREATION (Manual Step Required)
--==============================================================================

/*
IMPORTANT: Before applying RLS policies, you MUST create the storage bucket manually:

1. Go to Supabase Dashboard -> Storage
2. Click "Create a new bucket"
3. Bucket name: "farms"
4. Public bucket: UNCHECKED (keep it private)
5. Click "Create bucket"

File path structure will be:
onboarding_sessions/{session_id}/{table_name}/{record_id}/{file_name}

Example:
onboarding_sessions/123e4567-e89b-12d3-a456-************/licenses/456e7890-e89b-12d3-a456-************/water_license.pdf
*/

--==============================================================================
-- 3. RLS POLICIES FOR STORAGE.OBJECTS (Applied to farms bucket)
--==============================================================================

-- Policy: Allow authenticated users to view/download their own documents
DROP POLICY IF EXISTS "Allow users to view own files in farms bucket" ON storage.objects;
CREATE POLICY "Allow users to view own files in farms bucket"
ON storage.objects FOR SELECT
TO authenticated, service_role
USING (
    bucket_id = 'farms' AND
    (
        -- Allow service role for Edge Functions (they perform their own auth checks)
        auth.role() = 'service_role' OR
        -- Regular users can only access their own files
        (
            SELECT user_id
            FROM farms.onboarding_sessions
            WHERE id = public.get_session_id_from_path(name)
        ) = auth.uid()
    )
);

-- Policy: Allow authenticated users to upload documents into their own session folder                   
DROP POLICY IF EXISTS "Allow users to upload to own session in farms bucket" ON storage.objects;
CREATE POLICY "Allow users to upload to own session in farms bucket"
ON storage.objects FOR INSERT
TO authenticated, service_role
WITH CHECK (
    bucket_id = 'farms' AND
    (
        -- Allow service role for Edge Functions (they perform their own auth checks)
        auth.role() = 'service_role' OR
        -- Regular users can only upload to their own session folders
        (
            SELECT user_id
            FROM farms.onboarding_sessions
            WHERE id = public.get_session_id_from_path(name)
        ) = auth.uid()
    )
);

-- Policy: Allow users to update their own documents
DROP POLICY IF EXISTS "Allow users to update own files in farms bucket" ON storage.objects;
CREATE POLICY "Allow users to update own files in farms bucket"
ON storage.objects FOR UPDATE
TO authenticated, service_role
USING (
    bucket_id = 'farms' AND
    (
        -- Allow service role for Edge Functions (they perform their own auth checks)
        auth.role() = 'service_role' OR
        -- Regular users can only update their own files
        (
            SELECT user_id
            FROM farms.onboarding_sessions
            WHERE id = public.get_session_id_from_path(name)
        ) = auth.uid()
    )
)
WITH CHECK (
    bucket_id = 'farms' AND
    (
        -- Allow service role for Edge Functions (they perform their own auth checks)
        auth.role() = 'service_role' OR
        -- Regular users can only update their own files  
        (
            SELECT user_id
            FROM farms.onboarding_sessions
            WHERE id = public.get_session_id_from_path(name)
        ) = auth.uid()
    )
);

-- Policy: Allow users to delete their own documents
DROP POLICY IF EXISTS "Allow users to delete own files in farms bucket" ON storage.objects;
CREATE POLICY "Allow users to delete own files in farms bucket"
ON storage.objects FOR DELETE
TO authenticated, service_role
USING (
    bucket_id = 'farms' AND
    (
        -- Allow service role for Edge Functions (they perform their own auth checks)
        auth.role() = 'service_role' OR
        -- Regular users can only delete their own files
        (
            SELECT user_id
            FROM farms.onboarding_sessions
            WHERE id = public.get_session_id_from_path(name)
        ) = auth.uid()
    )
);

--==============================================================================
-- 4. VERIFY STORAGE BUCKET EXISTS (Optional Check)
--==============================================================================

-- This will help verify the bucket exists after manual creation
DO $$
BEGIN
    -- Check if the farms bucket exists
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'farms') THEN
        RAISE WARNING 'Storage bucket "farms" does not exist. Please create it manually via the Supabase Dashboard before using file upload features.';
        RAISE WARNING 'Instructions: Dashboard -> Storage -> Create bucket -> Name: "farms" -> Public: NO -> Create';
    ELSE
        RAISE NOTICE 'Storage bucket "farms" found. RLS policies applied successfully.';
    END IF;
END $$;

/*
--------------------------------------------------------------------------------
-- Manual Storage Setup Instructions:
--
-- 1. Navigate to Supabase Dashboard -> Storage
-- 2. Click "Create a new bucket"
-- 3. Enter bucket name: "farms"
-- 4. Leave "Public bucket" UNCHECKED (important for security)
-- 5. Click "Create bucket"
-- 6. The RLS policies above will then work correctly
--
-- File Path Structure:
-- onboarding_sessions/{session_id}/{table_name}/{record_id}/{file_name}
--
-- Example:
-- onboarding_sessions/123e4567-e89b-12d3-a456-************/licenses/456e7890-e89b-12d3-a456-************/water_license.pdf
--
-- Security:
-- - Only authenticated users can access files
-- - Users can only access files in their own onboarding session folders
-- - All operations (SELECT, INSERT, UPDATE, DELETE) are restricted by user ownership
--------------------------------------------------------------------------------
*/ 