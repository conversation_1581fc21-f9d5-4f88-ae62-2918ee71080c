/*
--------------------------------------------------------------------------------
-- File: farms_schema_RLS_policies.sql
--
-- Version: 3.1 - Updated for complete schema coverage
--
-- Description:
-- Complete and robust RLS policies for the hierarchical farms schema.
-- Includes all tables and handles the key_staff table addition.
--------------------------------------------------------------------------------
*/

--==============================================================================
-- 0. GRANT SCHEMA AND TABLE ACCESS TO SUPABASE ROLES
-- These grants are required before RLS can be applied
--==============================================================================

-- Grant schema usage to Supabase roles
GRANT USAGE ON SCHEMA farms TO authenticated;
GRANT USAGE ON SCHEMA farms TO anon;
GRANT USAGE ON SCHEMA farms TO service_role;

-- Grant table permissions to roles
GRANT ALL ON ALL TABLES IN SCHEMA farms TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA farms TO service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA farms TO anon;

-- Grant usage on custom types
GRANT USAGE ON TYPE farms.activity_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.agreement_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.contact_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.business_structure_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.license_type_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.asset_category_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.communication_method_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.reporting_frequency_enum TO authenticated, service_role;
GRANT USAGE ON TYPE farms.bas_frequency_enum TO authenticated, service_role;

--==============================================================================
-- 1. ROOT TABLE POLICIES: 'onboarding_sessions'
-- These policies are the foundation of the entire security model.
--==============================================================================

ALTER TABLE farms.onboarding_sessions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Onboarding Sessions: Users can manage their own sessions" ON farms.onboarding_sessions;
CREATE POLICY "Onboarding Sessions: Users can manage their own sessions"
ON farms.onboarding_sessions
FOR ALL
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "Onboarding Sessions: Prevent deletion of completed sessions" ON farms.onboarding_sessions;
CREATE POLICY "Onboarding Sessions: Prevent deletion of completed sessions"
ON farms.onboarding_sessions
FOR DELETE
USING (status <> 'completed' AND user_id = auth.uid());

--==============================================================================
-- 2. PARENT "STEP" TABLE POLICIES
-- These policies secure the four main parent tables. Access is granted if the
-- user owns the referenced 'onboarding_sessions' record.
--==============================================================================

-- Table: step_1_business_profile
ALTER TABLE farms.step_1_business_profile ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 1: Full access for session owners" ON farms.step_1_business_profile;
CREATE POLICY "Step 1: Full access for session owners" ON farms.step_1_business_profile FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Table: step_2_farm_operations
ALTER TABLE farms.step_2_farm_operations ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 2: Full access for session owners" ON farms.step_2_farm_operations;
CREATE POLICY "Step 2: Full access for session owners" ON farms.step_2_farm_operations FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Table: step_3_financial_systems
ALTER TABLE farms.step_3_financial_systems ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 3: Full access for session owners" ON farms.step_3_financial_systems;
CREATE POLICY "Step 3: Full access for session owners" ON farms.step_3_financial_systems FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));

-- Table: step_4_agreements
ALTER TABLE farms.step_4_agreements ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Step 4: Full access for session owners" ON farms.step_4_agreements;
CREATE POLICY "Step 4: Full access for session owners" ON farms.step_4_agreements FOR ALL
USING (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid()));


--==============================================================================
-- 3. CHILD TABLE POLICIES
-- These policies use a subquery to join up the hierarchy to verify ownership.
--==============================================================================

-- STEP 1 CHILDREN
ALTER TABLE farms.business_registration ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Business Registration: Full access for session owners" ON farms.business_registration;
CREATE POLICY "Business Registration: Full access for session owners" ON farms.business_registration FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));

ALTER TABLE farms.addresses ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Addresses: Full access for session owners" ON farms.addresses;
CREATE POLICY "Addresses: Full access for session owners" ON farms.addresses FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));

ALTER TABLE farms.contacts ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Contacts: Full access for session owners" ON farms.contacts;
CREATE POLICY "Contacts: Full access for session owners" ON farms.contacts FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));

-- Key Staff table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'farms' AND table_name = 'key_staff') THEN
        ALTER TABLE farms.key_staff ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Key Staff: Full access for session owners" ON farms.key_staff;
        CREATE POLICY "Key Staff: Full access for session owners" ON farms.key_staff FOR ALL 
        USING (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()))
        WITH CHECK (EXISTS (SELECT 1 FROM farms.step_1_business_profile s1 JOIN farms.onboarding_sessions os ON s1.onboarding_session_id = os.id WHERE s1.id = step_1_id AND os.user_id = auth.uid()));
    END IF;
END $$;

-- STEP 2 CHILDREN
ALTER TABLE farms.activities ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Activities: Full access for session owners" ON farms.activities;
CREATE POLICY "Activities: Full access for session owners" ON farms.activities FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.licenses ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Licenses: Full access for session owners" ON farms.licenses;
CREATE POLICY "Licenses: Full access for session owners" ON farms.licenses FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.suppliers ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Suppliers: Full access for session owners" ON farms.suppliers;
CREATE POLICY "Suppliers: Full access for session owners" ON farms.suppliers FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.contracts ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Contracts: Full access for session owners" ON farms.contracts;
CREATE POLICY "Contracts: Full access for session owners" ON farms.contracts FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

ALTER TABLE farms.chemical_usage ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Chemical Usage: Full access for session owners" ON farms.chemical_usage;
CREATE POLICY "Chemical Usage: Full access for session owners" ON farms.chemical_usage FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_2_farm_operations s2 JOIN farms.onboarding_sessions os ON s2.onboarding_session_id = os.id WHERE s2.id = step_2_id AND os.user_id = auth.uid()));

-- STEP 3 CHILDREN
ALTER TABLE farms.bookkeeping ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Bookkeeping: Full access for session owners" ON farms.bookkeeping;
CREATE POLICY "Bookkeeping: Full access for session owners" ON farms.bookkeeping FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));

ALTER TABLE farms.payroll ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Payroll: Full access for session owners" ON farms.payroll;
CREATE POLICY "Payroll: Full access for session owners" ON farms.payroll FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));

ALTER TABLE farms.assets ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Assets: Full access for session owners" ON farms.assets;
CREATE POLICY "Assets: Full access for session owners" ON farms.assets FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_3_financial_systems s3 JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id WHERE s3.id = step_3_id AND os.user_id = auth.uid()));

-- STEP 4 CHILDREN
ALTER TABLE farms.data_migration ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Data Migration: Full access for session owners" ON farms.data_migration;
CREATE POLICY "Data Migration: Full access for session owners" ON farms.data_migration FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.permissions ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Permissions: Full access for session owners" ON farms.permissions;
CREATE POLICY "Permissions: Full access for session owners" ON farms.permissions FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.agreements ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Agreements: Full access for session owners" ON farms.agreements;
CREATE POLICY "Agreements: Full access for session owners" ON farms.agreements FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.payments ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Payments: Full access for session owners" ON farms.payments;
CREATE POLICY "Payments: Full access for session owners" ON farms.payments FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));

ALTER TABLE farms.communication_preferences ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Comm Preferences: Full access for session owners" ON farms.communication_preferences;
CREATE POLICY "Comm Preferences: Full access for session owners" ON farms.communication_preferences FOR ALL 
USING (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()))
WITH CHECK (EXISTS (SELECT 1 FROM farms.step_4_agreements s4 JOIN farms.onboarding_sessions os ON s4.onboarding_session_id = os.id WHERE s4.id = step_4_id AND os.user_id = auth.uid()));


--==============================================================================
-- 4. CENTRAL DOCUMENTS TABLE POLICY
-- This policy remains simple as it's a direct child of onboarding_sessions.
--==============================================================================
ALTER TABLE farms.documents ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Documents: Full access for session owners" ON farms.documents;
CREATE POLICY "Documents: Full access for session owners" ON farms.documents FOR ALL
TO authenticated, service_role
USING (
    -- Allow service role for Edge Functions (they perform their own auth checks)
    auth.role() = 'service_role' OR
    -- Regular users can only access documents from their own sessions
    EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid())
)
WITH CHECK (
    -- Allow service role for Edge Functions (they perform their own auth checks)
    auth.role() = 'service_role' OR
    -- Regular users can only create/update documents in their own sessions
    EXISTS (SELECT 1 FROM farms.onboarding_sessions WHERE id = onboarding_session_id AND user_id = auth.uid())
);

/*
--------------------------------------------------------------------------------
-- END OF ENHANCED RLS POLICIES V3.1
-- 
-- Key improvements:
-- 1. Added WITH CHECK clauses to all policies for complete INSERT/UPDATE security
-- 2. Added support for key_staff table with conditional creation check
-- 3. Added grants for custom enum types
-- 4. Enhanced policy names for clarity
-- 5. Comprehensive coverage of all schema tables
--------------------------------------------------------------------------------
*/
