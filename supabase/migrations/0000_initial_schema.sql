/*
================================================================================
-- File: farms_schema_database_setup.sql
--
-- Version: 3.0 - Complete and Enhanced Schema
--
-- Description:
-- This script defines the complete, production-ready, and hierarchically
-- structured database schema for the NewTerra onboarding wizard. This enhanced
-- schema addresses all business requirements and organizes data under four
-- parent "step" tables, directly mirroring the frontend user journey.
--
-- Architectural Principles:
-- 1. 4-Step Hierarchical Structure: All data organized under four parent tables
--    corresponding to the major steps in the onboarding flow
-- 2. Session-Centric Root: All data cascades from 'onboarding_sessions'
-- 3. Complete Requirements Coverage: Every data attribute from business
--    requirements is properly captured and validated
-- 4. Data Validation at Core: CHECK constraints enforce formats and business rules
-- 5. Explicit Encryption Requirement: Sensitive data stored as BYTEA with
--    encryption via Edge Functions
-- 6. Performance Optimization: Strategic indexing on foreign keys and query paths
================================================================================
*/

-- Clean slate approach for refactoring
DROP SCHEMA IF EXISTS farms CASCADE;

--==============================================================================
-- 0. SCHEMA SETUP AND FOUNDATIONAL TYPES
--==============================================================================

CREATE SCHEMA IF NOT EXISTS farms;
COMMENT ON SCHEMA farms IS 'Complete schema for NewTerra farm onboarding and management process covering all business requirements.';

-- Business-specific enums with comprehensive coverage
CREATE TYPE farms.activity_type_enum AS ENUM (
    'Cropping', 'Livestock', 'Mixed Farming', 'Horticulture', 
    'Dairy', 'Aquaculture', 'Forestry', 'Other'
);

CREATE TYPE farms.agreement_type_enum AS ENUM (
    'Service Agreement', 'Privacy Policy', 'Direct Debit', 'Terms and Conditions'
);

-- Enhanced contact type enum to cover all business requirements
CREATE TYPE farms.contact_type_enum AS ENUM (
    'Main Contact', 'Accountant', 'BAS Agent', 'Key Staff', 
    'Admin', 'Accounts', 'Personal'
);

-- Australian business structure types for validation
CREATE TYPE farms.business_structure_enum AS ENUM (
    'Sole Trader', 'Company', 'Partnership', 'Trust', 
    'Cooperative', 'Other'
);

-- License types with specific water license support
CREATE TYPE farms.license_type_enum AS ENUM (
    'Chemical Permit', 'Machinery License', 'Food Safety Cert', 
    'Water License', 'Heavy Vehicle License', 'Quad Bike License', 'Other'
);

-- Asset categories for proper differentiation
CREATE TYPE farms.asset_category_enum AS ENUM (
    'Vehicle', 'Equipment', 'Insurance'
);

-- Communication preferences for structured selection
CREATE TYPE farms.communication_method_enum AS ENUM (
    'Email', 'Phone', 'SMS', 'WhatsApp'
);

CREATE TYPE farms.reporting_frequency_enum AS ENUM (
    'Weekly', 'Fortnightly', 'Monthly'
);

CREATE TYPE farms.bas_frequency_enum AS ENUM (
    'Monthly', 'Quarterly', 'Annually'
);

-- Utility function for automatic timestamp updates
CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.trigger_set_timestamp IS 'Automatically updates the updated_at timestamp on row modification.';

--==============================================================================
-- 1. ROOT SESSION TABLE
--==============================================================================

CREATE TABLE farms.onboarding_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'in_progress', 
    current_step INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_status CHECK (status IN ('in_progress', 'completed', 'archived')),
    CONSTRAINT chk_current_step CHECK (current_step BETWEEN 1 AND 5)
);

COMMENT ON TABLE farms.onboarding_sessions IS 'Root table for user onboarding journey. All data cascades from here via user_id for RLS security.';
COMMENT ON COLUMN farms.onboarding_sessions.status IS 'Tracks onboarding progress: in_progress, completed, archived';
COMMENT ON COLUMN farms.onboarding_sessions.current_step IS 'Current step (1-4) for UI state management, 5 = completed';

CREATE INDEX idx_onboarding_sessions_user_id ON farms.onboarding_sessions(user_id);
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.onboarding_sessions FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

--==============================================================================
-- 2. PARENT STEP TABLES
-- These represent the four major sections of the onboarding wizard
--==============================================================================

CREATE TABLE farms.step_1_business_profile (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_1_business_profile IS 'Parent table for Step 1: Business registration, contacts, and addresses';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_1_business_profile FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_2_farm_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_2_farm_operations IS 'Parent table for Step 2: Farm activities, licenses, suppliers, and compliance';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_2_farm_operations FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_3_financial_systems (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_3_financial_systems IS 'Parent table for Step 3: Bookkeeping, payroll, and asset management';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_3_financial_systems FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_4_agreements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_4_agreements IS 'Parent table for Step 4: Legal agreements, data migration, and communication preferences';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_4_agreements FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

--==============================================================================
-- 3. STEP 1 CHILD TABLES: BUSINESS PROFILE & CONTACT DETAILS
--==============================================================================

CREATE TABLE farms.business_registration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL UNIQUE REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    -- Core business identification (all required by Australian law)
    full_business_name TEXT NOT NULL,
    trading_name TEXT,
    abn TEXT NOT NULL,
    acn TEXT,
    is_gst_registered BOOLEAN NOT NULL DEFAULT false,
    business_structure farms.business_structure_enum NOT NULL,
    
    -- Primary business contact (separate from individual contacts)
    primary_business_phone TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Australian format validation
    CONSTRAINT chk_abn_format CHECK (abn ~ '^[0-9]{2}\s[0-9]{3}\s[0-9]{3}\s[0-9]{3}$'),
    CONSTRAINT chk_acn_format CHECK (acn IS NULL OR acn ~ '^[0-9]{3}\s[0-9]{3}\s[0-9]{3}$'),
    CONSTRAINT chk_primary_phone_format CHECK (primary_business_phone IS NULL OR primary_business_phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.business_registration IS 'Core legal business information as required by Australian business registration';
COMMENT ON COLUMN farms.business_registration.abn IS 'Australian Business Number in format: XX XXX XXX XXX';
COMMENT ON COLUMN farms.business_registration.acn IS 'Australian Company Number in format: XXX XXX XXX (companies only)';
COMMENT ON COLUMN farms.business_registration.primary_business_phone IS 'Main business phone in international format +61XXXXXXXXX';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.business_registration FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    address_type TEXT NOT NULL,
    full_address_text TEXT NOT NULL,
    
    -- Structured address components for search/validation
    street TEXT,
    locality TEXT, -- Suburb or city
    state TEXT,
    postcode TEXT,
    country TEXT DEFAULT 'Australia',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_address_type CHECK (address_type IN ('Postal', 'Property'))
);

COMMENT ON TABLE farms.addresses IS 'Business postal and property addresses with structured components';
COMMENT ON COLUMN farms.addresses.address_type IS 'Postal (for mail) or Property (where farm operations occur)';
COMMENT ON COLUMN farms.addresses.full_address_text IS 'Complete address as single string for display purposes';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.addresses FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_addresses_step_1_id ON farms.addresses(step_1_id);

CREATE TABLE farms.contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    contact_type farms.contact_type_enum NOT NULL,
    name TEXT NOT NULL,
    title_or_firm TEXT,
    email TEXT,
    phone TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'),
    CONSTRAINT chk_phone_format CHECK (phone IS NULL OR phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.contacts IS 'All human contacts including main contact, accountant, BAS agent, and key staff';
COMMENT ON COLUMN farms.contacts.contact_type IS 'Categorizes contact role - Main Contact is required, others optional';
COMMENT ON COLUMN farms.contacts.title_or_firm IS 'Job title for individuals or firm name for external professionals';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.contacts FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_contacts_step_1_id ON farms.contacts(step_1_id);
CREATE INDEX idx_contacts_type ON farms.contacts(contact_type);

-- Add this after the contacts table creation
CREATE TABLE farms.key_staff (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    staff_name TEXT NOT NULL,
    role_or_title TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_staff_email_format CHECK (contact_email IS NULL OR contact_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'),
    CONSTRAINT chk_staff_phone_format CHECK (contact_phone IS NULL OR contact_phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.key_staff IS 'Key farm staff members and their contact details';
COMMENT ON COLUMN farms.key_staff.role_or_title IS 'Job title or role description (e.g., Farm Manager, Head Groundskeeper)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.key_staff FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_key_staff_step_1_id ON farms.key_staff(step_1_id);

--==============================================================================
-- 4. STEP 2 CHILD TABLES: FARM OPERATIONS & COMPLIANCE
--==============================================================================

CREATE TABLE farms.activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    activity_type farms.activity_type_enum NOT NULL,
    
    -- Crop-specific details
    crop_type TEXT,
    crop_varieties TEXT[], -- Array to store multiple varieties (e.g., Valencia, Navel oranges)
    
    -- Livestock-specific details  
    livestock_type TEXT,
    approximate_numbers INTEGER,
    
    -- Additional activity details
    activity_description TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_positive_numbers CHECK (approximate_numbers IS NULL OR approximate_numbers > 0)
);

COMMENT ON TABLE farms.activities IS 'Detailed farming activities including crops, livestock, and other operations';
COMMENT ON COLUMN farms.activities.crop_varieties IS 'Array of crop varieties for detailed tracking (e.g., [Valencia, Navel] for oranges)';
COMMENT ON COLUMN farms.activities.approximate_numbers IS 'For livestock - approximate head count';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.activities FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_activities_step_2_id ON farms.activities(step_2_id);

CREATE TABLE farms.licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    license_type farms.license_type_enum NOT NULL,
    license_number TEXT,
    issuing_authority TEXT,
    issue_date DATE,
    expiry_date DATE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_expiry_date CHECK (expiry_date IS NULL OR issue_date IS NULL OR expiry_date > issue_date)
);

COMMENT ON TABLE farms.licenses IS 'All operational licenses including water licenses, chemical permits, and machinery licenses';
COMMENT ON COLUMN farms.licenses.license_type IS 'Specific license categories with Water License as distinct type';
COMMENT ON COLUMN farms.licenses.issuing_authority IS 'Government body or agency that issued the license';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.licenses FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_licenses_step_2_id ON farms.licenses(step_2_id);
CREATE INDEX idx_licenses_expiry ON farms.licenses(expiry_date) WHERE expiry_date IS NOT NULL;

CREATE TABLE farms.suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    supplier_name TEXT NOT NULL,
    contact_person TEXT,
    contact_details TEXT,
    services_provided TEXT NOT NULL,
    supplier_category TEXT, -- e.g., 'Chemical Supplier', 'Equipment Rental', 'Feed Supplier'
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE farms.suppliers IS 'All farm operation suppliers with categorization for better organization';
COMMENT ON COLUMN farms.suppliers.supplier_category IS 'Groups suppliers by type of service for reporting and management';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.suppliers FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_suppliers_step_2_id ON farms.suppliers(step_2_id);
CREATE INDEX idx_suppliers_category ON farms.suppliers(supplier_category);

CREATE TABLE farms.contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES farms.suppliers(id) ON DELETE SET NULL,
    
    contract_description TEXT NOT NULL,
    contract_type TEXT, -- e.g., 'Land Lease', 'Equipment Lease', 'Agistment', 'Sales Contract'
    contract_value DECIMAL(10,2),
    start_date DATE,
    expiry_date DATE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_contract_dates CHECK (expiry_date IS NULL OR start_date IS NULL OR expiry_date > start_date),
    CONSTRAINT chk_contract_value CHECK (contract_value IS NULL OR contract_value >= 0)
);

COMMENT ON TABLE farms.contracts IS 'Key contracts with expiry tracking for proactive management';
COMMENT ON COLUMN farms.contracts.contract_type IS 'Categorizes contract for expiry monitoring (Land/Equipment leases, Agistment, Sales)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.contracts FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_contracts_step_2_id ON farms.contracts(step_2_id);
CREATE INDEX idx_contracts_supplier_id ON farms.contracts(supplier_id);
CREATE INDEX idx_contracts_expiry ON farms.contracts(expiry_date) WHERE expiry_date IS NOT NULL;

CREATE TABLE farms.chemical_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    product_name TEXT NOT NULL,
    manufacturer TEXT,
    usage_purpose TEXT NOT NULL, -- e.g., 'Pesticide', 'Herbicide', 'Fertiliser', 'Fungicide'
    application_rate TEXT, -- e.g., '10L/ha', '5kg per 100L water'
    application_method TEXT, -- e.g., 'Spray', 'Granular', 'Injection'
    last_application_date DATE,
    withholding_period_days INTEGER,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_positive_withholding CHECK (withholding_period_days IS NULL OR withholding_period_days >= 0)
);

COMMENT ON TABLE farms.chemical_usage IS 'Detailed chemical and fertilizer usage for compliance and safety tracking';
COMMENT ON COLUMN farms.chemical_usage.withholding_period_days IS 'Days between application and safe harvest/consumption';
COMMENT ON COLUMN farms.chemical_usage.application_rate IS 'Rate of application (e.g., L/ha, kg/100L)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.chemical_usage FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_chemical_usage_step_2_id ON farms.chemical_usage(step_2_id);

--==============================================================================
-- 5. STEP 3 CHILD TABLES: FINANCIAL SYSTEMS & ASSET MANAGEMENT
--==============================================================================

CREATE TABLE farms.bookkeeping (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,
    
    current_software TEXT NOT NULL,
    software_version TEXT,
    access_credentials BYTEA, -- Encrypted storage
    has_bank_feeds_enabled BOOLEAN NOT NULL DEFAULT false,
    bas_lodgement_frequency farms.bas_frequency_enum NOT NULL,
    
    -- Accountant integration details
    accountant_has_access BOOLEAN NOT NULL DEFAULT false,
    accountant_access_level TEXT, -- e.g., 'View Only', 'Full Access', 'Advisor'
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE farms.bookkeeping IS 'Financial system details with encrypted credential storage';
COMMENT ON COLUMN farms.bookkeeping.access_credentials IS 'MUST be encrypted via Edge Function before storage';
COMMENT ON COLUMN farms.bookkeeping.bas_lodgement_frequency IS 'Business Activity Statement frequency for compliance tracking';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.bookkeeping FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.payroll (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,
    
    is_payroll_processing_needed BOOLEAN NOT NULL,
    employee_count INTEGER,
    current_payroll_software TEXT,
    payroll_frequency TEXT, -- e.g., 'Weekly', 'Fortnightly', 'Monthly'
    
    -- Access management
    is_access_to_software_granted BOOLEAN NOT NULL DEFAULT false,
    encrypted_access_credentials BYTEA,
    
    -- Compliance tracking
    superannuation_fund TEXT,
    workers_compensation_policy TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_employee_count CHECK (employee_count IS NULL OR employee_count >= 0),
    CONSTRAINT chk_payroll_logic CHECK (
        (is_payroll_processing_needed = false) OR 
        (is_payroll_processing_needed = true AND employee_count > 0)
    )
);

COMMENT ON TABLE farms.payroll IS 'Comprehensive payroll processing needs and system access details';
COMMENT ON COLUMN farms.payroll.encrypted_access_credentials IS 'Encrypted payroll system credentials';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.payroll FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,
    
    asset_category farms.asset_category_enum NOT NULL,
    asset_type TEXT NOT NULL,
    
    -- Common fields
    make_or_provider TEXT,
    registration_or_policy_number TEXT,
    renewal_date DATE,
    
    -- Vehicle/Equipment specific
    model_year INTEGER,
    serial_number TEXT,
    purchase_date DATE,
    purchase_price DECIMAL(10,2),
    
    -- Insurance specific  
    policy_type TEXT, -- e.g., 'Public Liability', 'Vehicle', 'Workers Comp', 'Crop Insurance'
    coverage_amount DECIMAL(12,2),
    excess_amount DECIMAL(8,2),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Conditional validation based on asset category
    CONSTRAINT chk_vehicle_equipment_fields CHECK (
        asset_category = 'Insurance' OR 
        (asset_category IN ('Vehicle', 'Equipment') AND registration_or_policy_number IS NOT NULL)
    ),
    CONSTRAINT chk_insurance_fields CHECK (
        asset_category != 'Insurance' OR 
        (asset_category = 'Insurance' AND policy_type IS NOT NULL AND renewal_date IS NOT NULL)
    ),
    CONSTRAINT chk_positive_amounts CHECK (
        (purchase_price IS NULL OR purchase_price >= 0) AND
        (coverage_amount IS NULL OR coverage_amount >= 0) AND
        (excess_amount IS NULL OR excess_amount >= 0)
    )
);

COMMENT ON TABLE farms.assets IS 'Unified asset registry for vehicles, equipment, and insurance with category-specific validation';
COMMENT ON COLUMN farms.assets.policy_type IS 'For insurance: Public Liability, Vehicle, Workers Comp, Crop/Livestock insurance';
COMMENT ON COLUMN farms.assets.registration_or_policy_number IS 'Vehicle registration or insurance policy number';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.assets FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_assets_step_3_id ON farms.assets(step_3_id);
CREATE INDEX idx_assets_category ON farms.assets(asset_category);
CREATE INDEX idx_assets_renewal ON farms.assets(renewal_date) WHERE renewal_date IS NOT NULL;

--==============================================================================
-- 6. STEP 4 CHILD TABLES: DATA MIGRATION & SERVICE AGREEMENTS
--==============================================================================

CREATE TABLE farms.data_migration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL UNIQUE REFERENCES farms.step_4_agreements(id) ON DELETE CASCADE,
    
    primary_cloud_storage TEXT NOT NULL,
    secondary_cloud_storage TEXT,
    filing_system_description TEXT NOT NULL,
    
    -- Document organization details
    folder_structure_description TEXT,
    document_categories TEXT[], -- e.g., ['Financial', 'Legal', 'Operational', 'Compliance']
    estimated_document_count INTEGER,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_cloud_storage CHECK (
        primary_cloud_storage IN ('Google Drive', 'Dropbox', 'OneDrive', 'SharePoint', 'Other', 'None')
    )
);

COMMENT ON TABLE farms.data_migration IS 'Current data storage and organization for migration planning';
COMMENT ON COLUMN farms.data_migration.document_categories IS 'Array of document types for migration prioritization';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.data_migration FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL REFERENCES farms.step_4_agreements(id) ON DELETE CASCADE,
    
    data_type TEXT NOT NULL,
    access_level TEXT NOT NULL,
    permission_granted BOOLEAN NOT NULL DEFAULT false,
    permission_granted_date TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT uq_permission_per_step UNIQUE (step_4_id, data_type),
    CONSTRAINT chk_data_type CHECK (data_type IN ('Bookkeeping', 'Cloud Storage', 'Bank Feeds', 'Payroll', 'Documents')),
    CONSTRAINT chk_access_level CHECK (access_level IN ('View', 'Edit', 'Full', 'None'))
);

COMMENT ON TABLE farms.permissions IS 'Granular permission matrix for data access authorization';
COMMENT ON COLUMN farms.permissions.access_level IS 'Level of access: View (read-only), Edit (modify), Full (admin), None (no access)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.permissions FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_permissions_step_4_id ON farms.permissions(step_4_id);

CREATE TABLE farms.agreements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL REFERENCES farms.step_4_agreements(id) ON DELETE CASCADE,
    
    agreement_type farms.agreement_type_enum NOT NULL,
    is_agreed BOOLEAN NOT NULL DEFAULT false,
    signature_data TEXT, -- Base64 signature data (legacy support)
    signature_storage_path TEXT, -- Path to signature file in storage
    agreed_at TIMESTAMPTZ,
    agreement_version TEXT DEFAULT '1.0',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT uq_agreement_type_per_step UNIQUE (step_4_id, agreement_type),
    CONSTRAINT chk_agreement_completion CHECK (
        (is_agreed = false) OR 
        (is_agreed = true AND agreed_at IS NOT NULL AND (signature_data IS NOT NULL OR signature_storage_path IS NOT NULL))
    )
);

COMMENT ON TABLE farms.agreements IS 'Legal agreements and consent tracking with digital signature support';
COMMENT ON COLUMN farms.agreements.signature_storage_path IS 'Preferred: Path to signature file in secure storage';
COMMENT ON COLUMN farms.agreements.signature_data IS 'Legacy: Base64 signature data (use storage_path instead)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.agreements FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_agreements_step_4_id ON farms.agreements(step_4_id);

CREATE TABLE farms.payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL UNIQUE REFERENCES farms.step_4_agreements(id) ON DELETE CASCADE,
    
    bank_account_details BYTEA NOT NULL, -- Encrypted bank details
    payment_method TEXT DEFAULT 'Direct Debit',
    payment_frequency TEXT DEFAULT 'Monthly',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_payment_method CHECK (payment_method IN ('Direct Debit', 'Credit Card', 'Bank Transfer'))
);

COMMENT ON TABLE farms.payments IS 'Encrypted payment method and bank account information';
COMMENT ON COLUMN farms.payments.bank_account_details IS 'MUST be encrypted bank account details via Edge Function';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.payments FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.communication_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL UNIQUE REFERENCES farms.step_4_agreements(id) ON DELETE CASCADE,
    
    preferred_methods farms.communication_method_enum[] NOT NULL,
    preferred_contact_times TEXT NOT NULL,
    reporting_frequency farms.reporting_frequency_enum NOT NULL,
    
    -- Detailed communication preferences
    emergency_contact_method farms.communication_method_enum,
    business_hours_only BOOLEAN NOT NULL DEFAULT true,
    timezone TEXT DEFAULT 'Australia/Sydney',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_preferred_methods_not_empty CHECK (array_length(preferred_methods, 1) > 0)
);

COMMENT ON TABLE farms.communication_preferences IS 'Comprehensive communication preferences for service delivery';
COMMENT ON COLUMN farms.communication_preferences.preferred_methods IS 'Array of communication methods in order of preference';
COMMENT ON COLUMN farms.communication_preferences.emergency_contact_method IS 'Preferred method for urgent communications';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.communication_preferences FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

--==============================================================================
-- 7. CENTRAL DOCUMENTS TABLE
--==============================================================================

CREATE TABLE farms.documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    
    document_name TEXT NOT NULL,
    document_category TEXT NOT NULL, -- e.g., 'License', 'Insurance', 'Contract', 'Financial'
    related_to_entity TEXT NOT NULL, -- Table name (e.g., 'licenses', 'assets', 'contracts')
    related_to_id UUID NOT NULL,
    
    storage_bucket_path TEXT NOT NULL UNIQUE,
    file_size_bytes INTEGER,
    file_type TEXT,
    
    uploaded_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_file_size CHECK (file_size_bytes IS NULL OR file_size_bytes > 0),
    CONSTRAINT chk_document_category CHECK (document_category IN ('License', 'Insurance', 'Contract', 'Financial', 'Legal', 'Operational', 'Compliance', 'Other'))
);

COMMENT ON TABLE farms.documents IS 'Central registry for all uploaded documents with relationship tracking';
COMMENT ON COLUMN farms.documents.related_to_entity IS 'Name of the table this document relates to (e.g., licenses, assets)';
COMMENT ON COLUMN farms.documents.related_to_id IS 'ID of the specific record this document supports';

CREATE INDEX idx_documents_session_id ON farms.documents(onboarding_session_id);
CREATE INDEX idx_documents_related_entity ON farms.documents(related_to_entity, related_to_id);
CREATE INDEX idx_documents_category ON farms.documents(document_category);

/*
================================================================================
-- END OF COMPLETE SCHEMA DEFINITION
--
-- Summary of Enhancements:
-- 1. Added comprehensive enum types for data validation
-- 2. Included all missing data attributes from requirements
-- 3. Enhanced contact management with proper categorization
-- 4. Separated asset types with conditional validation
-- 5. Added detailed chemical and license tracking
-- 6. Comprehensive communication preferences
-- 7. Enhanced documentation and comments throughout
-- 8. Strategic indexing for performance
-- 9. Proper constraint validation for business rules
-- 10. Complete coverage of all business requirements
================================================================================
*/
