-- ============================================================================
-- SharePoint Synchronization Tables
-- ============================================================================
-- This migration creates tables to manage synchronization between Supabase 
-- Storage and SharePoint Document Libraries via REST API integration.

-- Enable RLS on both tables
ALTER TABLE IF EXISTS public.synced_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.sharepoint_sync_map ENABLE ROW LEVEL SECURITY;

-- Drop existing tables if they exist (for clean migration)
DROP TABLE IF EXISTS public.sharepoint_sync_map CASCADE;
DROP TABLE IF EXISTS public.synced_files CASCADE;

-- ============================================================================
-- Synced Files Table
-- ============================================================================
-- Tracks all file operations that need to be synchronized with SharePoint
CREATE TABLE public.synced_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supabase_object_id UUID NOT NULL, -- References storage.objects.id
    supabase_file_path TEXT NOT NULL, -- Full path including file name (e.g., "farms/session_id/document.pdf")
    supabase_bucket_id TEXT NOT NULL DEFAULT 'farms', -- Storage bucket name
    operation_type TEXT NOT NULL CHECK (operation_type IN ('CREATE', 'UPDATE', 'DELETE')),
    
    -- SharePoint-specific metadata
    metadata JSONB DEFAULT '{}', -- Custom metadata for SharePoint list item fields
    file_size BIGINT, -- File size in bytes
    content_type TEXT, -- MIME type
    
    -- Session and user context for RLS
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID, -- Links to onboarding_sessions if applicable
    
    -- Processing status
    status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'RETRYING')),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    next_retry_at TIMESTAMPTZ,
    
    -- Error handling
    error_message TEXT,
    error_details JSONB,
    
    -- Audit trail
    created_by UUID REFERENCES auth.users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- SharePoint Sync Map Table
-- ============================================================================
-- Maps Supabase storage objects to their SharePoint counterparts
CREATE TABLE public.sharepoint_sync_map (
    supabase_object_id UUID PRIMARY KEY,
    
    -- SharePoint identifiers
    sharepoint_site_id TEXT NOT NULL,
    sharepoint_drive_id TEXT NOT NULL,
    sharepoint_list_id TEXT, -- For document library list items
    sharepoint_item_id TEXT, -- SharePoint list item ID
    sharepoint_file_id TEXT, -- SharePoint file/drive item ID
    sharepoint_file_name TEXT NOT NULL,
    sharepoint_relative_url TEXT NOT NULL, -- Server-relative URL
    sharepoint_web_url TEXT, -- Full SharePoint web URL
    
    -- Supabase context
    supabase_file_path TEXT NOT NULL,
    supabase_bucket_id TEXT NOT NULL DEFAULT 'farms',
    
    -- User and session context for RLS
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID, -- Links to onboarding_sessions if applicable
    
    -- Sync status tracking
    sync_status TEXT DEFAULT 'SYNCED' CHECK (sync_status IN ('SYNCED', 'ERROR', 'PENDING', 'OUT_OF_SYNC')),
    last_synced_at TIMESTAMPTZ DEFAULT NOW(),
    sync_error_message TEXT,
    
    -- SharePoint metadata
    sharepoint_etag TEXT, -- For conflict detection
    sharepoint_version TEXT,
    sharepoint_metadata JSONB DEFAULT '{}',
    
    -- Audit trail
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id)
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

-- Synced Files Indexes
CREATE INDEX idx_synced_files_status ON public.synced_files (status) WHERE status != 'COMPLETED';
CREATE INDEX idx_synced_files_user_id ON public.synced_files (user_id);
CREATE INDEX idx_synced_files_session_id ON public.synced_files (session_id) WHERE session_id IS NOT NULL;
CREATE INDEX idx_synced_files_created_at ON public.synced_files (created_at);
CREATE INDEX idx_synced_files_next_retry ON public.synced_files (next_retry_at) WHERE next_retry_at IS NOT NULL;
CREATE INDEX idx_synced_files_object_id ON public.synced_files (supabase_object_id);

-- Prevent duplicate sync entries for the same operation
CREATE UNIQUE INDEX idx_synced_files_unique_operation 
ON public.synced_files (supabase_object_id, operation_type, created_at)
WHERE status IN ('PENDING', 'PROCESSING');

-- SharePoint Sync Map Indexes
CREATE INDEX idx_sharepoint_sync_user_id ON public.sharepoint_sync_map (user_id);
CREATE INDEX idx_sharepoint_sync_session_id ON public.sharepoint_sync_map (session_id) WHERE session_id IS NOT NULL;
CREATE INDEX idx_sharepoint_sync_status ON public.sharepoint_sync_map (sync_status) WHERE sync_status != 'SYNCED';
CREATE INDEX idx_sharepoint_sync_file_path ON public.sharepoint_sync_map (supabase_file_path);
CREATE UNIQUE INDEX idx_sharepoint_sync_sharepoint_item ON public.sharepoint_sync_map (sharepoint_site_id, sharepoint_item_id) WHERE sharepoint_item_id IS NOT NULL;

-- ============================================================================
-- Row Level Security Policies
-- ============================================================================

-- Synced Files RLS Policies
-- Users can only see their own sync records
CREATE POLICY "Users can view their own sync records" ON public.synced_files
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() IN (
            SELECT user_id FROM farms.onboarding_sessions 
            WHERE id = synced_files.session_id
        )
    );

-- Users can insert sync records for their own files
CREATE POLICY "Users can create sync records for their files" ON public.synced_files
    FOR INSERT WITH CHECK (
        auth.uid() = user_id OR
        auth.uid() IN (
            SELECT user_id FROM farms.onboarding_sessions 
            WHERE id = session_id
        )
    );

-- Only service role can update sync records (for Edge Functions)
CREATE POLICY "Service role can update sync records" ON public.synced_files
    FOR UPDATE USING (
        auth.jwt() ->> 'role' = 'service_role' OR
        auth.uid() = user_id
    );

-- Service role can delete old/completed sync records
CREATE POLICY "Service role can delete sync records" ON public.synced_files
    FOR DELETE USING (
        auth.jwt() ->> 'role' = 'service_role' OR
        (auth.uid() = user_id AND status = 'COMPLETED')
    );

-- SharePoint Sync Map RLS Policies
-- Users can view their own SharePoint mappings
CREATE POLICY "Users can view their own SharePoint mappings" ON public.sharepoint_sync_map
    FOR SELECT USING (
        auth.uid() = user_id OR
        auth.uid() IN (
            SELECT user_id FROM farms.onboarding_sessions 
            WHERE id = sharepoint_sync_map.session_id
        )
    );

-- Users can create SharePoint mappings for their files
CREATE POLICY "Users can create SharePoint mappings" ON public.sharepoint_sync_map
    FOR INSERT WITH CHECK (
        auth.uid() = user_id OR
        auth.uid() IN (
            SELECT user_id FROM farms.onboarding_sessions 
            WHERE id = session_id
        )
    );

-- Service role and file owners can update mappings
CREATE POLICY "Authorized users can update SharePoint mappings" ON public.sharepoint_sync_map
    FOR UPDATE USING (
        auth.jwt() ->> 'role' = 'service_role' OR
        auth.uid() = user_id
    );

-- Service role and file owners can delete mappings
CREATE POLICY "Authorized users can delete SharePoint mappings" ON public.sharepoint_sync_map
    FOR DELETE USING (
        auth.jwt() ->> 'role' = 'service_role' OR
        auth.uid() = user_id
    );

-- ============================================================================
-- Helper Functions
-- ============================================================================

-- Function to automatically set updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER update_synced_files_updated_at
    BEFORE UPDATE ON public.synced_files
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_sharepoint_sync_map_updated_at
    BEFORE UPDATE ON public.sharepoint_sync_map
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================================================
-- Storage Triggers (for automatic sync queue population)
-- ============================================================================

-- Function to queue files for SharePoint sync when uploaded to storage
CREATE OR REPLACE FUNCTION public.queue_file_for_sharepoint_sync()
RETURNS TRIGGER AS $$
BEGIN
    -- Only queue files from the 'farms' bucket
    IF NEW.bucket_id = 'farms' THEN
        INSERT INTO public.synced_files (
            supabase_object_id,
            supabase_file_path,
            supabase_bucket_id,
            operation_type,
            file_size,
            content_type,
            user_id,
            metadata,
            created_by
        ) VALUES (
            NEW.id,
            NEW.name,
            NEW.bucket_id,
            CASE 
                WHEN TG_OP = 'INSERT' THEN 'CREATE'
                WHEN TG_OP = 'UPDATE' THEN 'UPDATE'
                ELSE 'CREATE'
            END,
            NEW.metadata->>'size',
            NEW.metadata->>'mimetype',
            COALESCE((NEW.metadata->>'user_id')::UUID, auth.uid()),
            NEW.metadata,
            auth.uid()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new file uploads
CREATE TRIGGER trigger_queue_new_files_for_sync
    AFTER INSERT ON storage.objects
    FOR EACH ROW
    EXECUTE FUNCTION public.queue_file_for_sharepoint_sync();

-- Trigger for file updates
CREATE TRIGGER trigger_queue_updated_files_for_sync
    AFTER UPDATE ON storage.objects
    FOR EACH ROW
    WHEN (OLD.metadata IS DISTINCT FROM NEW.metadata OR OLD.name IS DISTINCT FROM NEW.name)
    EXECUTE FUNCTION public.queue_file_for_sharepoint_sync();

-- Function to queue file deletions for SharePoint sync
CREATE OR REPLACE FUNCTION public.queue_file_deletion_for_sharepoint_sync()
RETURNS TRIGGER AS $$
BEGIN
    -- Only queue deletions from the 'farms' bucket
    IF OLD.bucket_id = 'farms' THEN
        INSERT INTO public.synced_files (
            supabase_object_id,
            supabase_file_path,
            supabase_bucket_id,
            operation_type,
            user_id,
            metadata,
            created_by
        ) VALUES (
            OLD.id,
            OLD.name,
            OLD.bucket_id,
            'DELETE',
            COALESCE((OLD.metadata->>'user_id')::UUID, auth.uid()),
            OLD.metadata,
            auth.uid()
        );
    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for file deletions
CREATE TRIGGER trigger_queue_deleted_files_for_sync
    AFTER DELETE ON storage.objects
    FOR EACH ROW
    EXECUTE FUNCTION public.queue_file_deletion_for_sharepoint_sync();

-- ============================================================================
-- Cleanup Function for Old Records
-- ============================================================================

-- Function to clean up old completed sync records (can be called by a scheduled job)
CREATE OR REPLACE FUNCTION public.cleanup_old_sync_records(
    older_than_days INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.synced_files 
    WHERE status = 'COMPLETED' 
      AND processed_at < NOW() - (older_than_days || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- Comments for Documentation
-- ============================================================================

COMMENT ON TABLE public.synced_files IS 'Tracks file operations that need synchronization with SharePoint';
COMMENT ON TABLE public.sharepoint_sync_map IS 'Maps Supabase storage objects to SharePoint items';

COMMENT ON COLUMN public.synced_files.metadata IS 'JSON metadata for SharePoint list item fields and custom properties';
COMMENT ON COLUMN public.synced_files.retry_count IS 'Number of sync retry attempts made';
COMMENT ON COLUMN public.synced_files.next_retry_at IS 'Timestamp for next retry attempt (exponential backoff)';

COMMENT ON COLUMN public.sharepoint_sync_map.sharepoint_etag IS 'SharePoint ETag for conflict detection and optimistic concurrency';
COMMENT ON COLUMN public.sharepoint_sync_map.sharepoint_relative_url IS 'Server-relative URL for SharePoint REST API operations';
COMMENT ON COLUMN public.sharepoint_sync_map.sharepoint_metadata IS 'Additional SharePoint-specific metadata and properties';

