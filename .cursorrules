# Cursor Rules for NewTerra Onboarding System

## Project Context
This is a React/TypeScript/Supabase Ag-tech onboarding wizard for agriculture businesses. The system uses a consolidated hook architecture, hierarchical database design, and Australian business validation.

## Tech Stack Priorities
1. **React 18 + TypeScript** - Strict type checking required
2. **Supabase** - PostgreSQL + Auth + Edge Functions + Storage
3. **shadcn/ui + Tailwind** - Component library with Radix primitives
4. **Consolidated Hooks** - Custom hook system for forms and entities

## Critical Architecture Patterns

### Database Operations - ALWAYS USE THESE
```typescript
// ✅ USE: Generic CRUD from OnboardingContext
import { useOnboarding } from '@/contexts/OnboardingContext';
const { createRecord, updateRecord, deleteRecord } = useOnboarding();

// ❌ NEVER: Direct Supabase calls
const { data } = await supabase.from('contacts').insert(data);
```

### Hook Usage - MANDATORY PATTERNS
```typescript
// ✅ USE: Consolidated hooks
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';
import { useFormManagement } from '@/hooks/use-form-management';

// ❌ DEPRECATED: Do not use these
import { useAutoSaveFormField } from '@/hooks/use-auto-save-form-field';
import { useFormFieldManager } from '@/hooks/use-form-field-manager';
```

### Type Safety - EXPLICIT TYPING REQUIRED
```typescript
// ✅ ALWAYS: Use explicit database types
import type { TableRow, TableInsert, TableUpdate } from '@/contexts/OnboardingContext';
import type { Database } from '@/types/database.types';

const contactData: TableInsert<'contacts'> = {
  step_1_id: stepId,
  name: 'John Doe',
  contact_type: 'Main Contact' as Database['farms']['Enums']['contact_type_enum']
};
```

## Component Development Rules

### Step Components MUST Follow This Pattern
```typescript
export const BusinessProfileStep = () => {
  // 1. ALWAYS: Initialize step first
  const { isLoading, effectiveStepId, initializationError } = useEnhancedStepInit({
    stepName: 'BusinessProfileStep',
    sessionId,
    contextLoading,
    existingStepId: getStepId(sessionData, 'step1'),
    ensureStepFunction: ensureStep1BusinessProfileRecordExists,
  });

  // 2. ALWAYS: Check for effective step ID before proceeding
  if (!effectiveStepId) {
    return <StepLoadingState />;
  }

  // 3. THEN: Set up entity management
  const contactManagement = useEntityListManagement({
    stepId: effectiveStepId,
    tableName: 'contacts',
    entities: sessionData?.step1_businessProfile?.contacts || [],
    createDefaultEntity: (stepId) => ({ ... }),
    entityDisplayName: 'Contact'
  });
};
```

### Form Components Best Practices
```typescript
// ✅ USE: FormSection wrapper for consistency
<FormSection title="Business Details" description="Enter your business information">
  <AutoSaveFormField
    label="Business Name"
    name="business_name"
    entityId={entity.id}
    tableName="business_registration"
    updateFn={updateRecord}
    validationRules={[{ rule: 'required', message: 'Business name required' }]}
  />
</FormSection>

// ✅ USE: Australian validation patterns
import { validateABN, formatPhoneNumber } from '@/utils/onboarding-validation';
```

## Security & Validation Rules

### Australian Business Requirements
- **ABN Format**: Must use `validateABN()` from `@/utils/onboarding-validation`
- **Phone Format**: Must use `formatPhoneNumber()` for `+61XXXXXXXXX` format
- **ACN Format**: Must use `validateACN()` for 9-digit format

### Sensitive Data Handling
```typescript
// ✅ ALWAYS: Encrypt sensitive fields via Edge Functions
const { encryptAndStoreSensitiveField } = useOnboarding();
await encryptAndStoreSensitiveField({
  tableName: 'bookkeeping',
  fieldName: 'access_credentials',
  plainTextValue: credentials
});

// ❌ NEVER: Store sensitive data directly
await updateRecord('bookkeeping', id, { access_credentials: credentials });
```

### Error Handling Pattern
```typescript
// ✅ ALWAYS: Comprehensive error handling
try {
  const result = await createRecord('contacts', contactData);
  toast({ title: "Success", description: "Contact added successfully" });
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  logger.error('Failed to create contact:', error);
  toast({
    title: "Error",
    description: `Failed to add contact: ${errorMessage}`,
    variant: "destructive"
  });
}
```

## File Organization Guidelines

### Import Order (ENFORCE)
```typescript
// 1. React and external libraries
import React from 'react';
import { useCallback } from 'react';

// 2. Internal contexts and hooks
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';

// 3. Types (ALWAYS explicit)
import type { ContactFormData, TableInsert } from '@/types/form-components';

// 4. Utils and validation
import { validateABN, formatPhoneNumber } from '@/utils/onboarding-validation';

// 5. UI components
import { FormSection } from '@/components/form_components/FormSection';
import { Button } from '@/components/ui/button';
```

### File Naming Conventions
- Components: `PascalCase.tsx`
- Hooks: `use-kebab-case.ts`
- Utils: `kebab-case.ts`
- Types: `kebab-case.ts`

## Database & API Patterns

### Step Column Mapping (REFERENCE)
```typescript
const STEP_COLUMN_MAPPING = {
  // Step 1 tables
  'addresses': 'step_1_id',
  'contacts': 'step_1_id',
  'business_registration': 'step_1_id',
  'key_staff': 'step_1_id',
  // Step 2 tables
  'activities': 'step_2_id',
  'licenses': 'step_2_id',
  'suppliers': 'step_2_id',
  'contracts': 'step_2_id',
  // Step 3 tables
  'assets': 'step_3_id',
  'bookkeeping': 'step_3_id',
  'payroll': 'step_3_id',
  // Step 4 tables
  'agreements': 'step_4_id',
  'permissions': 'step_4_id',
  'payments': 'step_4_id'
};
```

### Edge Function Usage
```typescript
// ✅ USE: API wrappers from @/integrations/supabase/api
import { validateAbn, loadOrCreateOnboardingSession } from '@/integrations/supabase/api';

// ❌ AVOID: Direct Edge Function calls
const { data } = await supabase.functions.invoke('validate-abn', { body: { abn } });
```

## Performance Guidelines

### Debouncing & Auto-Save
- **Form fields**: 1000ms debounce (default)
- **Entity operations**: Use `useSelectiveRefresh` for targeted updates
- **Memory management**: Always use cleanup functions in useEffect

### Component Optimization
```typescript
// ✅ USE: React.memo for entity list items
const ContactItem = React.memo<ContactItemProps>(({ contact, onUpdate, onDelete }) => {
  // Component implementation
});

// ✅ USE: Proper dependency arrays
const handleUpdate = useCallback(async (data) => {
  await updateRecord('contacts', contact.id, data);
}, [updateRecord, contact.id]);
```

## Code Quality Standards

### ESLint Compliance
- **Strict TypeScript**: No `any` types allowed
- **React Hooks**: Proper dependency arrays mandatory
- **Import Organization**: Absolute imports with `@/` prefix

### Testing Requirements
```typescript
// ✅ FOCUS: Business logic and validation
describe('validateABN', () => {
  it('should validate correct ABN format', () => {
    expect(validateABN('**************')).toBe(true);
  });
});

// ✅ FOCUS: Hook behavior and state management
describe('useEntityListManagement', () => {
  it('should handle entity CRUD operations', async () => {
    // Test implementation
  });
});
```

## Common Anti-Patterns to AVOID

### ❌ DO NOT:
1. Use deprecated hooks (`useAutoSaveFormField`, `useFormFieldManager`)
2. Make direct Supabase calls (use OnboardingContext CRUD)
3. Skip type annotations (always use explicit types)
4. Store sensitive data unencrypted
5. Bypass Australian validation patterns
6. Create components without error boundaries
7. Use `any` type annotations
8. Skip UUID validation before database operations
9. Forget debouncing on form fields
10. Use console.log in production code

### ✅ ALWAYS DO:
1. Use consolidated hooks from `@/hooks/`
2. Explicit type casting with database types
3. Australian business format validation
4. Comprehensive error handling with user feedback
5. Auto-save patterns for better UX
6. Step initialization before accessing child data
7. UUID validation for all ID parameters
8. Proper loading and error states
9. Accessibility compliance in components
10. Performance optimization with React.memo and useCallback

## File-Specific Guidelines

When editing specific files:
- **Step components**: Always follow the 3-step pattern (init → entities → forms)
- **Form components**: Use AutoSaveFormField for individual fields
- **Hook files**: Follow consolidated architecture patterns
- **API files**: Use proper error handling and type definitions
- **Validation files**: Include Australian business format requirements

## Documentation Standards
- **README**: Keep updated with current architecture
- **CLAUDE.md**: Reference for all development decisions
- **Inline comments**: Focus on business logic and Australian requirements
- **Type definitions**: Always include JSDoc for complex types

This configuration ensures consistent development patterns that align with the NewTerra onboarding system's consolidated architecture and Australian business requirements.