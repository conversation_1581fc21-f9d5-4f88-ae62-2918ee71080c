/**
 * TypeScript interfaces for refactored form components
 */

import { ReactNode } from 'react';
import { Database } from './database.types';

/**
 * Base props for step components
 */
export interface StepComponentProps {
  disabled?: boolean;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
}

/**
 * Props for individual entity form components
 */
export interface EntityFormComponentProps<T extends { id: string }> {
  entity: T;
  disabled: boolean;
  onUpdate: (id: string, data: Partial<T>) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  isDeleting?: boolean;
}

/**
 * Props for entity form renderers used with EntityFormList
 */
export interface EntityFormRenderProps<T extends { id: string }> {
  entity: T;
  disabled: boolean;
  onUpdate: (entityId: string, data: Partial<T>) => Promise<void>;
  onDelete: (entityId: string) => Promise<void>;
  isDeleting?: boolean;
}

/**
 * Configuration for select field options
 */
export interface SelectFieldOption {
  value: string;
  label: string;
  disabled?: boolean;
}

/**
 * Business Registration types
 */
export interface BusinessRegistrationFormData {
  id: string;
  full_business_name: string;
  trading_name?: string | null;
  abn: string;
  acn?: string | null;
  business_structure: Database['farms']['Enums']['business_structure_enum'];
  is_gst_registered?: boolean;
  primary_business_phone?: string | null;
  step_1_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * Contact types
 */
export interface ContactFormData {
  id: string;
  step_1_id: string;
  name: string;
  email?: string | null;
  phone?: string | null;
  contact_type: Database['farms']['Enums']['contact_type_enum'];
  title_or_firm?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Address types
 */
export interface AddressFormData {
  id: string;
  step_1_id: string;
  address_type: string;
  full_address_text: string;
  street?: string | null;
  locality?: string | null;
  state?: string | null;
  postcode?: string | null;
  country?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Key Staff types
 */
export interface KeyStaffFormData {
  id: string;
  step_1_id: string;
  staff_name: string;
  role_or_title?: string | null;
  contact_email?: string | null;
  contact_phone?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Activity types for farm operations
 */
export interface ActivityFormData {
  id: string;
  step_2_id: string;
  activity_type: Database['farms']['Enums']['activity_type_enum'];
  activity_description?: string | null;
  approximate_numbers?: number | null;
  crop_type?: string | null;
  crop_varieties?: string[] | null;
  livestock_type?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * License types for farm operations
 */
export interface LicenseFormData {
  id: string;
  step_2_id: string;
  license_type: Database['farms']['Enums']['license_type_enum'];
  license_number?: string | null;
  issuing_authority?: string | null;
  issue_date?: string | null;
  expiry_date?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Supplier types for farm operations
 */
export interface SupplierFormData {
  id: string;
  step_2_id: string;
  supplier_name: string;
  supplier_category?: string | null;
  contact_person?: string | null;
  contact_details?: string | null;
  services_provided: string;
  created_at: string;
  updated_at: string;
}

/**
 * Contract types for farm operations
 */
export interface ContractFormData {
  id: string;
  step_2_id: string;
  contract_type?: string | null;
  contract_description: string;
  start_date?: string | null;
  expiry_date?: string | null;
  contract_value?: number | null;
  supplier_id?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Chemical usage types for farm operations
 */
export interface ChemicalUsageFormData {
  id: string;
  step_2_id: string;
  product_name: string;
  manufacturer?: string | null;
  application_method?: string | null;
  application_rate?: string | null;
  usage_purpose: string;
  withholding_period_days?: number | null;
  last_application_date?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Bookkeeping types for financial systems
 */
export interface BookkeepingFormData {
  id: string;
  step_3_id: string;
  current_software: string;
  software_version?: string | null;
  access_credentials: Uint8Array | null;
  accountant_access_level?: string | null;
  accountant_has_access: boolean;
  bas_lodgement_frequency: Database['farms']['Enums']['bas_frequency_enum'];
  has_bank_feeds_enabled: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Payroll types for financial systems
 */
export interface PayrollFormData {
  id: string;
  step_3_id: string;
  is_payroll_processing_needed: boolean;
  employee_count?: number | null;
  current_payroll_software?: string | null;
  payroll_frequency?: string | null;
  superannuation_fund?: string | null;
  workers_compensation_policy?: string | null;
  encrypted_access_credentials: Uint8Array | null;
  is_access_to_software_granted: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Asset types for financial systems
 */
export interface AssetFormData {
  id: string;
  step_3_id: string;
  asset_category: Database['farms']['Enums']['asset_category_enum'];
  asset_type: string;
  make_or_provider?: string | null;
  registration_or_policy_number?: string | null;
  renewal_date?: string | null;
  coverage_amount?: number | null;
  excess_amount?: number | null;
  model_year?: number | null;
  policy_type?: string | null;
  purchase_date?: string | null;
  purchase_price?: number | null;
  serial_number?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Data migration types for agreements and review
 */
export interface DataMigrationFormData {
  id: string;
  step_4_id: string;
  filing_system_description: string;
  primary_cloud_storage: string;
  secondary_cloud_storage?: string | null;
  folder_structure_description?: string | null;
  document_categories?: string[] | null;
  estimated_document_count?: number | null;
  created_at: string;
  updated_at: string;
}

/**
 * Permission types for agreements and review
 */
export interface PermissionFormData {
  id: string;
  step_4_id: string;
  data_type: string;
  access_level: string;
  permission_granted: boolean;
  permission_granted_date?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Agreement types for agreements and review
 */
export interface AgreementFormData {
  id: string;
  step_4_id: string;
  agreement_type: Database['farms']['Enums']['agreement_type_enum'];
  is_agreed: boolean;
  agreed_at?: string | null;
  agreement_version?: string | null;
  signature_data?: string | null;
  signature_storage_path?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Payment types for agreements and review
 */
export interface PaymentFormData {
  id: string;
  step_4_id: string;
  bank_account_details: Uint8Array;
  payment_method?: string | null;
  payment_frequency?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Communication preferences types for agreements and review
 */
export interface CommunicationPreferencesFormData {
  id: string;
  step_4_id: string;
  preferred_methods: Database['farms']['Enums']['communication_method_enum'][];
  preferred_contact_times: string;
  reporting_frequency: Database['farms']['Enums']['reporting_frequency_enum'];
  emergency_contact_method?: Database['farms']['Enums']['communication_method_enum'] | null;
  business_hours_only: boolean;
  timezone?: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Common validation patterns
 */
export interface ValidationRule {
  required?: boolean;
  pattern?: RegExp;
  custom?: (value: string | number | boolean | null | undefined) => string | null;
  message?: string;
}

/**
 * Form field configuration for auto-generation
 */
export interface FieldConfig<T = Record<string, unknown>> {
  key: keyof T;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: SelectFieldOption[];
  placeholder?: string;
  maxLength?: number;
  min?: number;
  max?: number;
  step?: number;
  validationRules?: ValidationRule[];
  formatter?: (value: string | number | boolean | null) => string | number | boolean | null;
  description?: string;
  disabled?: boolean;
}

/**
 * Entity list configuration
 */
export interface EntityListConfig<T extends { id: string }> {
  title: string;
  description: string;
  entityDisplayName: string;
  entityDisplayNamePlural?: string;
  tableName: string;
  defaultValues: Partial<T>;
  minEntities?: number;
  maxEntities?: number;
  confirmDelete?: boolean;
  showEntityCount?: boolean;
  addButtonText?: string;
  emptyStateMessage?: string;
}

/**
 * Generic CRUD functions type
 */
export interface CRUDFunctions {
  createRecord: (tableName: string, data: Record<string, unknown>) => Promise<Record<string, unknown>>;
  updateRecord: (tableName: string, id: string, data: Record<string, unknown>) => Promise<void>;
  deleteRecord: (tableName: string, id: string) => Promise<boolean>;
}

/**
 * Step data structure for passing to components
 */
export interface StepData<T = Record<string, unknown>> {
  id: string;
  sessionId: string;
  data: T;
  isLoading: boolean;
  error: string | null;
}

/**
 * Common select options used across components
 */
export const BUSINESS_STRUCTURES: SelectFieldOption[] = [
  { value: 'Sole Trader', label: 'Sole Trader' },
  { value: 'Partnership', label: 'Partnership' },
  { value: 'Company', label: 'Company' },
  { value: 'Trust', label: 'Trust' },
];

export const CONTACT_TYPES: SelectFieldOption[] = [
  { value: 'Main Contact', label: 'Main Contact' },
  { value: 'Accountant', label: 'Accountant' },
  { value: 'BAS Agent', label: 'BAS Agent' },
  { value: 'Key Staff', label: 'Key Staff' },
];

export const ADDRESS_TYPES: SelectFieldOption[] = [
  { value: 'Property', label: 'Property Address' },
  { value: 'Postal', label: 'Postal Address' },
];

export const ACTIVITY_TYPES: SelectFieldOption[] = [
  { value: 'Cropping', label: 'Cropping' },
  { value: 'Livestock', label: 'Livestock' },
  { value: 'Mixed Farming', label: 'Mixed Farming' },
  { value: 'Horticulture', label: 'Horticulture' },
  { value: 'Dairy', label: 'Dairy' },
  { value: 'Aquaculture', label: 'Aquaculture' },
  { value: 'Forestry', label: 'Forestry' },
  { value: 'Other', label: 'Other' },
];

export const LICENSE_TYPES: SelectFieldOption[] = [
  { value: 'Chemical Permit', label: 'Chemical Permit' },
  { value: 'Machinery License', label: 'Machinery License' },
  { value: 'Food Safety Cert', label: 'Food Safety Certification' },
  { value: 'Water License', label: 'Water License/Allocation' },
  { value: 'Heavy Vehicle License', label: 'Heavy Vehicle License' },
  { value: 'Quad Bike License', label: 'Quad Bike License' },
  { value: 'Other', label: 'Other (Specify)' },
];

export const BAS_LODGEMENT_FREQUENCIES: SelectFieldOption[] = [
  { value: 'Monthly', label: 'Monthly' },
  { value: 'Quarterly', label: 'Quarterly' },
  { value: 'Annually', label: 'Annually' },
];

export const ASSET_CATEGORIES: SelectFieldOption[] = [
  { value: 'Vehicle', label: 'Vehicle' },
  { value: 'Equipment', label: 'Equipment' },
  { value: 'Insurance', label: 'Insurance' },
];