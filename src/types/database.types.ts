export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  farms: {
    Tables: {
      activities: {
        Row: {
          activity_description: string | null
          activity_type: Database["farms"]["Enums"]["activity_type_enum"]
          approximate_numbers: number | null
          created_at: string
          crop_type: string | null
          crop_varieties: string[] | null
          id: string
          livestock_type: string | null
          step_2_id: string
          updated_at: string
        }
        Insert: {
          activity_description?: string | null
          activity_type: Database["farms"]["Enums"]["activity_type_enum"]
          approximate_numbers?: number | null
          created_at?: string
          crop_type?: string | null
          crop_varieties?: string[] | null
          id?: string
          livestock_type?: string | null
          step_2_id: string
          updated_at?: string
        }
        Update: {
          activity_description?: string | null
          activity_type?: Database["farms"]["Enums"]["activity_type_enum"]
          approximate_numbers?: number | null
          created_at?: string
          crop_type?: string | null
          crop_varieties?: string[] | null
          id?: string
          livestock_type?: string | null
          step_2_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      addresses: {
        Row: {
          address_type: string
          country: string | null
          created_at: string
          full_address_text: string
          id: string
          locality: string | null
          postcode: string | null
          state: string | null
          step_1_id: string
          street: string | null
          updated_at: string
        }
        Insert: {
          address_type: string
          country?: string | null
          created_at?: string
          full_address_text: string
          id?: string
          locality?: string | null
          postcode?: string | null
          state?: string | null
          step_1_id: string
          street?: string | null
          updated_at?: string
        }
        Update: {
          address_type?: string
          country?: string | null
          created_at?: string
          full_address_text?: string
          id?: string
          locality?: string | null
          postcode?: string | null
          state?: string | null
          step_1_id?: string
          street?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "addresses_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: false
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      agreements: {
        Row: {
          agreed_at: string | null
          agreement_type: Database["farms"]["Enums"]["agreement_type_enum"]
          agreement_version: string | null
          created_at: string
          id: string
          is_agreed: boolean
          signature_data: string | null
          signature_storage_path: string | null
          step_4_id: string
          updated_at: string
        }
        Insert: {
          agreed_at?: string | null
          agreement_type: Database["farms"]["Enums"]["agreement_type_enum"]
          agreement_version?: string | null
          created_at?: string
          id?: string
          is_agreed?: boolean
          signature_data?: string | null
          signature_storage_path?: string | null
          step_4_id: string
          updated_at?: string
        }
        Update: {
          agreed_at?: string | null
          agreement_type?: Database["farms"]["Enums"]["agreement_type_enum"]
          agreement_version?: string | null
          created_at?: string
          id?: string
          is_agreed?: boolean
          signature_data?: string | null
          signature_storage_path?: string | null
          step_4_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agreements_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: false
            referencedRelation: "step_4_agreements"
            referencedColumns: ["id"]
          },
        ]
      }
      assets: {
        Row: {
          asset_category: Database["farms"]["Enums"]["asset_category_enum"]
          asset_type: string
          coverage_amount: number | null
          created_at: string
          excess_amount: number | null
          id: string
          make_or_provider: string | null
          model_year: number | null
          policy_type: string | null
          purchase_date: string | null
          purchase_price: number | null
          registration_or_policy_number: string | null
          renewal_date: string | null
          serial_number: string | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          asset_category: Database["farms"]["Enums"]["asset_category_enum"]
          asset_type: string
          coverage_amount?: number | null
          created_at?: string
          excess_amount?: number | null
          id?: string
          make_or_provider?: string | null
          model_year?: number | null
          policy_type?: string | null
          purchase_date?: string | null
          purchase_price?: number | null
          registration_or_policy_number?: string | null
          renewal_date?: string | null
          serial_number?: string | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          asset_category?: Database["farms"]["Enums"]["asset_category_enum"]
          asset_type?: string
          coverage_amount?: number | null
          created_at?: string
          excess_amount?: number | null
          id?: string
          make_or_provider?: string | null
          model_year?: number | null
          policy_type?: string | null
          purchase_date?: string | null
          purchase_price?: number | null
          registration_or_policy_number?: string | null
          renewal_date?: string | null
          serial_number?: string | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "assets_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: false
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      bookkeeping: {
        Row: {
          access_credentials: Uint8Array | null
          accountant_access_level: string | null
          accountant_has_access: boolean
          bas_lodgement_frequency: Database["farms"]["Enums"]["bas_frequency_enum"]
          created_at: string
          current_software: string
          has_bank_feeds_enabled: boolean
          id: string
          software_version: string | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          access_credentials?: Uint8Array | null
          accountant_access_level?: string | null
          accountant_has_access?: boolean
          bas_lodgement_frequency: Database["farms"]["Enums"]["bas_frequency_enum"]
          created_at?: string
          current_software: string
          has_bank_feeds_enabled?: boolean
          id?: string
          software_version?: string | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          access_credentials?: Uint8Array | null
          accountant_access_level?: string | null
          accountant_has_access?: boolean
          bas_lodgement_frequency?: Database["farms"]["Enums"]["bas_frequency_enum"]
          created_at?: string
          current_software?: string
          has_bank_feeds_enabled?: boolean
          id?: string
          software_version?: string | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookkeeping_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      business_registration: {
        Row: {
          abn: string
          acn: string | null
          business_structure: Database["farms"]["Enums"]["business_structure_enum"]
          created_at: string
          full_business_name: string
          id: string
          is_gst_registered: boolean
          primary_business_phone: string | null
          step_1_id: string
          trading_name: string | null
          updated_at: string
        }
        Insert: {
          abn: string
          acn?: string | null
          business_structure: Database["farms"]["Enums"]["business_structure_enum"]
          created_at?: string
          full_business_name: string
          id?: string
          is_gst_registered?: boolean
          primary_business_phone?: string | null
          step_1_id: string
          trading_name?: string | null
          updated_at?: string
        }
        Update: {
          abn?: string
          acn?: string | null
          business_structure?: Database["farms"]["Enums"]["business_structure_enum"]
          created_at?: string
          full_business_name?: string
          id?: string
          is_gst_registered?: boolean
          primary_business_phone?: string | null
          step_1_id?: string
          trading_name?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_registration_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: true
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      chemical_usage: {
        Row: {
          application_method: string | null
          application_rate: string | null
          created_at: string
          id: string
          last_application_date: string | null
          manufacturer: string | null
          product_name: string
          step_2_id: string
          updated_at: string
          usage_purpose: string
          withholding_period_days: number | null
        }
        Insert: {
          application_method?: string | null
          application_rate?: string | null
          created_at?: string
          id?: string
          last_application_date?: string | null
          manufacturer?: string | null
          product_name: string
          step_2_id: string
          updated_at?: string
          usage_purpose: string
          withholding_period_days?: number | null
        }
        Update: {
          application_method?: string | null
          application_rate?: string | null
          created_at?: string
          id?: string
          last_application_date?: string | null
          manufacturer?: string | null
          product_name?: string
          step_2_id?: string
          updated_at?: string
          usage_purpose?: string
          withholding_period_days?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "chemical_usage_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      communication_preferences: {
        Row: {
          business_hours_only: boolean
          created_at: string
          emergency_contact_method:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          id: string
          preferred_contact_times: string
          preferred_methods: Database["farms"]["Enums"]["communication_method_enum"][]
          reporting_frequency: Database["farms"]["Enums"]["reporting_frequency_enum"]
          step_4_id: string
          timezone: string | null
          updated_at: string
        }
        Insert: {
          business_hours_only?: boolean
          created_at?: string
          emergency_contact_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          id?: string
          preferred_contact_times: string
          preferred_methods: Database["farms"]["Enums"]["communication_method_enum"][]
          reporting_frequency: Database["farms"]["Enums"]["reporting_frequency_enum"]
          step_4_id: string
          timezone?: string | null
          updated_at?: string
        }
        Update: {
          business_hours_only?: boolean
          created_at?: string
          emergency_contact_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          id?: string
          preferred_contact_times?: string
          preferred_methods?: Database["farms"]["Enums"]["communication_method_enum"][]
          reporting_frequency?: Database["farms"]["Enums"]["reporting_frequency_enum"]
          step_4_id?: string
          timezone?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "communication_preferences_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: true
            referencedRelation: "step_4_agreements"
            referencedColumns: ["id"]
          },
        ]
      }
      contacts: {
        Row: {
          contact_type: Database["farms"]["Enums"]["contact_type_enum"]
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          step_1_id: string
          title_or_firm: string | null
          updated_at: string
        }
        Insert: {
          contact_type: Database["farms"]["Enums"]["contact_type_enum"]
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          step_1_id: string
          title_or_firm?: string | null
          updated_at?: string
        }
        Update: {
          contact_type?: Database["farms"]["Enums"]["contact_type_enum"]
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          step_1_id?: string
          title_or_firm?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contacts_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: false
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      contracts: {
        Row: {
          contract_description: string
          contract_type: string | null
          contract_value: number | null
          created_at: string
          expiry_date: string | null
          id: string
          start_date: string | null
          step_2_id: string
          supplier_id: string | null
          updated_at: string
        }
        Insert: {
          contract_description: string
          contract_type?: string | null
          contract_value?: number | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          start_date?: string | null
          step_2_id: string
          supplier_id?: string | null
          updated_at?: string
        }
        Update: {
          contract_description?: string
          contract_type?: string | null
          contract_value?: number | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          start_date?: string | null
          step_2_id?: string
          supplier_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contracts_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contracts_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      data_migration: {
        Row: {
          created_at: string
          document_categories: string[] | null
          estimated_document_count: number | null
          filing_system_description: string
          folder_structure_description: string | null
          id: string
          primary_cloud_storage: string
          secondary_cloud_storage: string | null
          step_4_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          document_categories?: string[] | null
          estimated_document_count?: number | null
          filing_system_description: string
          folder_structure_description?: string | null
          id?: string
          primary_cloud_storage: string
          secondary_cloud_storage?: string | null
          step_4_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          document_categories?: string[] | null
          estimated_document_count?: number | null
          filing_system_description?: string
          folder_structure_description?: string | null
          id?: string
          primary_cloud_storage?: string
          secondary_cloud_storage?: string | null
          step_4_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "data_migration_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: true
            referencedRelation: "step_4_agreements"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          document_category: string
          document_name: string
          file_size_bytes: number | null
          file_type: string | null
          id: string
          onboarding_session_id: string
          related_to_entity: string
          related_to_id: string
          storage_bucket_path: string
          uploaded_at: string
        }
        Insert: {
          document_category: string
          document_name: string
          file_size_bytes?: number | null
          file_type?: string | null
          id?: string
          onboarding_session_id: string
          related_to_entity: string
          related_to_id: string
          storage_bucket_path: string
          uploaded_at?: string
        }
        Update: {
          document_category?: string
          document_name?: string
          file_size_bytes?: number | null
          file_type?: string | null
          id?: string
          onboarding_session_id?: string
          related_to_entity?: string
          related_to_id?: string
          storage_bucket_path?: string
          uploaded_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: false
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      key_staff: {
        Row: {
          contact_email: string | null
          contact_phone: string | null
          created_at: string
          id: string
          role_or_title: string | null
          staff_name: string
          step_1_id: string
          updated_at: string
        }
        Insert: {
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          id?: string
          role_or_title?: string | null
          staff_name: string
          step_1_id: string
          updated_at?: string
        }
        Update: {
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          id?: string
          role_or_title?: string | null
          staff_name?: string
          step_1_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "key_staff_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: false
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      licenses: {
        Row: {
          created_at: string
          expiry_date: string | null
          id: string
          issue_date: string | null
          issuing_authority: string | null
          license_number: string | null
          license_type: Database["farms"]["Enums"]["license_type_enum"]
          step_2_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          expiry_date?: string | null
          id?: string
          issue_date?: string | null
          issuing_authority?: string | null
          license_number?: string | null
          license_type: Database["farms"]["Enums"]["license_type_enum"]
          step_2_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          expiry_date?: string | null
          id?: string
          issue_date?: string | null
          issuing_authority?: string | null
          license_number?: string | null
          license_type?: Database["farms"]["Enums"]["license_type_enum"]
          step_2_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "licenses_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      onboarding_sessions: {
        Row: {
          created_at: string
          current_step: number
          id: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_step?: number
          id?: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_step?: number
          id?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      payments: {
        Row: {
          bank_account_details: Uint8Array
          created_at: string
          id: string
          payment_frequency: string | null
          payment_method: string | null
          step_4_id: string
          updated_at: string
        }
        Insert: {
          bank_account_details: Uint8Array
          created_at?: string
          id?: string
          payment_frequency?: string | null
          payment_method?: string | null
          step_4_id: string
          updated_at?: string
        }
        Update: {
          bank_account_details?: Uint8Array
          created_at?: string
          id?: string
          payment_frequency?: string | null
          payment_method?: string | null
          step_4_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payments_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: true
            referencedRelation: "step_4_agreements"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll: {
        Row: {
          created_at: string
          current_payroll_software: string | null
          employee_count: number | null
          encrypted_access_credentials: Uint8Array | null
          id: string
          is_access_to_software_granted: boolean
          is_payroll_processing_needed: boolean
          payroll_frequency: string | null
          step_3_id: string
          superannuation_fund: string | null
          updated_at: string
          workers_compensation_policy: string | null
        }
        Insert: {
          created_at?: string
          current_payroll_software?: string | null
          employee_count?: number | null
          encrypted_access_credentials?: Uint8Array | null
          id?: string
          is_access_to_software_granted?: boolean
          is_payroll_processing_needed: boolean
          payroll_frequency?: string | null
          step_3_id: string
          superannuation_fund?: string | null
          updated_at?: string
          workers_compensation_policy?: string | null
        }
        Update: {
          created_at?: string
          current_payroll_software?: string | null
          employee_count?: number | null
          encrypted_access_credentials?: Uint8Array | null
          id?: string
          is_access_to_software_granted?: boolean
          is_payroll_processing_needed?: boolean
          payroll_frequency?: string | null
          step_3_id?: string
          superannuation_fund?: string | null
          updated_at?: string
          workers_compensation_policy?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payroll_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      permissions: {
        Row: {
          access_level: string
          created_at: string
          data_type: string
          id: string
          permission_granted: boolean
          permission_granted_date: string | null
          step_4_id: string
          updated_at: string
        }
        Insert: {
          access_level: string
          created_at?: string
          data_type: string
          id?: string
          permission_granted?: boolean
          permission_granted_date?: string | null
          step_4_id: string
          updated_at?: string
        }
        Update: {
          access_level?: string
          created_at?: string
          data_type?: string
          id?: string
          permission_granted?: boolean
          permission_granted_date?: string | null
          step_4_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "permissions_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: false
            referencedRelation: "step_4_agreements"
            referencedColumns: ["id"]
          },
        ]
      }
      step_1_business_profile: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_1_business_profile_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_2_farm_operations: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_2_farm_operations_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_3_financial_systems: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_3_financial_systems_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_4_agreements: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_4_agreements_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      suppliers: {
        Row: {
          contact_details: string | null
          contact_person: string | null
          created_at: string
          id: string
          services_provided: string
          step_2_id: string
          supplier_category: string | null
          supplier_name: string
          updated_at: string
        }
        Insert: {
          contact_details?: string | null
          contact_person?: string | null
          created_at?: string
          id?: string
          services_provided: string
          step_2_id: string
          supplier_category?: string | null
          supplier_name: string
          updated_at?: string
        }
        Update: {
          contact_details?: string | null
          contact_person?: string | null
          created_at?: string
          id?: string
          services_provided?: string
          step_2_id?: string
          supplier_category?: string | null
          supplier_name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "suppliers_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      activity_type_enum:
        | "Cropping"
        | "Livestock"
        | "Mixed Farming"
        | "Horticulture"
        | "Dairy"
        | "Aquaculture"
        | "Forestry"
        | "Other"
      agreement_type_enum:
        | "Service Agreement"
        | "Privacy Policy"
        | "Direct Debit"
        | "Terms and Conditions"
      asset_category_enum: "Vehicle" | "Equipment" | "Insurance"
      bas_frequency_enum: "Monthly" | "Quarterly" | "Annually"
      business_structure_enum:
        | "Sole Trader"
        | "Company"
        | "Partnership"
        | "Trust"
        | "Cooperative"
        | "Other"
      communication_method_enum: "Email" | "Phone" | "SMS" | "WhatsApp"
      contact_type_enum:
        | "Main Contact"
        | "Accountant"
        | "BAS Agent"
        | "Key Staff"
        | "Admin"
        | "Accounts"
        | "Personal"
      license_type_enum:
        | "Chemical Permit"
        | "Machinery License"
        | "Food Safety Cert"
        | "Water License"
        | "Heavy Vehicle License"
        | "Quad Bike License"
        | "Other"
      reporting_frequency_enum: "Weekly" | "Fortnightly" | "Monthly"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      profiles: {
        Row: {
          created_at: string | null
          full_name: string | null
          id: string
          phone_number: string | null
          receive_text_messages: boolean | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          created_at?: string | null
          full_name?: string | null
          id: string
          phone_number?: string | null
          receive_text_messages?: boolean | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          created_at?: string | null
          full_name?: string | null
          id?: string
          phone_number?: string | null
          receive_text_messages?: boolean | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_user_onboarding_session_with_steps: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      custom_access_token_hook: {
        Args: { event: Json }
        Returns: Json
      }
      finalize_document_upload: {
        Args: { payload: Json }
        Returns: string
      }
      get_onboarding_session_data: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_session_id_from_path: {
        Args: { p_path_name: string }
        Returns: string
      }
      update_encrypted_field: {
        Args: {
          p_table_name: string
          p_session_id: string
          p_field_name: string
          p_encrypted_value: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  farms: {
    Enums: {
      activity_type_enum: [
        "Cropping",
        "Livestock",
        "Mixed Farming",
        "Horticulture",
        "Dairy",
        "Aquaculture",
        "Forestry",
        "Other",
      ],
      agreement_type_enum: [
        "Service Agreement",
        "Privacy Policy",
        "Direct Debit",
        "Terms and Conditions",
      ],
      asset_category_enum: ["Vehicle", "Equipment", "Insurance"],
      bas_frequency_enum: ["Monthly", "Quarterly", "Annually"],
      business_structure_enum: [
        "Sole Trader",
        "Company",
        "Partnership",
        "Trust",
        "Cooperative",
        "Other",
      ],
      communication_method_enum: ["Email", "Phone", "SMS", "WhatsApp"],
      contact_type_enum: [
        "Main Contact",
        "Accountant",
        "BAS Agent",
        "Key Staff",
        "Admin",
        "Accounts",
        "Personal",
      ],
      license_type_enum: [
        "Chemical Permit",
        "Machinery License",
        "Food Safety Cert",
        "Water License",
        "Heavy Vehicle License",
        "Quad Bike License",
        "Other",
      ],
      reporting_frequency_enum: ["Weekly", "Fortnightly", "Monthly"],
    },
  },
  public: {
    Enums: {},
  },
} as const
