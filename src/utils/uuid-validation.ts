/**
 * UUID Validation Utilities
 * 
 * Centralized UUID validation to prevent invalid IDs from being passed
 * to document upload operations and database queries.
 */

export const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * Validates if a string is a properly formatted UUID v4
 */
export function isValidUUID(value: unknown): value is string {
  return typeof value === 'string' && 
         value.trim() !== '' && 
         UUID_REGEX.test(value);
}

/**
 * Validates UUID and throws a descriptive error if invalid
 */
export function validateUUID(value: unknown, fieldName: string): string {
  if (!value) {
    throw new Error(`${fieldName} is required`);
  }
  
  if (typeof value !== 'string') {
    throw new Error(`${fieldName} must be a string, got ${typeof value}`);
  }
  
  const trimmed = value.trim();
  if (trimmed === '') {
    throw new Error(`${fieldName} cannot be empty`);
  }
  
  if (!UUID_REGEX.test(trimmed)) {
    throw new Error(`${fieldName} must be a valid UUID format (got: ${trimmed})`);
  }
  
  return trimmed;
}

/**
 * Safely validates UUID with return type indicating success/failure
 */
export function safeValidateUUID(value: unknown, fieldName: string): { isValid: true; uuid: string } | { isValid: false; error: string } {
  try {
    const uuid = validateUUID(value, fieldName);
    return { isValid: true, uuid };
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : `Invalid ${fieldName}` 
    };
  }
}

/**
 * Validates multiple UUIDs at once
 */
export function validateUUIDs(values: Record<string, unknown>): { isValid: true; uuids: Record<string, string> } | { isValid: false; errors: string[] } {
  const errors: string[] = [];
  const uuids: Record<string, string> = {};
  
  for (const [fieldName, value] of Object.entries(values)) {
    const result = safeValidateUUID(value, fieldName);
    if (result.isValid) {
      uuids[fieldName] = result.uuid;
    } else {
      errors.push(result.error);
    }
  }
  
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  
  return { isValid: true, uuids };
}

/**
 * Validates entity IDs before document upload operations
 */
export function validateEntityForUpload(entityId: unknown, entityType: string): string {
  if (!entityId) {
    throw new Error(`Cannot upload document: ${entityType} entity ID is missing. Please save the ${entityType} first.`);
  }
  
  return validateUUID(entityId, `${entityType} entity ID`);
}

/**
 * Type guard for checking if an object has a valid ID field
 */
export function hasValidId(entity: unknown): entity is { id: string } {
  return (
    typeof entity === 'object' &&
    entity !== null &&
    'id' in entity &&
    isValidUUID((entity as { id: unknown }).id)
  );
}