/**
 * Common validation utilities used across onboarding form steps
 * Consolidates patterns from BusinessProfileStep, FarmOperationsStep, and other steps
 */

// Enhanced regex patterns for better validation
export const VALIDATION_PATTERNS = {
  // Australian phone number validation (more specific)
  PHONE: /^\+61[2-9]\d{8}$/,
  // Better email validation
  EMAIL: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
  // Australian ABN validation (11 digits)
  ABN: /^\d{11}$/,
  // Australian ACN validation (9 digits)
  ACN: /^\d{9}$/,
  // General numeric validation
  NUMERIC: /^\d+$/,
  // Decimal numbers
  DECIMAL: /^\d+(\.\d{1,2})?$/,
} as const;

/**
 * Format Australian phone number to standard format
 */
export const formatAustralianPhone = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Handle different input formats
  if (cleaned.startsWith('61')) {
    return `+${cleaned}`;
  }
  if (cleaned.startsWith('0')) {
    return `+61${cleaned.substring(1)}`;
  }
  if (cleaned.length === 9) {
    return `+61${cleaned}`;
  }
  
  return phone; // Return original if can't format
};

/**
 * Validate Australian phone number
 */
export const validateAustralianPhone = (phone: string): string | null => {
  if (!phone?.trim()) {
    return "Phone number is required";
  }
  
  const formatted = formatAustralianPhone(phone);
  if (!VALIDATION_PATTERNS.PHONE.test(formatted)) {
    return "Please enter a valid Australian phone number (e.g., +***********)";
  }
  
  return null;
};

/**
 * Format ABN for display (XX XXX XXX XXX)
 */
export const formatABN = (abn: string): string => {
  const cleaned = abn.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `${cleaned.substring(0, 2)} ${cleaned.substring(2, 5)} ${cleaned.substring(5, 8)} ${cleaned.substring(8, 11)}`;
  }
  return abn;
};

/**
 * Validate Australian Business Number (ABN)
 */
export const validateABN = (abn: string): string | null => {
  if (!abn?.trim()) {
    return "ABN is required";
  }
  
  const cleaned = abn.replace(/\D/g, '');
  if (!VALIDATION_PATTERNS.ABN.test(cleaned)) {
    return "ABN must be 11 digits";
  }
  
  // ABN checksum validation
  const weights = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
  const digits = cleaned.split('').map(Number);
  
  // Subtract 1 from the first digit
  digits[0] -= 1;
  
  // Calculate checksum
  const sum = digits.reduce((acc, digit, index) => acc + (digit * weights[index]), 0);
  
  if (sum % 89 !== 0) {
    return "Please enter a valid ABN";
  }
  
  return null;
};

/**
 * Validate email address
 */
export const validateEmail = (email: string): string | null => {
  if (!email?.trim()) {
    return "Email is required";
  }
  
  if (!VALIDATION_PATTERNS.EMAIL.test(email)) {
    return "Please enter a valid email address";
  }
  
  return null;
};

/**
 * Validate required field
 */
export const validateRequired = (value: unknown, fieldName: string): string | null => {
  if (!value || (typeof value === 'string' && !value.trim())) {
    return `${fieldName} is required`;
  }
  return null;
};

/**
 * Validate numeric field
 */
export const validateNumeric = (value: string, fieldName: string, min?: number, max?: number): string | null => {
  if (!value?.trim()) {
    return `${fieldName} is required`;
  }
  
  if (!VALIDATION_PATTERNS.NUMERIC.test(value)) {
    return `${fieldName} must be a number`;
  }
  
  const num = parseInt(value, 10);
  
  if (min !== undefined && num < min) {
    return `${fieldName} must be at least ${min}`;
  }
  
  if (max !== undefined && num > max) {
    return `${fieldName} must be no more than ${max}`;
  }
  
  return null;
};

/**
 * Validate decimal field
 */
export const validateDecimal = (value: string, fieldName: string, min?: number, max?: number): string | null => {
  if (!value?.trim()) {
    return `${fieldName} is required`;
  }
  
  if (!VALIDATION_PATTERNS.DECIMAL.test(value)) {
    return `${fieldName} must be a valid decimal number`;
  }
  
  const num = parseFloat(value);
  
  if (min !== undefined && num < min) {
    return `${fieldName} must be at least ${min}`;
  }
  
  if (max !== undefined && num > max) {
    return `${fieldName} must be no more than ${max}`;
  }
  
  return null;
};

/**
 * Validate date field
 */
export const validateDate = (value: string, fieldName: string, futureOnly?: boolean): string | null => {
  if (!value?.trim()) {
    return `${fieldName} is required`;
  }
  
  const date = new Date(value);
  if (isNaN(date.getTime())) {
    return `${fieldName} must be a valid date`;
  }
  
  if (futureOnly && date <= new Date()) {
    return `${fieldName} must be in the future`;
  }
  
  return null;
};

/**
 * Validate field length
 */
export const validateLength = (value: string, fieldName: string, min?: number, max?: number): string | null => {
  if (!value) {
    return `${fieldName} is required`;
  }
  
  if (min !== undefined && value.length < min) {
    return `${fieldName} must be at least ${min} characters`;
  }
  
  if (max !== undefined && value.length > max) {
    return `${fieldName} must be no more than ${max} characters`;
  }
  
  return null;
};

/**
 * Validation schema type for complex validation rules
 */
export interface ValidationRule {
  required?: boolean;
  type?: 'email' | 'phone' | 'abn' | 'numeric' | 'decimal' | 'date';
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  futureOnly?: boolean;
  custom?: (value: unknown) => string | null;
}

/**
 * Generic field validator using validation rules
 */
export const validateField = (value: unknown, fieldName: string, rules: ValidationRule): string | null => {
  // Required validation
  if (rules.required && validateRequired(value, fieldName)) {
    return validateRequired(value, fieldName);
  }
  
  // Skip other validations if field is empty and not required
  if (!rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
    return null;
  }
  
  // Type-specific validation
  switch (rules.type) {
    case 'email':
      return validateEmail(value);
    case 'phone':
      return validateAustralianPhone(value);
    case 'abn':
      return validateABN(value);
    case 'numeric':
      return validateNumeric(value, fieldName, rules.min, rules.max);
    case 'decimal':
      return validateDecimal(value, fieldName, rules.min, rules.max);
    case 'date':
      return validateDate(value, fieldName, rules.futureOnly);
  }
  
  // Length validation
  if (typeof value === 'string') {
    const lengthError = validateLength(value, fieldName, rules.minLength, rules.maxLength);
    if (lengthError) return lengthError;
  }
  
  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }
  
  return null;
};