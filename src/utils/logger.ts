/**
 * Secure logger utility with data sanitization and environment-aware filtering
 * Prevents sensitive data exposure in production logs
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Sensitive field patterns to redact
const SENSITIVE_PATTERNS = [
  /password/i,
  /token/i,
  /secret/i,
  /key/i,
  /credentials/i,
  /auth/i,
  /bearer/i,
  /session/i
];

// Fields that should be completely redacted
const SENSITIVE_FIELDS = new Set([
  'password',
  'access_token',
  'refresh_token',
  'api_key',
  'secret',
  'credentials',
  'access_credentials',
  'bank_account_details',
  'encrypted_access_credentials'
]);

/**
 * Sanitizes sensitive data from log parameters
 */
const sanitize = (data: unknown): unknown => {
  if (typeof data === 'string') {
    // Check if string contains sensitive patterns
    if (SENSITIVE_PATTERNS.some(pattern => pattern.test(data))) {
      return '[REDACTED]';
    }
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitize(item));
  }

  if (data && typeof data === 'object') {
    const sanitized: Record<string, unknown> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (SENSITIVE_FIELDS.has(key.toLowerCase()) || 
          SENSITIVE_PATTERNS.some(pattern => pattern.test(key))) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = sanitize(value);
      }
    }
    
    return sanitized;
  }

  return data;
};

/**
 * Checks if logging should occur based on environment
 */
const shouldLog = (level: LogLevel): boolean => {
  const isProduction = import.meta.env.PROD;
  const logLevel = (import.meta.env.VITE_LOG_LEVEL as LogLevel) || 'info';
  
  if (isProduction && level === 'debug') {
    return false;
  }

  const levels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3
  };

  return levels[level] >= levels[logLevel];
};

const log = (level: LogLevel, message: string, ...optionalParams: unknown[]) => {
  if (!shouldLog(level)) return;
  
  const timestamp = new Date().toISOString();
  const sanitizedParams = optionalParams.map(param => sanitize(param));
  
  console[level === 'debug' ? 'log' : level](
    `[${timestamp}] [${level.toUpperCase()}] ${message}`, 
    ...sanitizedParams
  );
};

export const logger = {
  debug: (message: string, ...optionalParams: unknown[]) => {
    log('debug', message, ...optionalParams);
  },
  info: (message: string, ...optionalParams: unknown[]) => {
    log('info', message, ...optionalParams);
  },
  warn: (message: string, ...optionalParams: unknown[]) => {
    log('warn', message, ...optionalParams);
  },
  error: (message: string, ...optionalParams: unknown[]) => {
    log('error', message, ...optionalParams);
  },
}; 