/**
 * Validation utilities for AgreementsStep
 * 
 * This file contains validation functions moved from AgreementsStep.tsx
 * to follow the pattern of other steps and improve maintainability.
 */

import { FullOnboardingSessionData } from '@/types/onboarding';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validates business profile data (Step 1) for final submission
 */
export const validateBusinessProfileData = (
  step1Data?: FullOnboardingSessionData['step1_businessProfile'] | null
): ValidationResult => {
  const errors: string[] = [];
  
  if (!step1Data) {
    errors.push("Business Profile data (Step 1) is missing.");
    return { isValid: false, errors };
  }

  const bizReg = step1Data.businessRegistration;
  if (!bizReg) {
    errors.push("Step 1: Business registration details are missing.");
  } else {
    if (!bizReg.full_business_name?.trim()) {
      errors.push("Step 1: Full Business Name is required.");
    }
    if (!bizReg.abn?.trim()) {
      errors.push("Step 1: ABN is required.");
    }
    if (!bizReg.business_structure?.trim()) {
      errors.push("Step 1: Business Structure is required.");
    }
  }

  const contacts = step1Data.contacts;
  if (!contacts || contacts.length === 0) {
    errors.push("Step 1: At least one contact is required.");
  } else {
    const mainContact = contacts.find(c => c.contact_type === 'Main Contact');
    if (!mainContact) {
      errors.push("Step 1: A 'Main Contact' is required.");
    } else {
      if (!mainContact.name?.trim()) {
        errors.push("Step 1: Main Contact requires a name.");
      }
      if (!mainContact.email?.trim()) {
        errors.push("Step 1: Main Contact requires an email.");
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(mainContact.email)) {
          errors.push("Step 1: Main Contact email format is invalid.");
        }
      }
    }
  }

  const addresses = step1Data.addresses;
  if (!addresses || addresses.length === 0) {
    errors.push("Step 1: At least one address is required.");
  } else {
    const propertyAddress = addresses.find(a => a.address_type === 'Property');
    if (!propertyAddress) {
      errors.push("Step 1: A 'Property' address is required.");
    } else {
      if (!propertyAddress.full_address_text?.trim()) {
        errors.push("Step 1: Property address requires the full address text.");
      }
    }
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * Validates farm operations data (Step 2) for final submission
 */
export const validateFarmOperationsData = (
  step2Data?: FullOnboardingSessionData['step2_farmOperations'] | null
): ValidationResult => {
  const errors: string[] = [];
  
  if (!step2Data) {
    errors.push("Farm Operations data (Step 2) is missing.");
    return { isValid: false, errors };
  }

  if (!step2Data.activities || step2Data.activities.length === 0) {
    errors.push("Step 2: At least one farming activity is required.");
  }

  // Optional: Add more checks for key child records if needed
  // e.g., if a license type is "Other", ensure license_number is provided

  return { isValid: errors.length === 0, errors };
};

/**
 * Validates financial systems data (Step 3) for final submission
 */
export const validateFinancialSystemsData = (
  step3Data?: FullOnboardingSessionData['step3_financialSystems'] | null
): ValidationResult => {
  const errors: string[] = [];
  
  if (!step3Data) {
    errors.push("Financial Systems data (Step 3) is missing.");
    return { isValid: false, errors };
  }

  if (!step3Data.bookkeeping) {
    errors.push("Step 3: Bookkeeping record is missing.");
  }

  if (!step3Data.payroll) {
    errors.push("Step 3: Payroll record is missing.");
  }

  // Optional deeper checks
  if (step3Data.payroll?.is_payroll_processing_needed) {
    if (
      step3Data.payroll.employee_count === null || 
      step3Data.payroll.employee_count === undefined || 
      step3Data.payroll.employee_count <= 0
    ) {
      errors.push("Step 3: Number of employees is required if payroll processing is needed.");
    }
    if (!step3Data.payroll.current_payroll_software?.trim()) {
      errors.push("Step 3: Current payroll software is required if payroll processing is needed.");
    }
  }

  // Optional: Check if assetsData is not empty if asset entry is expected.
  // This might be too strict, depends on business logic.
  // if (!step3Data.assets || step3Data.assets.length === 0) {
  //     errors.push("Step 3: At least one asset is required in the asset registry.");
  // }

  return { isValid: errors.length === 0, errors };
};

/**
 * Validates Step 4 agreements and review data for final submission
 */
export const validateStep4ReviewData = (
  step4Data?: FullOnboardingSessionData['step4_agreements'] | null
): ValidationResult => {
  const errors: string[] = [];
  
  if (!step4Data) {
    errors.push("Agreements & Review data (Step 4) is missing.");
    return { isValid: false, errors };
  }

  if (
    step4Data.dataMigration?.primary_cloud_storage !== 'none' && 
    !step4Data.dataMigration?.primary_cloud_storage?.trim()
  ) {
    errors.push("Step 4: Primary Cloud Storage Provider selection is required in Data Migration (or select 'None').");
  }

  const commPrefs = step4Data.communicationPreferences;
  if (!commPrefs?.preferred_methods || commPrefs.preferred_methods.length === 0) {
    errors.push("Step 4: At least one Preferred Communication Method must be selected.");
  }
  if (!commPrefs?.reporting_frequency?.trim()) {
    errors.push("Step 4: Reporting Frequency is required in Communication Preferences.");
  }

  if (!step4Data.payments?.bank_account_details) {
    errors.push("Step 4: Bank Account Details are required for Payment Information.");
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * Validates terms agreement and signature requirements
 */
export const validateTermsAndSignature = (
  agreedToTerms: boolean,
  hasServiceAgreementSignature: boolean,
  serviceAgreementType: string
): ValidationResult => {
  const errors: string[] = [];

  if (!agreedToTerms) {
    errors.push("Step 4: You must agree to the Terms of Service and Privacy Policy.");
  }

  if (!hasServiceAgreementSignature) {
    errors.push(`Step 4: Please sign the ${serviceAgreementType}.`);
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * Performs comprehensive validation of all onboarding steps for final submission
 */
export const validateCompleteOnboarding = (
  sessionData: FullOnboardingSessionData | null,
  agreedToTerms: boolean,
  hasServiceAgreementSignature: boolean,
  serviceAgreementType: string
): ValidationResult => {
  const allErrors: string[] = [];

  // Validate each step
  const step1Validation = validateBusinessProfileData(sessionData?.step1_businessProfile);
  const step2Validation = validateFarmOperationsData(sessionData?.step2_farmOperations);
  const step3Validation = validateFinancialSystemsData(sessionData?.step3_financialSystems);
  const step4Validation = validateStep4ReviewData(sessionData?.step4_agreements);
  const termsValidation = validateTermsAndSignature(
    agreedToTerms,
    hasServiceAgreementSignature,
    serviceAgreementType
  );

  // Collect all errors
  allErrors.push(...step1Validation.errors);
  allErrors.push(...step2Validation.errors);
  allErrors.push(...step3Validation.errors);
  allErrors.push(...step4Validation.errors);
  allErrors.push(...termsValidation.errors);

  return { isValid: allErrors.length === 0, errors: allErrors };
};

/**
 * Groups validation errors by step for better user experience
 */
export const groupErrorsByStep = (errors: string[]): Record<string, string[]> => {
  return errors.reduce((acc, error) => {
    const stepMatch = error.match(/^Step (\d+):/);
    const step = stepMatch ? stepMatch[1] : 'General';
    if (!acc[step]) acc[step] = [];
    acc[step].push(error.replace(/^Step \d+:\s*/, ''));
    return acc;
  }, {} as Record<string, string[]>);
};

/**
 * Formats grouped errors into a user-friendly message
 */
export const formatValidationErrors = (errorsByStep: Record<string, string[]>): string => {
  return Object.entries(errorsByStep)
    .map(([step, errors]) => `**Step ${step}:**\n• ${errors.join('\n• ')}`)
    .join('\n\n');
};