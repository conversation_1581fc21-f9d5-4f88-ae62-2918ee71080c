/**
 * Utils Index
 * Centralized exports for all utility functions
 */

// Existing utilities (re-export for consistency)
export { logger } from './logger';

// Form validation utilities
export {
    validateFormField,
    validateForm,
    isFormValid,
    type ValidationRule,
    type ValidationConfig,
} from './form-validation';

// Form formatting utilities
export {
    formatPhoneNumber,
    formatAbn,
    formatAcn,
    formatCurrency,
    formatDateForInput,
    formatDateForDisplay,
    normalizePhoneNumber,
    normalizeBusinessNumber,
} from './form-formatters';

// Business constants and configurations
export {
    BUSINESS_STRUCTURES,
    CONTACT_TYPES,
    ADDRESS_TYPES,
    ACTIVITY_TYPES,
    LICENSE_TYPES,
    BAS_LODGEMENT_FREQUENCIES,
    ASSET_CATEGORIES,
    COMMUNICATION_METHODS,
    REPORTING_FREQUENCIES,
    VALIDATION_PATTERNS,
    DEFAULT_VALUES,
    FILE_UPLOAD_CONFIGS,
} from './business-constants';

// OnboardingContext helper utilities
export {
    getStepData,
    getStepId,
    isStepInitialized,
    getStepChildRecords,
    isSessionDataValid,
    getSessionId,
    getCurrentStep,
    getSessionStatus,
    hasRecord,
    countRecords,
    humanizeTableName,
    createErrorHandler,
    isPostgresError,
    formatPostgresError,
    STEP_DATA_KEYS,
    STEP_ENSURE_FUNCTIONS,
} from './onboarding-context-helpers';

// Export form formatters
export * from './form-formatters';

// Export form validation
export * from './form-validation';

// Export onboarding validation utilities
export * from './onboarding-validation'; 