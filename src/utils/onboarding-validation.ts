import { Database } from '@/types/database.types';
import {
    ValidationResult,
    StepValidationSummary,
    ArrayFieldTypes,
    ActivityTypeEnum,
    ContactTypeEnum,
    BusinessStructureEnum,
    LicenseTypeEnum,
    AssetCategoryEnum,
    CommunicationMethodEnum,
    ReportingFrequencyEnum,
    AgreementTypeEnum,
    BasFrequencyEnum,
    FullOnboardingSessionData
} from '@/types/onboarding';

// === ENUM VALIDATION ===
const ACTIVITY_TYPES: ActivityTypeEnum[] = [
    'Cropping', 'Livestock', 'Mixed Farming', 'Horticulture',
    'Dairy', 'Aquaculture', 'Forestry', 'Other'
];

const CONTACT_TYPES: ContactTypeEnum[] = [
    'Main Contact', 'Accountant', 'BAS Agent', 'Key Staff',
    'Admin', 'Accounts', 'Personal'
];

const BUSINESS_STRUCTURES: BusinessStructureEnum[] = [
    'Sole Trader', 'Company', 'Partnership', 'Trust',
    'Cooperative', 'Other'
];

const LICENSE_TYPES: LicenseTypeEnum[] = [
    'Chemical Permit', 'Machinery License', 'Food Safety Cert',
    'Water License', 'Heavy Vehicle License', 'Quad Bike License', 'Other'
];

const ASSET_CATEGORIES: AssetCategoryEnum[] = ['Vehicle', 'Equipment', 'Insurance'];

const COMMUNICATION_METHODS: CommunicationMethodEnum[] = ['Email', 'Phone', 'SMS', 'WhatsApp'];

const REPORTING_FREQUENCIES: ReportingFrequencyEnum[] = ['Weekly', 'Fortnightly', 'Monthly'];

const AGREEMENT_TYPES: AgreementTypeEnum[] = [
    'Service Agreement', 'Privacy Policy', 'Direct Debit', 'Terms and Conditions'
];

const BAS_FREQUENCIES: BasFrequencyEnum[] = ['Monthly', 'Quarterly', 'Annually'];

// === VALIDATION FUNCTIONS ===
export const validateEnumValue = <T extends keyof Database['farms']['Enums']>(
    enumType: T,
    value: string
): value is Database['farms']['Enums'][T] => {
    switch (enumType) {
        case 'activity_type_enum':
            return ACTIVITY_TYPES.includes(value as ActivityTypeEnum);
        case 'contact_type_enum':
            return CONTACT_TYPES.includes(value as ContactTypeEnum);
        case 'business_structure_enum':
            return BUSINESS_STRUCTURES.includes(value as BusinessStructureEnum);
        case 'license_type_enum':
            return LICENSE_TYPES.includes(value as LicenseTypeEnum);
        case 'asset_category_enum':
            return ASSET_CATEGORIES.includes(value as AssetCategoryEnum);
        case 'communication_method_enum':
            return COMMUNICATION_METHODS.includes(value as CommunicationMethodEnum);
        case 'reporting_frequency_enum':
            return REPORTING_FREQUENCIES.includes(value as ReportingFrequencyEnum);
        case 'agreement_type_enum':
            return AGREEMENT_TYPES.includes(value as AgreementTypeEnum);
        case 'bas_frequency_enum':
            return BAS_FREQUENCIES.includes(value as BasFrequencyEnum);
        default:
            return false;
    }
};

export const validateArrayField = <K extends keyof ArrayFieldTypes>(
    fieldName: K,
    value: ArrayFieldTypes[K]
): boolean => {
    if (!Array.isArray(value)) return false;

    switch (fieldName) {
        case 'preferred_methods':
            return value.length > 0 && value.every(method =>
                COMMUNICATION_METHODS.includes(method as CommunicationMethodEnum)
            );
        case 'crop_varieties':
        case 'document_categories':
            return true; // These are string arrays, no specific validation needed
        default:
            return false;
    }
};

// === FIELD-SPECIFIC VALIDATION ===
export const validateABN = (abn: string): boolean => {
    // Australian Business Number format: XX XXX XXX XXX
    return /^[0-9]{2}\s[0-9]{3}\s[0-9]{3}\s[0-9]{3}$/.test(abn);
};

export const validateACN = (acn: string): boolean => {
    // Australian Company Number format: XXX XXX XXX
    return /^[0-9]{3}\s[0-9]{3}\s[0-9]{3}$/.test(acn);
};

export const validateAustralianPhone = (phone: string): boolean => {
    // Australian phone number format: +61XXXXXXXXX
    return /^\+61\d{9}$/.test(phone);
};

export const validateEmail = (email: string): boolean => {
    return /^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+\.[A-Za-z]+$/.test(email);
};

export const validatePostcode = (postcode: string): boolean => {
    // Australian postcode: 4 digits
    return /^\d{4}$/.test(postcode);
};

// === CONDITIONAL VALIDATION ===
export interface AssetValidationRules {
    Vehicle: {
        required: ('registration_or_policy_number')[];
        optional: ('model_year' | 'serial_number' | 'purchase_date' | 'purchase_price')[];
    };
    Equipment: {
        required: ('registration_or_policy_number')[];
        optional: ('model_year' | 'serial_number' | 'purchase_date' | 'purchase_price')[];
    };
    Insurance: {
        required: ('policy_type' | 'renewal_date')[];
        optional: ('coverage_amount' | 'excess_amount')[];
    };
}

export const ASSET_VALIDATION_RULES: AssetValidationRules = {
    Vehicle: {
        required: ['registration_or_policy_number'],
        optional: ['model_year', 'serial_number', 'purchase_date', 'purchase_price']
    },
    Equipment: {
        required: ['registration_or_policy_number'],
        optional: ['model_year', 'serial_number', 'purchase_date', 'purchase_price']
    },
    Insurance: {
        required: ['policy_type', 'renewal_date'],
        optional: ['coverage_amount', 'excess_amount']
    }
};

export const validateAssetByCategory = (
    asset: Partial<Database['farms']['Tables']['assets']['Row']>
): ValidationResult => {
    const errors: string[] = [];
    const fieldErrors: Record<string, string[]> = {};

    if (!asset.asset_category) {
        errors.push('Asset category is required');
        return { isValid: false, errors, fieldErrors };
    }

    const rules = ASSET_VALIDATION_RULES[asset.asset_category];
    if (!rules) {
        errors.push('Invalid asset category');
        return { isValid: false, errors, fieldErrors };
    }

    // Check required fields
    rules.required.forEach(field => {
        if (!asset[field]) {
            const fieldName = field.replace(/_/g, ' ');
            errors.push(`${fieldName} is required for ${asset.asset_category} assets`);
            fieldErrors[field] = [`${fieldName} is required`];
        }
    });

    return {
        isValid: errors.length === 0,
        errors,
        fieldErrors
    };
};

// === DATE VALIDATION ===
export const validateDateRange = (
    startDate: string | null,
    endDate: string | null,
    fieldNames: { start: string; end: string }
): ValidationResult => {
    const errors: string[] = [];
    const fieldErrors: Record<string, string[]> = {};

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (end <= start) {
            const error = `${fieldNames.end} must be after ${fieldNames.start}`;
            errors.push(error);
            fieldErrors[fieldNames.end] = [error];
        }
    }

    return {
        isValid: errors.length === 0,
        errors,
        fieldErrors
    };
};

// === STEP VALIDATION ===
export const validateStep1 = (data: FullOnboardingSessionData['step1_businessProfile']): ValidationResult => {
    const errors: string[] = [];
    const fieldErrors: Record<string, string[]> = {};

    if (!data) {
        errors.push('Business profile data is missing');
        return { isValid: false, errors, fieldErrors };
    }

    // Validate business registration
    const businessReg = data.businessRegistration;
    if (!businessReg) {
        errors.push('Business registration is required');
    } else {
        if (!businessReg.full_business_name) {
            errors.push('Business name is required');
            fieldErrors.full_business_name = ['Business name is required'];
        }

        if (!businessReg.abn) {
            errors.push('ABN is required');
            fieldErrors.abn = ['ABN is required'];
        } else if (!validateABN(businessReg.abn)) {
            errors.push('Invalid ABN format');
            fieldErrors.abn = ['Invalid ABN format (should be XX XXX XXX XXX)'];
        }

        if (!businessReg.business_structure) {
            errors.push('Business structure is required');
            fieldErrors.business_structure = ['Business structure is required'];
        }
    }

    // Validate main contact
    const mainContact = data.contacts?.find(c => c.contact_type === 'Main Contact');
    if (!mainContact) {
        errors.push('Main contact is required');
    }

    return {
        isValid: errors.length === 0,
        errors,
        fieldErrors
    };
};

export const validateCompleteOnboarding = (data: FullOnboardingSessionData): StepValidationSummary => {
    const step1 = validateStep1(data.step1_businessProfile);

    // Placeholder validations for other steps
    const step2: ValidationResult = { isValid: true, errors: [], fieldErrors: {} };
    const step3: ValidationResult = { isValid: true, errors: [], fieldErrors: {} };
    const step4: ValidationResult = { isValid: true, errors: [], fieldErrors: {} };

    const overall: ValidationResult = {
        isValid: step1.isValid && step2.isValid && step3.isValid && step4.isValid,
        errors: [...step1.errors, ...step2.errors, ...step3.errors, ...step4.errors],
        fieldErrors: { ...step1.fieldErrors, ...step2.fieldErrors, ...step3.fieldErrors, ...step4.fieldErrors }
    };

    return {
        step1,
        step2,
        step3,
        step4,
        overall
    };
};

// === UTILITY FUNCTIONS ===
export const formatFieldName = (fieldName: string): string => {
    return fieldName
        .replace(/_/g, ' ')
        .replace(/\b\w/g, char => char.toUpperCase());
};

export const sanitizeFileName = (fileName: string): string => {
    return fileName
        .replace(/[^a-z0-9_.-]/gi, '_')
        .replace(/(\.\.)|(\/)/g, '')
        .substring(0, 100);
};

export const formatABN = (abn: string): string => {
    const cleaned = abn.replace(/\D/g, '');
    if (cleaned.length === 11) {
        return `${cleaned.substring(0, 2)} ${cleaned.substring(2, 5)} ${cleaned.substring(5, 8)} ${cleaned.substring(8, 11)}`;
    }
    return abn;
};

export const formatACN = (acn: string): string => {
    const cleaned = acn.replace(/\D/g, '');
    if (cleaned.length === 9) {
        return `${cleaned.substring(0, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6, 9)}`;
    }
    return acn;
};

export const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('61') && cleaned.length === 11) {
        return `+61 ${cleaned.substring(2, 3)} ${cleaned.substring(3, 7)} ${cleaned.substring(7, 11)}`;
    }
    if (cleaned.startsWith('0') && cleaned.length === 10) {
        return `+61 ${cleaned.substring(1, 2)} ${cleaned.substring(2, 6)} ${cleaned.substring(6, 10)}`;
    }
    return phone;
}; 