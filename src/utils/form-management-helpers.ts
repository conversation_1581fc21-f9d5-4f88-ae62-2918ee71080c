import { ValidationConfig } from './form-validation';

/**
 * Common types for form and entity management
 */
export type FormFieldValue = string | number | boolean | null | undefined;
export type FormData = Record<string, FormFieldValue>;
export type EntityId = string;
export type TableName = string;

/**
 * Common validation result type
 */
export type ValidationResult = string | null;

/**
 * Common error types
 */
export interface FieldError {
    field: string;
    message: string;
}

export interface EntityError {
    entityId: EntityId;
    message: string;
}

/**
 * Operation types for logging and state management
 */
export type EntityOperation = 'create' | 'update' | 'delete' | 'refresh';
export type FormOperation = 'save' | 'validate' | 'reset';

/**
 * Step column mapping for database tables
 * Maps table names to their corresponding step foreign key columns
 */
export const STEP_COLUMN_MAPPING: Record<string, string> = {
    // Step 1 tables
    'addresses': 'step_1_id',
    'contacts': 'step_1_id',
    'business_registration': 'step_1_id',
    'key_staff': 'step_1_id',

    // Step 2 tables
    'activities': 'step_2_id',
    'licenses': 'step_2_id',
    'suppliers': 'step_2_id',
    'contracts': 'step_2_id',
    'chemical_usage': 'step_2_id',

    // Step 3 tables
    'assets': 'step_3_id',
    'bookkeeping': 'step_3_id',
    'payroll': 'step_3_id',

    // Step 4 tables
    'agreements': 'step_4_id',
    'permissions': 'step_4_id',
    'payments': 'step_4_id',
    'data_migration': 'step_4_id',
    'communication_preferences': 'step_4_id',
};

/**
 * Get the step column name for a given table
 */
export function getStepColumnName(tableName: string): string {
    const columnName = STEP_COLUMN_MAPPING[tableName];
    if (!columnName) {
        throw new Error(`Unknown table name: ${tableName}. No step column mapping found.`);
    }
    return columnName;
}

/**
 * Default debounce delay for form operations (in milliseconds)
 */
export const DEFAULT_DEBOUNCE_MS = 1000;

/**
 * Default timeout for operations (in milliseconds)
 */
export const DEFAULT_TIMEOUT_MS = 30000;

/**
 * Common validation rules factory
 */
export const createValidationRules = {
    required: (message = 'This field is required'): ValidationConfig => ({
        rule: 'required',
        message,
    }),

    email: (message = 'Please enter a valid email address'): ValidationConfig => ({
        rule: 'email',
        message,
    }),

    phone: (message = 'Please enter a valid phone number'): ValidationConfig => ({
        rule: 'phone',
        message,
    }),

    minLength: (length: number, message?: string): ValidationConfig => ({
        rule: 'minLength',
        value: length,
        message: message || `Must be at least ${length} characters`,
    }),

    maxLength: (length: number, message?: string): ValidationConfig => ({
        rule: 'maxLength',
        value: length,
        message: message || `Must be no more than ${length} characters`,
    }),

    pattern: (regex: RegExp, message: string): ValidationConfig => ({
        rule: 'pattern',
        value: regex,
        message,
    }),
};

/**
 * Utility function to check if a value is empty
 */
export function isEmpty(value: unknown): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim().length === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
}

/**
 * Utility function to sanitize form data for database storage
 */
export function sanitizeFormData(data: FormData): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    Object.entries(data).forEach(([key, value]) => {
        // Convert empty strings to null for database storage
        if (value === '') {
            sanitized[key] = null;
        } else {
            sanitized[key] = value;
        }
    });

    return sanitized;
}

/**
 * Utility function to format field names for display
 */
export function formatFieldName(fieldName: string): string {
    return fieldName
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * Utility function to generate entity display names
 */
export function generateEntityDisplayName(tableName: string, singular = true): string {
    const baseNames: Record<string, string> = {
        'activities': 'Activity',
        'addresses': 'Address',
        'agreements': 'Agreement',
        'assets': 'Asset',
        'bookkeeping': 'Bookkeeping',
        'business_registration': 'Business Registration',
        'chemical_usage': 'Chemical Usage',
        'communication_preferences': 'Communication Preference',
        'contacts': 'Contact',
        'contracts': 'Contract',
        'data_migration': 'Data Migration',
        'key_staff': 'Key Staff',
        'licenses': 'License',
        'payments': 'Payment',
        'payroll': 'Payroll',
        'permissions': 'Permission',
        'suppliers': 'Supplier',
    };

    const baseName = baseNames[tableName] || formatFieldName(tableName);

    if (singular) {
        return baseName;
    }

    // Simple pluralization rules
    if (baseName.endsWith('y')) {
        return baseName.slice(0, -1) + 'ies';
    } else if (baseName.endsWith('s') || baseName.endsWith('x') || baseName.endsWith('ch')) {
        return baseName + 'es';
    } else {
        return baseName + 's';
    }
}

/**
 * Utility function to create constraint messages
 */
export function createConstraintMessage(
    entityDisplayName: string,
    constraint: 'min' | 'max',
    count: number
): string {
    const plural = count !== 1;
    const entityName = plural ? generateEntityDisplayName(entityDisplayName, false) : entityDisplayName;

    if (constraint === 'min') {
        return `You must have at least ${count} ${entityName.toLowerCase()}.`;
    } else {
        return `You can only have up to ${count} ${entityName.toLowerCase()}.`;
    }
}

/**
 * Utility function to deep compare objects for dirty state checking
 */
export function deepEqual(obj1: unknown, obj2: unknown): boolean {
    if (obj1 === obj2) return true;

    if (obj1 == null || obj2 == null) return false;

    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;

    const keys1 = Object.keys(obj1 as Record<string, unknown>);
    const keys2 = Object.keys(obj2 as Record<string, unknown>);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
        if (!keys2.includes(key)) return false;

        const val1 = (obj1 as Record<string, unknown>)[key];
        const val2 = (obj2 as Record<string, unknown>)[key];

        if (!deepEqual(val1, val2)) return false;
    }

    return true;
}

/**
 * Utility function to create logging context for operations
 */
export function createLogContext(
    operation: EntityOperation | FormOperation,
    context: {
        tableName?: string;
        entityId?: string;
        stepId?: string;
        fieldName?: string;
        entityDisplayName?: string;
        [key: string]: unknown;
    }
): Record<string, unknown> {
    return {
        operation,
        timestamp: new Date().toISOString(),
        ...context,
    };
}

/**
 * Utility function to handle async operations with error catching
 */
export async function handleAsyncOperation<T>(
    operation: () => Promise<T>,
    errorContext: {
        operationName: string;
        entityDisplayName?: string;
        [key: string]: unknown;
    }
): Promise<{ success: boolean; data?: T; error?: string }> {
    try {
        const data = await operation();
        return { success: true, data };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : `Failed to ${errorContext.operationName}`;
        return { success: false, error: errorMessage };
    }
}

/**
 * Type guard to check if an entity has an ID
 */
export function hasEntityId<T>(entity: T): entity is T & { id: string } {
    return typeof entity === 'object' && entity !== null && 'id' in entity && typeof (entity as Record<string, unknown>).id === 'string';
}

/**
 * Utility to create default entity data with step linkage
 */
export function createEntityWithStepLink<T>(
    defaultEntity: Omit<T, 'id'>,
    stepId: string,
    tableName: string
): T & Record<string, unknown> {
    const stepColumnName = getStepColumnName(tableName);
    return {
        ...defaultEntity,
        [stepColumnName]: stepId,
    } as T & Record<string, unknown>;
} 