/**
 * Form Validation Utility
 * Centralized validation logic for common field types
 */

export type ValidationRule =
    | 'required'
    | 'email'
    | 'phone'
    | 'abn'
    | 'acn'
    | 'numeric'
    | 'positive'
    | 'minLength'
    | 'maxLength';

export interface ValidationConfig {
    rule: ValidationRule;
    value?: number; // for minLength, maxLength
    message?: string;
}

// Regex patterns for validation
const PATTERNS = {
    email: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
    phone: /^\+61[2-9]\d{8}$/,
    abn: /^\d{11}$/,
    acn: /^\d{9}$/,
} as const;

// Default error messages - enhanced for accessibility
const DEFAULT_MESSAGES = {
    required: 'This field is required. Please provide a value.',
    email: 'Please enter a valid email address in the format: <EMAIL>',
    phone: 'Please enter a valid Australian phone number in the format: +61X XXXX XXXX',
    abn: 'ABN must be exactly 11 digits. Please check your entry.',
    acn: 'ACN must be exactly 9 digits. Please check your entry.',
    numeric: 'Please enter a valid number. Only numeric values are allowed.',
    positive: 'Please enter a positive number greater than zero.',
    minLength: 'Input is too short. Please enter more characters.',
    maxLength: 'Input is too long. Please reduce the number of characters.',
} as const;

/**
 * Validates a single field against a set of validation rules
 * @param value - The value to validate
 * @param rules - Array of validation configurations
 * @returns Error message string or null if valid
 */
export function validateFormField(
    value: unknown,
    rules: ValidationConfig[]
): string | null {
    for (const config of rules) {
        const { rule, value: ruleValue, message } = config;
        const errorMessage = message || DEFAULT_MESSAGES[rule];

        switch (rule) {
            case 'required':
                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    return errorMessage;
                }
                break;

            case 'email':
                if (value && !PATTERNS.email.test(String(value).toLowerCase())) {
                    return errorMessage;
                }
                break;

            case 'phone':
                if (value) {
                    const normalized = String(value).replace(/\s/g, '');
                    if (!PATTERNS.phone.test(normalized)) {
                        return errorMessage;
                    }
                }
                break;

            case 'abn':
                if (value) {
                    const digits = String(value).replace(/\D/g, '');
                    if (!PATTERNS.abn.test(digits)) {
                        return errorMessage;
                    }
                }
                break;

            case 'acn':
                if (value) {
                    const digits = String(value).replace(/\D/g, '');
                    if (!PATTERNS.acn.test(digits)) {
                        return errorMessage;
                    }
                }
                break;

            case 'numeric':
                if (value && isNaN(Number(value))) {
                    return errorMessage;
                }
                break;

            case 'positive':
                if (value && Number(value) <= 0) {
                    return errorMessage;
                }
                break;

            case 'minLength':
                if (value && String(value).length < (ruleValue || 0)) {
                    return message || `Minimum length is ${ruleValue} characters`;
                }
                break;

            case 'maxLength':
                if (value && String(value).length > (ruleValue || 0)) {
                    return message || `Maximum length is ${ruleValue} characters`;
                }
                break;

            default:
                console.warn(`Unknown validation rule: ${rule}`);
        }
    }

    return null;
}

/**
 * Validates multiple fields against their respective rules
 * @param formData - Object containing form field values
 * @param schema - Object mapping field names to their validation rules
 * @returns Object containing field names mapped to error messages
 */
export function validateForm<T extends Record<string, unknown>>(
    formData: T,
    schema: Record<keyof T, ValidationConfig[]>
): Partial<Record<keyof T, string>> {
    const errors: Partial<Record<keyof T, string>> = {};

    for (const [fieldName, rules] of Object.entries(schema)) {
        const value = formData[fieldName];
        const error = validateFormField(value, rules as ValidationConfig[]);
        if (error) {
            errors[fieldName as keyof T] = error;
        }
    }

    return errors;
}

/**
 * Checks if a form has any validation errors
 * @param errors - Errors object from validateForm
 * @returns True if form is valid (no errors)
 */
export function isFormValid<T>(errors: Partial<Record<keyof T, string>>): boolean {
    return Object.values(errors).every(error => !error);
} 