/**
 * Business Constants
 * Centralized constants for business forms and validation
 */

// Business structure options
export const BUSINESS_STRUCTURES = [
    { value: 'Sole Trader', label: 'Sole Trader' },
    { value: 'Partnership', label: 'Partnership' },
    { value: 'Company', label: 'Company' },
    { value: 'Trust', label: 'Trust' },
] as const;

// Contact type options
export const CONTACT_TYPES = [
    { value: 'Main Contact', label: 'Main Contact' },
    { value: 'Accountant', label: 'Accountant' },
    { value: 'BAS Agent', label: 'BAS Agent' },
    { value: 'Key Staff', label: 'Key Staff' },
] as const;

// Address type options
export const ADDRESS_TYPES = [
    { value: 'Property', label: 'Property Address' },
    { value: 'Postal', label: 'Postal Address' },
] as const;

// Farm activity types
export const ACTIVITY_TYPES = [
    { value: 'Cropping', label: 'Cropping' },
    { value: 'Livestock', label: 'Livestock' },
    { value: 'Mixed Farming', label: 'Mixed Farming' },
    { value: 'Horticulture', label: 'Horticulture' },
    { value: 'Dairy', label: 'Dairy' },
    { value: 'Aquaculture', label: 'Aquaculture' },
    { value: 'Forestry', label: 'Forestry' },
    { value: 'Other', label: 'Other' },
] as const;

// Common license types
export const LICENSE_TYPES = [
    { value: 'Chemical Permit', label: 'Chemical Permit' },
    { value: 'Machinery License', label: 'Machinery License' },
    { value: 'Food Safety Cert', label: 'Food Safety Certification' },
    { value: 'Water License', label: 'Water License/Allocation' },
    { value: 'Organic Certification', label: 'Organic Certification' },
    { value: 'Heavy Vehicle License', label: 'Heavy Vehicle License' },
    { value: 'Other', label: 'Other (Specify)' },
] as const;

// BAS lodgement frequencies
export const BAS_LODGEMENT_FREQUENCIES = [
    { value: 'Monthly', label: 'Monthly' },
    { value: 'Quarterly', label: 'Quarterly' },
    { value: 'Annually', label: 'Annually' },
] as const;

// Asset categories
export const ASSET_CATEGORIES = [
    { value: 'Vehicle', label: 'Vehicle' },
    { value: 'Equipment', label: 'Equipment' },
    { value: 'Insurance', label: 'Insurance' },
] as const;

// Communication preferences
export const COMMUNICATION_METHODS = [
    { value: 'Email', label: 'Email' },
    { value: 'Phone', label: 'Phone' },
    { value: 'SMS', label: 'SMS' },
    { value: 'Post', label: 'Post' },
] as const;

export const REPORTING_FREQUENCIES = [
    { value: 'Weekly', label: 'Weekly' },
    { value: 'Monthly', label: 'Monthly' },
    { value: 'Quarterly', label: 'Quarterly' },
    { value: 'Annually', label: 'Annually' },
] as const;

// Common validation patterns
export const VALIDATION_PATTERNS = {
    phone: /^\+61[2-9]\d{8}$/,
    email: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
    abn: /^\d{11}$/,
    acn: /^\d{9}$/,
} as const;

// Default values for common entities
export const DEFAULT_VALUES = {
    contact: {
        contact_type: 'Main Contact',
        email: '',
        name: '',
    },
    address: {
        address_type: 'Property',
        full_address_text: '',
    },
    activity: {
        activity_type: 'Cropping',
    },
    license: {
        license_type: 'Other',
    },
    supplier: {
        supplier_name: '',
        contact_details: null,
        services_provided: null,
    },
    contract: {
        contract_description: '',
    },
    chemicalUsage: {
        product_name: '',
    },
} as const;

// File upload configurations
export const FILE_UPLOAD_CONFIGS = {
    license: {
        allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
        maxSize: 10 * 1024 * 1024, // 10MB
        description: 'Upload a copy of this license/certification (PDF, JPG, PNG - max 10MB)',
    },
    assetsCsv: {
        allowedTypes: ['text/csv', 'application/csv'],
        maxSize: 5 * 1024 * 1024, // 5MB
        description: 'CSV headers should match: asset_category, asset_type, make_or_provider, registration_or_policy_number, renewal_date',
    },
} as const; 