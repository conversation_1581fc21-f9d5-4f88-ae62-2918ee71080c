/**
 * Form Field Formatters
 * Utility functions for formatting user input in real-time
 */

/**
 * Formats a phone number to Australian format (+61 X XXXX XXXX)
 * @param value - Raw phone number input
 * @returns Formatted phone number string
 */
export function formatPhoneNumber(value: string): string {
    if (!value) return '';

    // Remove all non-digits
    const digits = value.replace(/\D/g, '');

    // Handle different input formats
    if (digits.startsWith('61') && digits.length >= 3) {
        // International format starting with 61
        const localPart = digits.substring(2);
        let formatted = '+61';
        if (localPart.length > 0) {
            formatted += ' ' + localPart.substring(0, Math.min(1, localPart.length));
            if (localPart.length > 1) {
                formatted += ' ' + localPart.substring(1, Math.min(5, localPart.length));
                if (localPart.length > 5) {
                    formatted += ' ' + localPart.substring(5, Math.min(9, localPart.length));
                }
            }
        }
        return formatted.trim();
    } else if (digits.startsWith('0') && digits.length >= 2) {
        // Local format starting with 0 - convert to international
        const localPart = digits.substring(1); // Remove leading 0
        let formatted = '+61';
        if (localPart.length > 0) {
            formatted += ' ' + localPart.substring(0, Math.min(1, localPart.length));
            if (localPart.length > 1) {
                formatted += ' ' + localPart.substring(1, Math.min(5, localPart.length));
                if (localPart.length > 5) {
                    formatted += ' ' + localPart.substring(5, Math.min(9, localPart.length));
                }
            }
        }
        return formatted.trim();
    } else if (value.startsWith('+61')) {
        // Already in international format - just clean up spacing
        return value.replace(/(\+61)(\d{1})(\d{4})(\d{4})/, '$1 $2 $3 $4');
    }

    // Return as-is for partial inputs or manual formatting
    return value;
}

/**
 * Formats ABN (Australian Business Number) with spaces
 * @param value - Raw ABN input
 * @returns Formatted ABN string (XX XXX XXX XXX)
 */
export function formatAbn(value: string): string {
    const digits = value.replace(/\D/g, '');
    if (digits.length === 0) return '';

    let formatted = '';
    if (digits.length > 0) formatted += digits.substring(0, 2);
    if (digits.length > 2) formatted += ' ' + digits.substring(2, 5);
    if (digits.length > 5) formatted += ' ' + digits.substring(5, 8);
    if (digits.length > 8) formatted += ' ' + digits.substring(8, 11);

    return formatted.trim();
}

/**
 * Formats ACN (Australian Company Number) with spaces
 * @param value - Raw ACN input
 * @returns Formatted ACN string (XXX XXX XXX)
 */
export function formatAcn(value: string): string {
    const digits = value.replace(/\D/g, '');
    if (digits.length === 0) return '';

    let formatted = '';
    if (digits.length > 0) formatted += digits.substring(0, 3);
    if (digits.length > 3) formatted += ' ' + digits.substring(3, 6);
    if (digits.length > 6) formatted += ' ' + digits.substring(6, 9);

    return formatted.trim();
}

/**
 * Formats a currency value for display
 * @param value - Numeric value
 * @param currency - Currency code (default: AUD)
 * @returns Formatted currency string
 */
export function formatCurrency(value: number | string, currency: string = 'AUD'): string {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    if (isNaN(numericValue)) return '';

    return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: currency,
    }).format(numericValue);
}

/**
 * Formats a date for input fields (YYYY-MM-DD)
 * @param date - Date object or ISO string
 * @returns Formatted date string for input field
 */
export function formatDateForInput(date: Date | string | null): string {
    if (!date) return '';

    try {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return dateObj.toISOString().split('T')[0];
    } catch (error) {
        console.error('Error formatting date for input:', error);
        return '';
    }
}

/**
 * Formats a date for display
 * @param date - Date object or ISO string
 * @returns Formatted date string for display
 */
export function formatDateForDisplay(date: Date | string | null): string {
    if (!date) return '';

    try {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return dateObj.toLocaleDateString('en-AU', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    } catch (error) {
        console.error('Error formatting date for display:', error);
        return '';
    }
}

/**
 * Strips formatting from a phone number to get clean digits
 * @param formattedPhone - Formatted phone number
 * @returns Clean phone number for database storage
 */
export function normalizePhoneNumber(formattedPhone: string): string {
    return formattedPhone.replace(/\s/g, '');
}

/**
 * Removes formatting from ABN/ACN
 * @param formatted - Formatted ABN or ACN
 * @returns Clean digits only
 */
export function normalizeBusinessNumber(formatted: string): string {
    return formatted.replace(/\D/g, '');
} 