/**
 * Common validation patterns and utilities for onboarding forms
 */

// Regular expression patterns for common validations
export const VALIDATION_PATTERNS = {
  ABN: /^\d{11}$/,
  ACN: /^\d{9}$/,
  PHONE: /^(\+61|0)[2-9]\d{8}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  NUMERIC: /^\d+$/,
  DECIMAL: /^\d+(\.\d{1,2})?$/,
} as const;

/**
 * Format Australian phone number to standard format
 * Returns format: +61XXXXXXXXX (no spaces, matches database constraint)
 */
export const formatAustralianPhone = (value: string): string => {
  if (!value) return '';

  // Remove all non-digit characters
  const cleaned = value.replace(/\D/g, '');

  // Handle different input formats
  if (cleaned.startsWith('61')) {
    return `+${cleaned}`;
  }
  if (cleaned.startsWith('0') && cleaned.length === 10) {
    return `+61${cleaned.substring(1)}`;
  }
  if (cleaned.length === 9) {
    return `+61${cleaned}`;
  }

  // Return with + prefix if it looks like it might be a phone number
  if (cleaned.length > 8 && cleaned.length < 12) {
    return cleaned.startsWith('61') ? `+${cleaned}` : `+61${cleaned}`;
  }

  return value; // Return original if can't format
};

/**
 * Format Australian phone number for display (with spaces)
 * Returns format: +61 X XXXX XXXX for better readability
 */
export const formatAustralianPhoneDisplay = (value: string): string => {
  if (!value) return '';

  // First format to standard
  const formatted = formatAustralianPhone(value);
  
  // If it's a valid formatted number, add spaces for display
  if (formatted.startsWith('+61') && formatted.length === 12) {
    const number = formatted.substring(3); // Remove +61
    // Format as +61 X XXXX XXXX
    return `+61 ${number.charAt(0)} ${number.substring(1, 5)} ${number.substring(5)}`;
  }
  
  return formatted;
};

/**
 * Format ABN for display (XX XXX XXX XXX)
 */
export const formatABNDisplay = (value: string): string => {
  if (!value) return '';

  const cleaned = value.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `${cleaned.substring(0, 2)} ${cleaned.substring(2, 5)} ${cleaned.substring(5, 8)} ${cleaned.substring(8, 11)}`;
  }
  return value;
};

/**
 * Clean ABN input (remove formatting, keep only digits)
 */
export const formatABNInput = (value: string): string => {
  if (!value) return '';
  return value.replace(/\D/g, '').substring(0, 11);
};

/**
 * Validate Australian Business Number (ABN)
 */
export const validateABN = (value: string): string | null => {
  if (!value?.trim()) {
    return 'ABN is required';
  }

  const cleaned = value.replace(/\D/g, '');

  if (!VALIDATION_PATTERNS.ABN.test(cleaned)) {
    return 'ABN must be 11 digits';
  }

  // ABN checksum validation
  const weights = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
  const digits = cleaned.split('').map(Number);

  // Subtract 1 from the first digit
  digits[0] -= 1;

  // Calculate checksum
  const sum = digits.reduce((acc, digit, index) => acc + (digit * weights[index]), 0);

  if (sum % 89 !== 0) {
    return 'Please enter a valid ABN';
  }

  return null;
};

/**
 * Validate Australian phone number
 */
export const validatePhone = (value: string): string | null => {
  if (!value?.trim()) {
    return null; // Phone is typically optional
  }

  const formatted = formatAustralianPhone(value);
  if (!VALIDATION_PATTERNS.PHONE.test(formatted.replace(/\+/, ''))) {
    return 'Please enter a valid Australian phone number';
  }

  return null;
};

/**
 * Validate email address
 */
export const validateEmail = (value: string): string | null => {
  if (!value?.trim()) {
    return null; // Email validation - caller determines if required
  }

  if (!VALIDATION_PATTERNS.EMAIL.test(value)) {
    return 'Please enter a valid email address';
  }

  return null;
};

/**
 * Validate required field
 */
export const validateRequired = (value: unknown, fieldName?: string): string | null => {
  if (!value || (typeof value === 'string' && !value.trim())) {
    return `${fieldName || 'This field'} is required`;
  }
  return null;
};

/**
 * Validate numeric field
 */
export const validateNumeric = (value: string, fieldName?: string, min?: number, max?: number): string | null => {
  if (!value?.trim()) {
    return null;
  }

  if (!VALIDATION_PATTERNS.NUMERIC.test(value)) {
    return `${fieldName || 'This field'} must be a number`;
  }

  const num = parseInt(value, 10);

  if (min !== undefined && num < min) {
    return `${fieldName || 'This field'} must be at least ${min}`;
  }

  if (max !== undefined && num > max) {
    return `${fieldName || 'This field'} must be no more than ${max}`;
  }

  return null;
};

/**
 * Validate decimal field
 */
export const validateDecimal = (value: string, fieldName?: string, min?: number, max?: number): string | null => {
  if (!value?.trim()) {
    return null;
  }

  if (!VALIDATION_PATTERNS.DECIMAL.test(value)) {
    return `${fieldName || 'This field'} must be a valid decimal number`;
  }

  const num = parseFloat(value);

  if (min !== undefined && num < min) {
    return `${fieldName || 'This field'} must be at least ${min}`;
  }

  if (max !== undefined && num > max) {
    return `${fieldName || 'This field'} must be no more than ${max}`;
  }

  return null;
};

/**
 * Format ACN for display (XXX XXX XXX)
 */
export const formatACNDisplay = (value: string): string => {
  if (!value) return '';

  const cleaned = value.replace(/\D/g, '');
  if (cleaned.length === 9) {
    return `${cleaned.substring(0, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6, 9)}`;
  }
  return value;
};

/**
 * Clean ACN input (remove formatting, keep only digits)
 */
export const formatACNInput = (value: string): string => {
  if (!value) return '';
  return value.replace(/\D/g, '').substring(0, 9);
};

/**
 * Validate Australian Company Number (ACN)
 */
export const validateACN = (value: string): string | null => {
  if (!value?.trim()) {
    return null; // ACN is optional
  }

  const cleaned = value.replace(/\D/g, '');

  if (!VALIDATION_PATTERNS.ACN.test(cleaned)) {
    return 'ACN must be 9 digits';
  }

  // ACN checksum validation
  const weights = [8, 7, 6, 5, 4, 3, 2, 1];
  const digits = cleaned.split('').map(Number);
  const checkDigit = digits[8];

  // Calculate sum of first 8 digits multiplied by weights
  const sum = digits.slice(0, 8).reduce((acc, digit, index) => acc + (digit * weights[index]), 0);

  // Calculate remainder and complement
  const remainder = sum % 10;
  const complement = remainder === 0 ? 0 : 10 - remainder;

  if (complement !== checkDigit) {
    return 'Please enter a valid ACN';
  }

  return null;
};

// Formatter functions for common field types
export const formatters = {
  abn: formatABNInput,
  abnDisplay: formatABNDisplay,
  acn: formatACNInput,
  acnDisplay: formatACNDisplay,
  phone: formatAustralianPhone,
  phoneDisplay: formatAustralianPhoneDisplay,
  uppercase: (value: string) => value?.toUpperCase() || '',
  lowercase: (value: string) => value?.toLowerCase() || '',
  trim: (value: string) => value?.trim() || '',
} as const;

// Validator functions for common field types
export const validators = {
  abn: validateABN,
  acn: validateACN,
  phone: validatePhone,
  email: validateEmail,
  required: (fieldName?: string) => (value: unknown) => validateRequired(value, fieldName),
  numeric: (fieldName?: string, min?: number, max?: number) => (value: string) => validateNumeric(value, fieldName, min, max),
  decimal: (fieldName?: string, min?: number, max?: number) => (value: string) => validateDecimal(value, fieldName, min, max),
} as const;

/**
 * Validation rule type for use with form components
 */
export interface ValidationRule {
  required?: boolean;
  pattern?: RegExp;
  custom?: (value: unknown) => string | null;
  message?: string;
}

/**
 * Apply validation rules to a value
 */
export const applyValidationRules = (value: unknown, rules: ValidationRule[]): string | null => {
  for (const rule of rules) {
    if (rule.required && validateRequired(value)) {
      return rule.message || 'This field is required';
    }

    if (rule.pattern && value && !rule.pattern.test(String(value))) {
      return rule.message || 'Invalid format';
    }

    if (rule.custom) {
      const customResult = rule.custom(value);
      if (customResult) {
        return customResult;
      }
    }
  }

  return null;
};