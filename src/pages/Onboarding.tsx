import { StepIndicator } from "@/components/onboarding/StepIndicator";
import { BusinessProfileStep } from "@/components/onboarding/steps/BusinessProfileStep";
import { FarmOperationsStep } from "@/components/onboarding/steps/FarmOperationsStep";
import { FinancialSystemsStep } from "@/components/onboarding/steps/FinancialSystemsStep";
import { AgreementsStep } from "@/components/onboarding/steps/AgreementsStep";
import Header from "@/components/landing/Header";
import { Card, CardContent } from "@/components/ui/card";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { OnboardingNavigation } from "@/components/onboarding/OnboardingNavigation";
import { Button } from "@/components/ui/button";

/**
 * `STEPS` defines the configuration for each step in the onboarding wizard.
 * Each object contains:
 * - `id`: A unique numerical identifier for the step.
 * - `title`: The display title for the step, shown in the `StepIndicator`.
 * - `component`: The React component to be rendered for this step.
 */
const STEPS = [
  { id: 1, title: "Business Profile", component: BusinessProfileStep },
  { id: 2, title: "Farm Operations", component: FarmOperationsStep },
  { id: 3, title: "Financial Systems", component: FinancialSystemsStep },
  { id: 4, title: "Agreements & Review", component: AgreementsStep },
];

/**
 * `Onboarding` is the main page component for the multi-step onboarding wizard.
 * It orchestrates the display of different steps, manages navigation between them,
 * and handles the overall loading and error states of the onboarding process using `useOnboarding` context.
 */
const Onboarding = () => {
  const {
    loading, // Indicates if the OnboardingContext is busy loading initial session data.
    currentStep, // The active step number (1-indexed), managed by OnboardingContext and persisted in `onboarding_sessions.current_step`.
    updateCurrentStep, // Function from OnboardingContext to change the current step in the backend and context state.
    sessionData, // Holds all data for the current onboarding session, fetched via `get_onboarding_session_data` RPC.
    error // Global error messages from OnboardingContext (e.g., failure to load session).
  } = useOnboarding();

  // Display a global loading indicator if the context is loading and no session data is yet available.
  // This typically happens on initial page load while fetching or creating the onboarding session.
  if (loading && !sessionData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
        <p className="ml-4 text-lg">Loading your session...</p>
      </div>
    );
  }

  // Display a global error message if the OnboardingContext encountered a critical error (e.g., failed to load session).
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center text-red-700">
          <h2 className="text-2xl font-bold mb-2">An Error Occurred</h2>
          <p>{error}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Reload Page
          </Button>
        </div>
      </div>
    );
  }

  /**
   * `handleNext` is called when the user clicks the "Continue" or "Complete Setup" button.
   * It advances the wizard to the next step using `updateCurrentStep` from the context.
   * If it's the last step, it should trigger final submission logic (currently a console log).
   */
  const handleNext = () => {
    if (currentStep < STEPS.length) {
      updateCurrentStep(currentStep + 1);
    } else {
      // TODO: Implement final submission logic.
      // This would typically involve:
      // 1. Marking the `onboarding_sessions.status` as 'submitted' or 'completed'.
      // 2. Potentially calling an Edge Function to trigger post-onboarding workflows (e.g., notifications, data handoff).
      // 3. Navigating the user to a confirmation page or dashboard.
      console.log("Onboarding complete! Session Data:", sessionData);
      // Example: supabase.from('onboarding_sessions').update({ status: 'completed' }).eq('id', sessionData?.onboardingSession?.id);
    }
  };

  /**
   * `handleBack` is called when the user clicks the "Back" button.
   * It navigates to the previous step using `updateCurrentStep` from the context.
   */
  const handleBack = () => {
    if (currentStep > 1) {
      updateCurrentStep(currentStep - 1);
    }
  };

  // Dynamically determines which step component to render based on `currentStep`.
  const CurrentStepComponent = STEPS[currentStep - 1]?.component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-secondary/5">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* `StepIndicator` visually shows the user's progress through the defined STEPS. */}
          <StepIndicator
            steps={STEPS.map(step => ({ id: step.id, title: step.title }))}
            currentStep={currentStep}
          />

          <Card className="mt-8">
            <CardContent className="p-8">
              {/* Renders the component for the current active step. */}
              {CurrentStepComponent ? <CurrentStepComponent /> : <p>Step not found.</p>}
              {/* `OnboardingNavigation` provides the "Back" and "Next/Complete" buttons. */}
              {/* Its `canGoNext` prop will eventually be driven by step-specific validation logic. */}
              <OnboardingNavigation
                currentStep={currentStep}
                totalSteps={STEPS.length}
                onNext={handleNext}
                onBack={handleBack}
                canGoBack={currentStep > 1}
                canGoNext={true} // TODO: Implement step-specific validation logic to control this.
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
