import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import AuthForm from '@/components/auth/AuthForm';

const Auth: React.FC = () => {
  const [mode, setMode] = useState<'login' | 'register' | 'forgot-password'>('login');
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!loading && user) {
      const params = new URLSearchParams(location.search);
      const redirectTo = params.get('redirectTo');
      navigate(redirectTo || '/');
    }
  }, [user, loading, navigate, location.search]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-emerald-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-emerald-50 p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-lg shadow-lg p-8 animate-fade-in">
          <AuthForm mode={mode} onModeChange={setMode} />
        </div>
      </div>
    </div>
  );
};

export default Auth;
