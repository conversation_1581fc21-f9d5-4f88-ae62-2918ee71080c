
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import <PERSON> from "@/components/landing/Hero";
import ProblemSection from "@/components/landing/ProblemSection";
import SolutionSection from "@/components/landing/SolutionSection";
import HowItWorks from "@/components/landing/HowItWorks";
import SocialProof from "@/components/landing/SocialProof";
import PricingSection from "@/components/landing/PricingSection";
import ContactForm from "@/components/landing/ContactForm";
import Footer from "@/components/landing/Footer";
import Header from "@/components/landing/Header";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      <Header />
      <Hero />
      <ProblemSection />
      <SolutionSection />
      <HowItWorks />
      <SocialProof />
      <PricingSection />
      <ContactForm />
      <Footer />
    </div>
  );
};

export default Index;
