/**
 * Type-safe database table name constants
 * These constants ensure we're using valid table names that exist in the database schema
 */

import { Database } from '@/types/database.types';

// Extract the table names from the database schema
type FarmsSchema = Database['farms'];
type TableName = keyof FarmsSchema['Tables'];

// Type-safe table name constants
export const TABLE_NAMES = {
  // Onboarding session tables
  ONBOARDING_SESSIONS: 'onboarding_sessions',
  
  // Step parent tables
  STEP_1_BUSINESS_PROFILE: 'step_1_business_profile',
  STEP_2_FARM_OPERATIONS: 'step_2_farm_operations',
  STEP_3_FINANCIAL_SYSTEMS: 'step_3_financial_systems',
  STEP_4_AGREEMENTS: 'step_4_agreements',
  
  // Step 1 child tables
  BUSINESS_REGISTRATION: 'business_registration',
  CONTACTS: 'contacts',
  ADDRESSES: 'addresses',
  KEY_STAFF: 'key_staff',
  
  // Step 2 child tables
  ACTIVITIES: 'activities',
  LICENSES: 'licenses',
  SUPPLIERS: 'suppliers',
  CONTRACTS: 'contracts',
  CHEMICAL_USAGE: 'chemical_usage',
  
  // Step 3 child tables
  BOOKKEEPING: 'bookkeeping',
  PAYROLL: 'payroll',
  ASSETS: 'assets',
  
  // Step 4 child tables
  DATA_MIGRATION: 'data_migration',
  PERMISSIONS: 'permissions',
  AGREEMENTS: 'agreements',
  PAYMENTS: 'payments',
  COMMUNICATION_PREFERENCES: 'communication_preferences',
  
  // Shared tables
  DOCUMENTS: 'documents',
} as const satisfies Record<string, TableName>;

// Type guard to check if a string is a valid table name
export function isValidTableName(tableName: string): tableName is TableName {
  return (Object.values(TABLE_NAMES) as string[]).includes(tableName);
}

// Helper type to get the table name type
export type ValidTableName = typeof TABLE_NAMES[keyof typeof TABLE_NAMES];