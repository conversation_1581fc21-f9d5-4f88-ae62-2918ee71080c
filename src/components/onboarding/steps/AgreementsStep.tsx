import React, { useState } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { StepLoadingState, StepErrorState } from '@/components/ui/step-status';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BusinessInfoSection } from './review/BusinessInfoSection';
import { FarmOpsReviewSection } from './review/FarmOpsReviewSection';
import { FinancialsReviewSection } from './review/FinancialsReviewSection';
import { TermsAgreementSection } from './review/TermsAgreementSection';
import { PaymentInfoSection } from './review/PaymentInfoSection';
import { DigitalSignatureSection } from './review/DigitalSignatureSection';
import { DataMigrationSection } from './review/DataMigrationSection';
import { CommunicationPreferencesSection } from './review/CommunicationPreferencesSection';
import { SubmitButton } from './review/SubmitButton';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/utils/logger';
import { Database } from '@/types/database.types';
import { 
  validateCompleteOnboarding, 
  groupErrorsByStep, 
  formatValidationErrors 
} from '@/utils/agreements-validation';

// Database type aliases for entity management
type FarmsSchema = Database['farms'];
type AgreementInsert = FarmsSchema['Tables']['agreements']['Insert'];
type AgreementUpdate = FarmsSchema['Tables']['agreements']['Update'];

const SERVICE_AGREEMENT_TYPE = "Service Agreement";

/**
 * Refactored AgreementsStep using consolidated hook system
 * Following BusinessProfileStep patterns for consistency and stability
 * 
 * Key improvements:
 * - Uses useEntityListManagement for agreements entities
 * - Moves validation logic to utility functions
 * - Eliminates manual child record initialization in favor of hook-provided states
 * - Proper database state management through consolidated hooks
 * - Enhanced error handling and loading states
 */
export const AgreementsStep = () => {
    const {
        sessionData,
        loading: contextLoading,
        sessionId,
        ensureStep4AgreementsRecordExists,
        ensureDataMigrationRecordExists,
        ensureCommunicationPreferencesRecordExists,
        ensurePaymentRecordExists,
        submitOnboarding,
        isSubmittingSession,
        invokeProcessDigitalSignature,
        isProcessingSignature,
    } = useOnboarding();
    const { toast } = useToast();

    const [agreedToTerms, setAgreedToTerms] = useState(false);

    // Enhanced step initialization
    const {
        isStepInitializing,
        initializationError,
        handleRetryInitialization,
        isLoading,
        effectiveStepId,
    } = useEnhancedStepInit({
        stepName: 'Agreements',
        sessionId,
        contextLoading,
        existingStepId: sessionData?.step4_agreements?.id,
        ensureStepFunction: ensureStep4AgreementsRecordExists,
    });

    // Base disabled state for all operations
    const baseDisabled = !effectiveStepId || contextLoading || isStepInitializing;

    // Agreements Management using consolidated hook
    const agreementsManager = useEntityListManagement<
        { id: string; agreement_type: string; signature_storage_path: string | null },
        AgreementInsert,
        AgreementUpdate
    >({
        stepId: effectiveStepId,
        tableName: 'agreements',
        initialEntities: sessionData?.step4_agreements?.agreements || [],
        createDefaultEntity: (stepId) => ({
            step_4_id: stepId,
            agreement_type: SERVICE_AGREEMENT_TYPE,
            signature_storage_path: null,
        }),
        entityDisplayName: 'agreement',
        disabled: baseDisabled,
        showNotifications: false, // Agreements are managed internally
    });

    // Helper functions for signature management
    const hasServiceAgreementSignatureBeenProvided = !!agreementsManager.entities.find(
        agreement => agreement.agreement_type === SERVICE_AGREEMENT_TYPE && agreement.signature_storage_path
    );

    const handleConfirmServiceAgreementSignature = async (signatureDataUrl: string) => {
        if (!effectiveStepId || isProcessingSignature) return;
        await invokeProcessDigitalSignature(SERVICE_AGREEMENT_TYPE, signatureDataUrl);
    };

    const handleFormSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();

        // Prevent submission if already in progress or not ready
        if (isSubmittingSession || isProcessingSignature || !effectiveStepId) {
            logger.warn("Form submission blocked: Already in progress or data not ready", {
                isSubmittingSession,
                isProcessingSignature,
                effectiveStepId: !!effectiveStepId
            });
            return;
        }

        try {
            // Use consolidated validation utility
            const validation = validateCompleteOnboarding(
                sessionData,
                agreedToTerms,
                hasServiceAgreementSignatureBeenProvided,
                SERVICE_AGREEMENT_TYPE
            );

            if (!validation.isValid) {
                logger.warn("Client-side validation failed before submission:", {
                    step: 'AgreementsStep',
                    operation: 'form_submission_validation',
                    validationErrors: validation.errors,
                    context: {
                        sessionDataExists: !!sessionData,
                        step1_businessProfile_exists: !!sessionData?.step1_businessProfile,
                        step2_farmOperations_exists: !!sessionData?.step2_farmOperations,
                        step3_financialSystems_exists: !!sessionData?.step3_financialSystems,
                        step4_agreements_exists: !!sessionData?.step4_agreements,
                        agreedToTerms,
                        hasServiceAgreementSignatureBeenProvided,
                    }
                });

                // Group errors by step for better user experience
                const errorsByStep = groupErrorsByStep(validation.errors);
                const formattedErrorMessage = formatValidationErrors(errorsByStep);

                toast({
                    title: "Incomplete Information",
                    description: `Please address the following issues before submitting:\n\n${formattedErrorMessage}`,
                    variant: "destructive",
                    duration: 15000, // Longer duration for multiple errors
                });
                return;
            }

            // Proceed with submission
            const result = await submitOnboarding();

            if (result.success) {
                setAgreedToTerms(false); // Reset for potential re-submission or navigation
                logger.info("Onboarding submission successful", {
                    step: 'AgreementsStep',
                    operation: 'form_submission_success',
                    sessionId: sessionData?.onboardingSession?.id
                });
            } else {
                // Detailed error logging for submission failures
                logger.error("Onboarding submission failed", {
                    step: 'AgreementsStep',
                    operation: 'form_submission_failure',
                    error: result.error,
                    errors: result.errors,
                    sessionId: sessionData?.onboardingSession?.id
                });
            }
            // Error handling for submitOnboarding is done within the useOnboarding hook and will show its own toast

        } catch (unexpectedError: unknown) {
            const errorMessage = unexpectedError instanceof Error
                ? unexpectedError.message
                : 'An unexpected error occurred during submission';

            logger.error("Unexpected error during form submission:", {
                error: unexpectedError,
                step: 'AgreementsStep',
                operation: 'form_submission_unexpected_error',
                sessionId: sessionData?.onboardingSession?.id
            });

            toast({
                title: "Submission Error",
                description: `An unexpected error occurred: ${errorMessage}`,
                variant: "destructive",
                duration: 10000
            });
        }
    };

    const uiDisabled = contextLoading || isStepInitializing || isSubmittingSession || isProcessingSignature;

    // Loading state
    if (isLoading && !initializationError) {
        return <StepLoadingState stepName="Agreements & Review" message="Loading agreements & review essentials..." />;
    }

    // Error state
    if (initializationError) {
        return (
            <StepErrorState
                stepName="Agreements & Review"
                error={initializationError}
                onRetry={handleRetryInitialization}
                isRetrying={isStepInitializing}
            />
        );
    }

    return (
        <form onSubmit={handleFormSubmit} className="space-y-8">
            <div>
                <h2 className="text-2xl font-bold">Step 4: Agreements & Review</h2>
                <p className="text-muted-foreground">
                    Please review all your information carefully. Once submitted, you may not be able to make changes through this wizard.
                </p>
            </div>

            {/* Business Profile Review */}
            {sessionData?.step1_businessProfile ? (
                <BusinessInfoSection step1Data={sessionData.step1_businessProfile} />
            ) : (
                <Card>
                    <CardHeader><CardTitle>Business Profile Review</CardTitle></CardHeader>
                    <CardContent><p className="text-sm text-muted-foreground">Loading business profile data for review...</p></CardContent>
                </Card>
            )}

            {/* Farm Operations Review */}
            {sessionData?.step2_farmOperations ? (
                <FarmOpsReviewSection step2Data={sessionData.step2_farmOperations} />
            ) : (
                <Card>
                    <CardHeader><CardTitle>Farm Operations Review</CardTitle></CardHeader>
                    <CardContent><p className="text-sm text-muted-foreground">Loading farm operations data for review or data may be missing from Step 2.</p></CardContent>
                </Card>
            )}

            {/* Financial Systems Review */}
            {sessionData?.step3_financialSystems ? (
                <FinancialsReviewSection step3Data={sessionData.step3_financialSystems} />
            ) : (
                <Card>
                    <CardHeader><CardTitle>Financial Systems Review</CardTitle></CardHeader>
                    <CardContent><p className="text-sm text-muted-foreground">Loading financial systems data for review or data may be missing from Step 3.</p></CardContent>
                </Card>
            )}

            {/* Finalization Sections */}
            {effectiveStepId && (
                <div className="space-y-6 pt-4 border-t">
                    <h3 className="text-xl font-semibold">Finalize & Submit</h3>
                    <DataMigrationSection
                        step4Id={effectiveStepId}
                        dataMigrationData={sessionData?.step4_agreements?.dataMigration}
                        permissionsData={sessionData?.step4_agreements?.permissions || []}
                        disabled={uiDisabled}
                    />
                    <CommunicationPreferencesSection
                        step4Id={effectiveStepId}
                        communicationPreferencesData={sessionData?.step4_agreements?.communicationPreferences}
                        disabled={uiDisabled}
                    />
                    <PaymentInfoSection
                        step4Id={effectiveStepId}
                        paymentData={sessionData?.step4_agreements?.payments}
                        disabled={uiDisabled}
                    />
                    <TermsAgreementSection
                        agreedToTerms={agreedToTerms}
                        onAgreedToTermsChange={setAgreedToTerms}
                        disabled={uiDisabled}
                    />
                    <DigitalSignatureSection
                        agreementTypeLabel={SERVICE_AGREEMENT_TYPE}
                        onConfirmSignature={handleConfirmServiceAgreementSignature}
                        isProcessing={isProcessingSignature}
                        disabled={uiDisabled}
                        hasSignatureBeenProvided={hasServiceAgreementSignatureBeenProvided}
                    />
                    <SubmitButton
                        isSubmitting={isSubmittingSession}
                        disabled={!agreedToTerms || !effectiveStepId || uiDisabled || !hasServiceAgreementSignatureBeenProvided}
                    />
                </div>
            )}
        </form>
    );
}; 