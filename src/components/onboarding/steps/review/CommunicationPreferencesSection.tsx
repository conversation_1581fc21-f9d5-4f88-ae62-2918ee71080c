import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Edit, MessageSquare, Clock, Phone, Mail, MessageCircle, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { useDebounce } from '@/hooks/useDebounce';
import { FullOnboardingSessionData, CommunicationPreferences, CommunicationMethodEnum } from '@/types/onboarding';
import { Database } from '@/types/database.types';
import { cn } from '@/utils/utils';

// Type aliases for component use
type CommunicationPreferencesRow = CommunicationPreferences;
type TableInsert<T extends keyof Database['farms']['Tables']> = Database['farms']['Tables'][T]['Insert'];

// Enhanced communication methods with icons and descriptions
const COMMUNICATION_METHODS = [
    {
        id: 'Email' as CommunicationMethodEnum,
        label: 'Email',
        icon: Mail,
        description: 'Receive updates and reports via email',
        recommended: true
    },
    {
        id: 'Phone' as CommunicationMethodEnum,
        label: 'Phone Call',
        icon: Phone,
        description: 'Direct phone contact for urgent matters',
        recommended: false
    },
    {
        id: 'SMS' as CommunicationMethodEnum,
        label: 'SMS/Text Message',
        icon: MessageCircle,
        description: 'Quick notifications via text message',
        recommended: false
    },
];

const REPORTING_FREQUENCIES = [
    { value: 'weekly', label: 'Weekly', description: 'Every week on Monday' },
    { value: 'fortnightly', label: 'Fortnightly', description: 'Every two weeks' },
    { value: 'monthly', label: 'Monthly', description: 'First Monday of each month' },
    { value: 'quarterly', label: 'Quarterly', description: 'Every three months' },
    { value: 'as_needed', label: 'As Needed', description: 'Only when necessary' },
];

interface CommunicationPreferencesSectionProps {
    step4Id?: string | null;
    communicationPreferencesData?: CommunicationPreferencesRow | null;
    disabled?: boolean;
    onEdit?: () => void;
    className?: string;
}

interface ValidationState {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    completionRate: number;
}

/**
 * Enhanced CommunicationPreferencesSection component with comprehensive form management
 * 
 * Features:
 * - Advanced form validation with real-time feedback
 * - Debounced auto-save for better performance
 * - Mobile-responsive design with accessibility compliance
 * - Visual indicators for communication preferences
 * - Type-safe implementation with proper error handling
 * - Optimistic UI updates with error recovery
 */
export const CommunicationPreferencesSection = memo<CommunicationPreferencesSectionProps>(({
    step4Id,
    communicationPreferencesData,
    disabled = false,
    onEdit,
    className,
}) => {
    const { upsertRecord, loading: contextLoading, ensureLinkedRecord } = useOnboarding();
    const { toast } = useToast();
    const isMobile = useIsMobile();

    // Local state with proper typing
    const [selectedMethods, setSelectedMethods] = useState<CommunicationMethodEnum[]>(
        communicationPreferencesData?.preferred_methods || []
    );
    const [preferredContactTimes, setPreferredContactTimes] = useState<string>(
        communicationPreferencesData?.preferred_contact_times || ''
    );
    const [reportingFrequency, setReportingFrequency] = useState<string>(
        communicationPreferencesData?.reporting_frequency || ''
    );
    const [isSaving, setIsSaving] = useState(false);
    const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);

    // Debounced contact times for auto-save
    const debouncedContactTimes = useDebounce(preferredContactTimes, 1500);

    // Validation state
    const validationState = useMemo<ValidationState>(() => {
        const errors: string[] = [];
        const warnings: string[] = [];

        if (selectedMethods.length === 0) {
            errors.push('At least one communication method must be selected');
        }

        if (!reportingFrequency) {
            errors.push('Reporting frequency is required');
        }

        if (selectedMethods.includes('Phone' as CommunicationMethodEnum) && !preferredContactTimes?.trim()) {
            warnings.push('Consider specifying preferred contact times for phone calls');
        }

        if (selectedMethods.includes('SMS' as CommunicationMethodEnum) && !preferredContactTimes?.trim()) {
            warnings.push('Consider specifying preferred times for SMS notifications');
        }

        const totalFields = 3; // methods, frequency, contact times (optional)
        const completedFields = [
            selectedMethods.length > 0 ? 1 : 0,
            reportingFrequency ? 1 : 0,
            preferredContactTimes?.trim() ? 1 : 0.5 // Optional field worth half
        ].reduce((sum, val) => sum + val, 0);

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completionRate: Math.round((completedFields / totalFields) * 100)
        };
    }, [selectedMethods, reportingFrequency, preferredContactTimes]);

    // Update local state when props change
    useEffect(() => {
        setSelectedMethods(communicationPreferencesData?.preferred_methods || []);
        setPreferredContactTimes(communicationPreferencesData?.preferred_contact_times || '');
        setReportingFrequency(communicationPreferencesData?.reporting_frequency || '');
    }, [communicationPreferencesData]);

    // Auto-save contact times when debounced value changes
    useEffect(() => {
        if (debouncedContactTimes !== (communicationPreferencesData?.preferred_contact_times || '')) {
            handleSingleFieldSave('preferred_contact_times', debouncedContactTimes);
        }
    }, [debouncedContactTimes, communicationPreferencesData?.preferred_contact_times]);

    const handleSingleFieldSave = useCallback(async (
        field: keyof CommunicationPreferencesRow,
        value: string | null
    ) => {
        if (!step4Id || disabled || contextLoading || isSaving) return;

        setIsSaving(true);
        try {
            const record = await ensureLinkedRecord({
                tableName: 'communication_preferences',
                linkCondition: { step_4_id: step4Id },
                defaultValues: { step_4_id: step4Id, [field]: value },
                existingRecordFromSession: communicationPreferencesData,
            });

            if (record?.id && communicationPreferencesData?.[field] !== value) {
                const baseData = { id: record.id };
                const updateData = field === 'preferred_contact_times'
                    ? { ...baseData, preferred_contact_times: value as string }
                    : { ...baseData, reporting_frequency: value as Database['farms']['Enums']['reporting_frequency_enum'] };
                await upsertRecord('communication_preferences', updateData as TableInsert<'communication_preferences'>);

                setLastSaveTime(new Date());

                toast({
                    title: "Preference Saved",
                    description: `${field === 'preferred_contact_times' ? 'Contact times' : 'Reporting frequency'} updated successfully.`,
                    duration: 2000,
                });
            } else if (!record) {
                throw new Error("Failed to ensure communication preferences record.");
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            toast({
                title: "Error Saving Preference",
                description: errorMessage,
                variant: "destructive",
                duration: 5000
            });
        } finally {
            setIsSaving(false);
        }
    }, [step4Id, disabled, contextLoading, isSaving, ensureLinkedRecord, communicationPreferencesData, upsertRecord, toast]);

    const handlePreferredMethodsChange = useCallback(async (methodId: string, checked: boolean) => {
        if (!step4Id || disabled || contextLoading || isSaving) return;

        const newMethods = checked
            ? [...selectedMethods, methodId as CommunicationMethodEnum]
            : selectedMethods.filter(m => m !== methodId);

        // Optimistic update for UI responsiveness
        setSelectedMethods(newMethods);
        setIsSaving(true);

        try {
            const record = await ensureLinkedRecord({
                tableName: 'communication_preferences',
                linkCondition: { step_4_id: step4Id },
                defaultValues: { step_4_id: step4Id, preferred_methods: newMethods },
                existingRecordFromSession: communicationPreferencesData,
            });

            if (record?.id) {
                await upsertRecord('communication_preferences', {
                    id: record.id,
                    preferred_methods: newMethods
                } as TableInsert<'communication_preferences'>);

                setLastSaveTime(new Date());

                toast({
                    title: "Communication Methods Updated",
                    description: `${checked ? 'Added' : 'Removed'} ${methodId} as a communication method.`,
                    duration: 2000,
                });
            } else if (!record) {
                throw new Error("Failed to ensure communication preferences record for methods.");
            }
        } catch (error) {
            // Revert optimistic update if save fails
            setSelectedMethods(communicationPreferencesData?.preferred_methods || []);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            toast({
                title: "Error Saving Communication Methods",
                description: errorMessage,
                variant: "destructive",
                duration: 5000
            });
        } finally {
            setIsSaving(false);
        }
    }, [step4Id, disabled, contextLoading, isSaving, selectedMethods, ensureLinkedRecord, communicationPreferencesData, upsertRecord, toast]);

    const isOperationDisabled = disabled || contextLoading || isSaving;

    return (
        <Card className={cn("transition-all duration-200", className)}>
            <CardHeader className="pb-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                    <div className="flex-1">
                        <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
                            <MessageSquare className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
                            <span>Communication Preferences</span>
                            <div className="flex items-center gap-2">
                                <Badge
                                    variant={validationState.isValid ? "default" : "destructive"}
                                    className="text-xs"
                                >
                                    {validationState.completionRate}% Complete
                                </Badge>
                                {validationState.warnings.length > 0 && (
                                    <Badge variant="outline" className="text-xs">
                                        {validationState.warnings.length} Suggestion{validationState.warnings.length > 1 ? 's' : ''}
                                    </Badge>
                                )}
                            </div>
                        </CardTitle>
                        <CardDescription className="mt-2">
                            Configure how and when you prefer to receive communications and reports from us.
                            {!validationState.isValid && (
                                <span className="block mt-1 text-destructive font-medium">
                                    {validationState.errors.length} required field{validationState.errors.length > 1 ? 's' : ''} missing
                                </span>
                            )}
                        </CardDescription>
                    </div>

                    {onEdit && (
                        <Button
                            variant="outline"
                            size={isMobile ? "sm" : "default"}
                            onClick={onEdit}
                            disabled={isOperationDisabled}
                            className="flex-shrink-0"
                            aria-label="Edit communication preferences"
                        >
                            <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
                            <span className="hidden sm:inline">Edit Preferences</span>
                            <span className="sm:hidden">Edit</span>
                        </Button>
                    )}
                </div>
            </CardHeader>

            <CardContent className="space-y-8">
                {/* Communication Methods Section */}
                <section aria-labelledby="communication-methods-heading">
                    <div className="space-y-4">
                        <div>
                            <h3 id="communication-methods-heading" className="text-base font-medium flex items-center gap-2">
                                <MessageSquare className="w-4 h-4 text-primary" aria-hidden="true" />
                                Preferred Communication Methods
                                <span className="text-destructive ml-1" aria-label="required field">*</span>
                            </h3>
                            <p className="text-sm text-muted-foreground mt-1">
                                Select how you'd like us to contact you. You can choose multiple methods.
                            </p>
                        </div>

                        <div className="grid gap-3">
                            {COMMUNICATION_METHODS.map((method) => {
                                const IconComponent = method.icon;
                                const isSelected = selectedMethods.includes(method.id);

                                return (
                                    <div
                                        key={method.id}
                                        className={cn(
                                            "flex items-start space-x-3 p-4 rounded-lg border transition-colors",
                                            isSelected
                                                ? "bg-primary/5 border-primary/20"
                                                : "bg-background border-border",
                                            "hover:bg-primary/10"
                                        )}
                                    >
                                        <Checkbox
                                            id={`comm-method-${method.id}`}
                                            checked={isSelected}
                                            onCheckedChange={(checked) => handlePreferredMethodsChange(method.id, !!checked)}
                                            disabled={isOperationDisabled}
                                            className="mt-1"
                                            aria-describedby={`comm-method-desc-${method.id}`}
                                        />
                                        <div className="flex-1 grid gap-2">
                                            <div className="flex items-center gap-2">
                                                <IconComponent className="w-4 h-4 text-primary" aria-hidden="true" />
                                                <Label
                                                    htmlFor={`comm-method-${method.id}`}
                                                    className="font-medium cursor-pointer"
                                                >
                                                    {method.label}
                                                </Label>
                                                {method.recommended && (
                                                    <Badge variant="outline" className="text-xs">
                                                        Recommended
                                                    </Badge>
                                                )}
                                                {isSelected && (
                                                    <CheckCircle className="w-4 h-4 text-green-600" aria-hidden="true" />
                                                )}
                                            </div>
                                            <p id={`comm-method-desc-${method.id}`} className="text-sm text-muted-foreground">
                                                {method.description}
                                            </p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        {selectedMethods.length === 0 && (
                            <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
                                <p className="text-sm text-destructive flex items-center gap-2">
                                    <AlertCircle className="w-4 h-4" aria-hidden="true" />
                                    Please select at least one communication method.
                                </p>
                            </div>
                        )}
                    </div>
                </section>

                {/* Contact Times Section */}
                <section aria-labelledby="contact-times-heading">
                    <div className="space-y-3">
                        <Label htmlFor="preferredContactTimes" className="text-base font-medium flex items-center gap-2">
                            <Clock className="w-4 h-4 text-primary" aria-hidden="true" />
                            Preferred Contact Times
                            <span className="text-muted-foreground font-normal ml-2">(Optional)</span>
                        </Label>
                        <Input
                            id="preferredContactTimes"
                            value={preferredContactTimes}
                            onChange={(e) => setPreferredContactTimes(e.target.value)}
                            placeholder="e.g., Weekdays 9 AM - 5 PM AEST, avoid lunch hours (12-1 PM)"
                            disabled={isOperationDisabled}
                            maxLength={150}
                            className="transition-colors"
                            aria-describedby="contact-times-description"
                        />
                        <div className="flex justify-between items-center">
                            <p id="contact-times-description" className="text-sm text-muted-foreground">
                                Specify when you're most available for phone calls or time-sensitive communications.
                            </p>
                            <span className="text-xs text-muted-foreground">
                                {preferredContactTimes.length}/150
                            </span>
                        </div>
                    </div>
                </section>

                {/* Reporting Frequency Section */}
                <section aria-labelledby="reporting-frequency-heading">
                    <div className="space-y-3">
                        <Label htmlFor="reportingFrequency" className="text-base font-medium">
                            Reporting Frequency
                            <span className="text-destructive ml-1" aria-label="required field">*</span>
                        </Label>
                        <Select
                            value={reportingFrequency}
                            onValueChange={(value) => {
                                setReportingFrequency(value);
                                handleSingleFieldSave('reporting_frequency', value);
                            }}
                            disabled={isOperationDisabled}
                            name="reportingFrequency"
                            required
                        >
                            <SelectTrigger
                                id="reportingFrequency"
                                className={cn(
                                    "transition-colors",
                                    !reportingFrequency && "border-destructive/50"
                                )}
                                aria-describedby="reporting-frequency-description"
                            >
                                <SelectValue placeholder="Select how often you'd like to receive reports..." />
                            </SelectTrigger>
                            <SelectContent>
                                {REPORTING_FREQUENCIES.map(option => (
                                    <SelectItem key={option.value} value={option.value}>
                                        <div className="flex flex-col">
                                            <span className="font-medium">{option.label}</span>
                                            <span className="text-xs text-muted-foreground">{option.description}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <p id="reporting-frequency-description" className="text-sm text-muted-foreground">
                            This determines how often you'll receive progress reports and updates about your account.
                        </p>
                    </div>
                </section>

                {/* Status Indicator */}
                {(isSaving || lastSaveTime) && (
                    <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        {isSaving ? (
                            <div className="flex items-center text-sm text-muted-foreground">
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" aria-hidden="true" />
                                <span>Saving preferences...</span>
                            </div>
                        ) : lastSaveTime && (
                            <div className="flex items-center text-sm text-green-700">
                                <CheckCircle className="w-4 h-4 mr-2" aria-hidden="true" />
                                <span>
                                    Last saved at {lastSaveTime.toLocaleTimeString()}
                                </span>
                            </div>
                        )}
                    </div>
                )}

                {/* Validation Summary */}
                {validationState.errors.length > 0 && (
                    <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
                        <h4 className="font-medium text-destructive mb-2 flex items-center gap-2">
                            <AlertCircle className="w-4 h-4" aria-hidden="true" />
                            Required Information Missing
                        </h4>
                        <ul className="text-sm text-destructive space-y-1">
                            {validationState.errors.map((error, index) => (
                                <li key={index} className="flex items-center gap-2">
                                    <span className="w-1 h-1 bg-destructive rounded-full" aria-hidden="true" />
                                    {error}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}

                {/* Suggestions */}
                {validationState.warnings.length > 0 && (
                    <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
                        <h4 className="font-medium text-yellow-800 mb-2">
                            Suggestions
                        </h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                            {validationState.warnings.map((warning, index) => (
                                <li key={index} className="flex items-center gap-2">
                                    <span className="w-1 h-1 bg-yellow-600 rounded-full" aria-hidden="true" />
                                    {warning}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </CardContent>
        </Card>
    );
});

CommunicationPreferencesSection.displayName = 'CommunicationPreferencesSection';