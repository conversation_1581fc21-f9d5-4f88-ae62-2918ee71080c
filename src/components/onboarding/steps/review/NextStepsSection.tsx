import React, { memo, useState, useCallback } from 'react';
import { Mail, CheckCircle, Clock, Users, Settings, ChevronRight, Phone, Calendar, ArrowRight, Spark<PERSON>, Star } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/utils/utils";

interface NextStepItemProps {
  stepNumber: number;
  title: string;
  description: string;
  timeline?: string;
  icon?: React.ElementType;
  isCompleted?: boolean;
  isCurrent?: boolean;
  isExpanded?: boolean;
  onToggle?: () => void;
  details?: string[];
}

interface NextStepsSectionProps {
  className?: string;
  showTimeline?: boolean;
  showExpandableDetails?: boolean;
  currentStep?: number;
  completedSteps?: number[];
}

const NextStepItem = memo<NextStepItemProps>(({ 
  stepNumber, 
  title, 
  description, 
  timeline, 
  icon: Icon = ChevronRight,
  isCompleted = false,
  isCurrent = false,
  isExpanded = false,
  onToggle,
  details = []
}) => {
  const isMobile = useIsMobile();
  
  return (
    <div className={cn(
      "group transition-all duration-200 rounded-lg p-4 border",
      isCompleted && "bg-green-50/50 border-green-200",
      isCurrent && "bg-blue-50/50 border-blue-200 shadow-sm",
      !isCompleted && !isCurrent && "bg-background border-border hover:bg-muted/30",
      onToggle && "cursor-pointer"
    )}
    onClick={onToggle}
    role={onToggle ? "button" : undefined}
    aria-expanded={onToggle ? isExpanded : undefined}
    tabIndex={onToggle ? 0 : undefined}
    onKeyDown={onToggle ? (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onToggle();
      }
    } : undefined}
    >
      <div className="flex items-start space-x-3">
        {/* Step Number/Status Indicator */}
        <div className={cn(
          "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-colors",
          isCompleted && "bg-green-600 text-white",
          isCurrent && "bg-blue-600 text-white",
          !isCompleted && !isCurrent && "bg-muted text-muted-foreground"
        )}>
          {isCompleted ? (
            <CheckCircle className="w-4 h-4" aria-hidden="true" />
          ) : (
            <span>{stepNumber}</span>
          )}
        </div>
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className={cn(
            "flex items-start justify-between",
            isMobile && onToggle ? "flex-col gap-2" : "flex-row"
          )}>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                {Icon && <Icon className="w-4 h-4 text-primary flex-shrink-0" aria-hidden="true" />}
                <h4 className={cn(
                  "font-medium transition-colors",
                  isCompleted && "text-green-700",
                  isCurrent && "text-blue-700",
                  !isCompleted && !isCurrent && "text-foreground"
                )}>
                  {title}
                </h4>
                {timeline && (
                  <Badge variant="outline" className="text-xs">
                    <Clock className="w-3 h-3 mr-1" aria-hidden="true" />
                    {timeline}
                  </Badge>
                )}
              </div>
              <p className={cn(
                "text-sm leading-relaxed",
                isCompleted && "text-green-600",
                isCurrent && "text-blue-600",
                !isCompleted && !isCurrent && "text-muted-foreground"
              )}>
                {description}
              </p>
            </div>
            
            {onToggle && (
              <ChevronRight className={cn(
                "w-4 h-4 text-muted-foreground transition-transform flex-shrink-0",
                isExpanded && "rotate-90",
                isMobile && "self-start mt-1"
              )} aria-hidden="true" />
            )}
          </div>
          
          {/* Expanded Details */}
          {isExpanded && details.length > 0 && (
            <div className="mt-3 pl-6 border-l-2 border-muted">
              <ul className="space-y-2">
                {details.map((detail, index) => (
                  <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                    <ArrowRight className="w-3 h-3 mt-0.5 flex-shrink-0" aria-hidden="true" />
                    <span>{detail}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
      
      {/* Progress Line (except for last item) */}
      <div className="ml-4 mt-2">
        <div className={cn(
          "w-0.5 h-4 transition-colors",
          isCompleted && "bg-green-200",
          isCurrent && "bg-blue-200",
          !isCompleted && !isCurrent && "bg-border"
        )} />
      </div>
    </div>
  );
});

NextStepItem.displayName = 'NextStepItem';

/**
 * Enhanced NextStepsSection component with improved user experience patterns
 * 
 * Features:
 * - Interactive expandable step details
 * - Progress tracking with visual indicators
 * - Mobile-responsive design with accessibility compliance
 * - Timeline information and status tracking
 * - Smooth animations and transitions
 * - Contact information and support links
 */
export const NextStepsSection = memo<NextStepsSectionProps>(({ 
  className,
  showTimeline = true,
  showExpandableDetails = true,
  currentStep = 0,
  completedSteps = []
}) => {
  const isMobile = useIsMobile();
  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set());
  
  const toggleStep = useCallback((stepNumber: number) => {
    if (!showExpandableDetails) return;
    
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepNumber)) {
        newSet.delete(stepNumber);
      } else {
        newSet.add(stepNumber);
      }
      return newSet;
    });
  }, [showExpandableDetails]);
  
  const steps = [
    {
      number: 1,
      title: "Application Review",
      description: "Our team will review your submitted information and may contact you if any clarifications are needed.",
      timeline: "1-2 business days",
      icon: CheckCircle,
      details: [
        "Automated validation of all submitted forms",
        "Manual review by our onboarding specialists",
        "Verification of business registration and licenses",
        "Assessment of service requirements and compatibility",
        "Contact via preferred communication method if needed"
      ]
    },
    {
      number: 2,
      title: "Account Activation",
      description: "Once approved, your NewTerra account will be activated with all requested services configured.",
      timeline: "Same day",
      icon: Mail,
      details: [
        "Account creation with secure login credentials",
        "Email confirmation with activation details",
        "Access to your personalized dashboard",
        "Initial service provisioning and configuration",
        "Welcome package with getting started guide"
      ]
    },
    {
      number: 3,
      title: "Welcome & Onboarding Call",
      description: "A dedicated account manager will reach out to schedule a personalized welcome call.",
      timeline: "Within 3 days",
      icon: Phone,
      details: [
        "Assignment of dedicated account manager",
        "Scheduling of convenient call time",
        "Platform walkthrough and feature overview",
        "Q&A session for any questions or concerns",
        "Customization of services to your specific needs"
      ]
    },
    {
      number: 4,
      title: "Service Setup & Go-Live",
      description: "Based on your selections, we'll begin provisioning services and scheduling any required setups.",
      timeline: "1-2 weeks",
      icon: Settings,
      details: [
        "Implementation of selected business services",
        "Integration with existing systems and software",
        "Data migration and initial synchronization",
        "Staff training sessions (if applicable)",
        "Ongoing support and monitoring activation"
      ]
    }
  ];
  
  return (
    <Card className={cn(
      "transition-all duration-200 border-green-500/30 bg-gradient-to-br from-green-50/50 to-blue-50/50",
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
          <div className="flex-1">
            <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
              <div className="p-2 rounded-full bg-green-100">
                <Sparkles className="w-5 h-5 text-green-600" aria-hidden="true" />
              </div>
              <span>What Happens Next?</span>
              <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                <Star className="w-3 h-3 mr-1" aria-hidden="true" />
                Your Journey
              </Badge>
            </CardTitle>
            <CardDescription className="mt-2 leading-relaxed">
              After you complete the setup, here's your personalized onboarding journey. 
              {showExpandableDetails && "Click on any step to see more details."}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-2">
        {/* Progress Overview */}
        {showTimeline && (
          <div className="mb-6 p-4 rounded-lg bg-white/50 border">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm">Process Timeline</h4>
              <Badge variant="outline" className="text-xs">
                Estimated: 2-3 weeks total
              </Badge>
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Calendar className="w-3 h-3" aria-hidden="true" />
              <span>Most clients are fully operational within 2-3 weeks of submission</span>
            </div>
          </div>
        )}
        
        {/* Steps */}
        <div className="space-y-3">
          {steps.map((step, index) => (
            <NextStepItem
              key={step.number}
              stepNumber={step.number}
              title={step.title}
              description={step.description}
              timeline={showTimeline ? step.timeline : undefined}
              icon={step.icon}
              isCompleted={completedSteps.includes(step.number)}
              isCurrent={currentStep === step.number}
              isExpanded={expandedSteps.has(step.number)}
              onToggle={showExpandableDetails ? () => toggleStep(step.number) : undefined}
              details={step.details}
            />
          ))}
        </div>
        
        {/* Support Information */}
        <div className="mt-6 pt-6 border-t">
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="p-4 rounded-lg bg-blue-50/50 border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <Phone className="w-4 h-4 text-blue-600" aria-hidden="true" />
                <h4 className="font-medium text-blue-900">Need Help?</h4>
              </div>
              <p className="text-sm text-blue-700 mb-2">
                Our support team is here to assist you throughout the process.
              </p>
              <div className="text-xs text-blue-600">
                <p>Phone: 1800 XXX XXX</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
            
            <div className="p-4 rounded-lg bg-green-50/50 border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <Users className="w-4 h-4 text-green-600" aria-hidden="true" />
                <h4 className="font-medium text-green-900">Dedicated Support</h4>
              </div>
              <p className="text-sm text-green-700 mb-2">
                You'll be assigned a dedicated account manager for personalized assistance.
              </p>
              <div className="text-xs text-green-600">
                <p>Available: Mon-Fri 8AM-6PM AEST</p>
                <p>Response time: Within 4 hours</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Screen Reader Status */}
        <div className="sr-only" aria-live="polite" aria-atomic="true">
          {expandedSteps.size > 0 && `${expandedSteps.size} step${expandedSteps.size > 1 ? 's' : ''} expanded for details.`}
          {completedSteps.length > 0 && `${completedSteps.length} step${completedSteps.length > 1 ? 's' : ''} completed.`}
          {currentStep > 0 && `Currently on step ${currentStep}.`}
        </div>
      </CardContent>
    </Card>
  );
});

NextStepsSection.displayName = 'NextStepsSection';
