import React, { memo, useMemo } from 'react';
import { <PERSON>rac<PERSON>, Edit, FileText, Users, FileSignature, SprayCan, Loader2, CheckCircle, AlertCircle, Calendar, Hash, Package, Building } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { Database } from "@/types/database.types";

type ActivityRow = Database['farms']['Tables']['activities']['Row'];
type LicenseRow = Database['farms']['Tables']['licenses']['Row'];
type SupplierRow = Database['farms']['Tables']['suppliers']['Row'];
type ContractRow = Database['farms']['Tables']['contracts']['Row'];
type ChemicalUsageRow = Database['farms']['Tables']['chemical_usage']['Row'];
import { Step2FarmOperationsWithChildren } from "@/types/onboarding";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/utils/utils";

interface FarmOpsReviewSectionProps {
  step2Data?: Step2FarmOperationsWithChildren | null;
  disabled?: boolean;
  onEdit?: () => void;
  className?: string;
}

interface ValidationState {
  isValid: boolean;
  completionRate: number;
  totalSections: number;
  completedSections: number;
  missingSections: string[];
}

interface DetailItemProps {
  label: string;
  value?: React.ReactNode;
  icon?: React.ElementType;
  important?: boolean;
  className?: string;
}

const DetailItem = memo<DetailItemProps>(({ label, value, icon: Icon, important = false, className }) => {
  const isMobile = useIsMobile();

  return (
    <div className={cn(
      "py-3 transition-colors hover:bg-muted/30 rounded-sm px-2 -mx-2",
      isMobile ? "space-y-1" : "sm:grid sm:grid-cols-3 sm:gap-4 items-start",
      className
    )}>
      <dt className={cn(
        "text-sm font-medium flex items-center",
        important ? "text-foreground" : "text-muted-foreground"
      )}>
        {Icon && <Icon className="w-4 h-4 mr-2 text-primary flex-shrink-0" aria-hidden="true" />}
        <span>{label}</span>
        {important && <span className="text-destructive ml-1" aria-label="important field">*</span>}
      </dt>
      <dd className={cn(
        "text-sm",
        isMobile ? "mt-1 pl-6" : "sm:mt-0 sm:col-span-2",
        value ? "text-foreground" : "text-muted-foreground italic"
      )}>
        {value || "Not provided"}
      </dd>
    </div>
  );
});

DetailItem.displayName = 'DetailItem';

interface SubSectionProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ElementType;
  count?: number;
  isEmpty?: boolean;
  className?: string;
}

const SubSection = memo<SubSectionProps>(({ title, children, icon: Icon, count, isEmpty = false, className }) => {
  const isMobile = useIsMobile();

  return (
    <section className={cn(
      "pt-6 mt-6 border-t first:mt-0 first:pt-0 first:border-t-0",
      isEmpty && "opacity-60",
      className
    )} aria-labelledby={`section-${title.toLowerCase().replace(/\s+/g, '-')}`}>
      <div className={cn(
        "flex items-center justify-between mb-4",
        isMobile && "flex-col items-start gap-2"
      )}>
        <h4 id={`section-${title.toLowerCase().replace(/\s+/g, '-')}`} className="text-base font-semibold flex items-center">
          {Icon && <Icon className="w-5 h-5 mr-2 text-primary" aria-hidden="true" />}
          <span>{title}</span>
        </h4>
        {typeof count === 'number' && (
          <Badge variant={count > 0 ? "default" : "outline"} className="text-xs">
            {count} {count === 1 ? 'item' : 'items'}
          </Badge>
        )}
      </div>
      <dl className={cn(
        "space-y-1",
        !isMobile && "divide-y divide-border/50"
      )}>
        {children}
      </dl>
    </section>
  );
});

SubSection.displayName = 'SubSection';

/**
 * Enhanced FarmOpsReviewSection component with comprehensive validation and mobile optimization
 * 
 * Features:
 * - Responsive design with mobile-first approach
 * - Type-safe implementation with proper error handling
 * - Comprehensive validation state tracking
 * - Accessible design patterns with ARIA attributes
 * - Visual indicators for completion status
 * - Enhanced UX with proper loading and empty states
 */
export const FarmOpsReviewSection = memo<FarmOpsReviewSectionProps>(({
  step2Data,
  disabled = false,
  onEdit,
  className
}) => {
  const { updateCurrentStep, sessionData } = useOnboarding();
  const isMobile = useIsMobile();

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      updateCurrentStep(2); // Navigate to Step 2: Farm Operations
    }
  };

  // Validation state calculation
  const validationState = useMemo<ValidationState>(() => {
    if (!step2Data) {
      return {
        isValid: false,
        completionRate: 0,
        totalSections: 5,
        completedSections: 0,
        missingSections: ['Activities', 'Licenses', 'Suppliers', 'Contracts', 'Chemical Usage']
      };
    }

    const sections = [
      { name: 'Activities', data: step2Data.activities, hasData: step2Data.activities && step2Data.activities.length > 0 },
      { name: 'Licenses', data: step2Data.licenses, hasData: step2Data.licenses && step2Data.licenses.length > 0 },
      { name: 'Suppliers', data: step2Data.suppliers, hasData: step2Data.suppliers && step2Data.suppliers.length > 0 },
      { name: 'Contracts', data: step2Data.contracts, hasData: step2Data.contracts && step2Data.contracts.length > 0 },
      { name: 'Chemical Usage', data: step2Data.chemicalUsage, hasData: step2Data.chemicalUsage && step2Data.chemicalUsage.length > 0 }
    ];

    const completedSections = sections.filter(section => section.hasData).length;
    const missingSections = sections.filter(section => !section.hasData).map(section => section.name);
    const totalSections = sections.length;
    const completionRate = Math.round((completedSections / totalSections) * 100);

    return {
      isValid: completedSections >= 2, // At least 2 sections should be completed
      completionRate,
      totalSections,
      completedSections,
      missingSections
    };
  }, [step2Data]);

  // Helper function to safely format dates from the database
  const formatDate = (dateString: string | null | undefined): string | null => {
    if (!dateString) return null;
    try {
      // Database stores dates as DATE type (YYYY-MM-DD), convert to local date string
      const date = new Date(dateString + 'T00:00:00');
      return date.toLocaleDateString('en-AU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Invalid date format:', dateString);
      return dateString; // Return original string if parsing fails
    }
  };

  // Helper to get document name with better type safety
  const getDocumentNameForLicense = (licenseId: string): string | null => {
    if (!licenseId || !sessionData?.documents) return null;
    const doc = sessionData.documents.find(d =>
      d.related_to_entity === 'licenses' && d.related_to_id === licenseId
    );
    return doc?.document_name || null;
  };

  if (!step2Data) {
    return (
      <Card className={cn("transition-all duration-200", className)}>
        <CardHeader className="pb-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
                <Tractor className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
                <span>Farm Operations Review</span>
                <Badge variant="outline" className="text-xs">
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" aria-hidden="true" />
                  Loading
                </Badge>
              </CardTitle>
              <CardDescription className="mt-2">
                Loading farm operations data for review...
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size={isMobile ? "sm" : "default"}
              onClick={handleEdit}
              disabled
              className="flex-shrink-0"
              aria-label="Edit farm operations - currently loading"
            >
              <Loader2 className="w-4 h-4 mr-2 animate-spin" aria-hidden="true" />
              <span className="hidden sm:inline">Loading Data...</span>
              <span className="sm:hidden">Loading...</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center space-y-2">
              <Loader2 className="w-8 h-8 animate-spin mx-auto text-muted-foreground" aria-hidden="true" />
              <p className="text-sm text-muted-foreground">Loading farm operations data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }


  return (
    <Card className={cn(
      "transition-all duration-200",
      validationState.isValid && "border-green-200 bg-green-50/30",
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
          <div className="flex-1">
            <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
              <Tractor className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
              <span>Farm Operations Review</span>
              <div className="flex items-center gap-2">
                <Badge
                  variant={validationState.isValid ? "default" : "destructive"}
                  className="text-xs"
                >
                  {validationState.completionRate}% Complete
                </Badge>
                {validationState.isValid ? (
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                    <CheckCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                    Ready
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs">
                    <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                    {validationState.missingSections.length} Missing
                  </Badge>
                )}
              </div>
            </CardTitle>
            <CardDescription className="mt-2">
              Review of your farming activities, licenses, suppliers, contracts, and chemical usage from Step 2.
              {!validationState.isValid && (
                <span className="block mt-1 text-destructive font-medium">
                  {validationState.completedSections} of {validationState.totalSections} sections completed
                </span>
              )}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size={isMobile ? "sm" : "default"}
            onClick={handleEdit}
            disabled={disabled}
            className="flex-shrink-0"
            aria-label="Edit farm operations from Step 2"
          >
            <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
            <span className="hidden sm:inline">Edit Step 2</span>
            <span className="sm:hidden">Edit</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Statistics */}
        <div className="grid grid-cols-2 sm:grid-cols-5 gap-4 p-4 rounded-lg bg-muted/30">
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{step2Data.activities?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Activities</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{step2Data.licenses?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Licenses</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{step2Data.suppliers?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Suppliers</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{step2Data.contracts?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Contracts</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{step2Data.chemicalUsage?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Chemicals</div>
          </div>
        </div>

        {step2Data.activities && step2Data.activities.length > 0 && (
          <SubSection
            title="Farming Activities"
            icon={Tractor}
            count={step2Data.activities.length}
          >
            {step2Data.activities.map((activity: ActivityRow, index: number) => (
              <div key={activity.id || index} className="mb-4 p-3 rounded-lg border bg-card/50">
                <h5 className="font-medium text-sm mb-2 flex items-center">
                  <Hash className="w-3 h-3 mr-1 text-muted-foreground" aria-hidden="true" />
                  Activity {index + 1}
                </h5>
                <div className="space-y-2">
                  <DetailItem label="Type" value={activity.activity_type} important />
                  {activity.crop_type && <DetailItem label="Crop Type" value={activity.crop_type} icon={Package} />}
                  {activity.livestock_type && <DetailItem label="Livestock Type" value={activity.livestock_type} icon={Package} />}
                  {activity.approximate_numbers && <DetailItem label="Approx. Numbers / Area" value={String(activity.approximate_numbers)} icon={Hash} />}
                </div>
              </div>
            ))}
          </SubSection>
        )}

        {step2Data.licenses && step2Data.licenses.length > 0 && (
          <SubSection
            title="Licenses & Certifications"
            icon={FileText}
            count={step2Data.licenses.length}
          >
            {step2Data.licenses.map((license: LicenseRow, index: number) => {
              const documentName = getDocumentNameForLicense(license.id);
              const isExpiringSoon = license.expiry_date &&
                new Date(license.expiry_date) < new Date(Date.now() + 90 * 24 * 60 * 60 * 1000); // 90 days

              return (
                <div key={license.id || index} className="mb-4 p-3 rounded-lg border bg-card/50">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-sm flex items-center">
                      <Hash className="w-3 h-3 mr-1 text-muted-foreground" aria-hidden="true" />
                      License {index + 1}
                    </h5>
                    {isExpiringSoon && (
                      <Badge variant="destructive" className="text-xs">
                        <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                        Expiring Soon
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-2">
                    <DetailItem label="Type" value={license.license_type} important />
                    {license.license_number && <DetailItem label="License Number" value={license.license_number} icon={Hash} />}
                    {license.issue_date && <DetailItem label="Issue Date" value={formatDate(license.issue_date)} icon={Calendar} />}
                    {license.expiry_date && (
                      <DetailItem
                        label="Expiry Date"
                        value={formatDate(license.expiry_date)}
                        icon={Calendar}
                        className={isExpiringSoon ? "text-destructive" : undefined}
                      />
                    )}
                    <DetailItem label="Associated Document" value={documentName || "No document attached"} icon={FileText} />
                  </div>
                </div>
              );
            })}
          </SubSection>
        )}

        {step2Data.suppliers && step2Data.suppliers.length > 0 && (
          <SubSection
            title="Suppliers & Contractors"
            icon={Users}
            count={step2Data.suppliers.length}
          >
            {step2Data.suppliers.map((supplier: SupplierRow, index: number) => (
              <div key={supplier.id || index} className="mb-4 p-3 rounded-lg border bg-card/50">
                <h5 className="font-medium text-sm mb-2 flex items-center">
                  <Hash className="w-3 h-3 mr-1 text-muted-foreground" aria-hidden="true" />
                  Supplier {index + 1}
                </h5>
                <div className="space-y-2">
                  <DetailItem label="Name" value={supplier.supplier_name} important icon={Building} />
                  {supplier.contact_details && <DetailItem label="Contact Details" value={supplier.contact_details} />}
                  {supplier.services_provided && <DetailItem label="Services/Products" value={supplier.services_provided} icon={Package} />}
                </div>
              </div>
            ))}
          </SubSection>
        )}

        {step2Data.contracts && step2Data.contracts.length > 0 && (
          <SubSection
            title="Farm Contracts & Agreements"
            icon={FileSignature}
            count={step2Data.contracts.length}
          >
            {step2Data.contracts.map((contract: ContractRow, index: number) => {
              const isExpiringSoon = contract.expiry_date &&
                new Date(contract.expiry_date) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

              return (
                <div key={contract.id || index} className="mb-4 p-3 rounded-lg border bg-card/50">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-sm flex items-center">
                      <Hash className="w-3 h-3 mr-1 text-muted-foreground" aria-hidden="true" />
                      Contract {index + 1}
                    </h5>
                    {isExpiringSoon && (
                      <Badge variant="destructive" className="text-xs">
                        <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                        Expiring Soon
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-2">
                    <DetailItem label="Description" value={contract.contract_description} important />
                    {contract.contract_type && <DetailItem label="Contract Type" value={contract.contract_type} />}
                    {contract.expiry_date && (
                      <DetailItem
                        label="Expiry Date"
                        value={formatDate(contract.expiry_date)}
                        icon={Calendar}
                        className={isExpiringSoon ? "text-destructive" : undefined}
                      />
                    )}
                  </div>
                </div>
              );
            })}
          </SubSection>
        )}

        {step2Data.chemicalUsage && step2Data.chemicalUsage.length > 0 && (
          <SubSection
            title="Chemical Usage & Storage"
            icon={SprayCan}
            count={step2Data.chemicalUsage.length}
          >
            {step2Data.chemicalUsage.map((chem: ChemicalUsageRow, index: number) => (
              <div key={chem.id || index} className="mb-4 p-3 rounded-lg border bg-card/50">
                <h5 className="font-medium text-sm mb-2 flex items-center">
                  <Hash className="w-3 h-3 mr-1 text-muted-foreground" aria-hidden="true" />
                  Chemical {index + 1}
                </h5>
                <div className="space-y-2">
                  <DetailItem label="Product Name" value={chem.product_name} important icon={Package} />
                  {chem.usage_purpose && <DetailItem label="Usage Purpose" value={chem.usage_purpose} />}
                  {chem.application_rate && <DetailItem label="Application Rate" value={chem.application_rate} />}
                  {chem.last_application_date && <DetailItem label="Last Application Date" value={formatDate(chem.last_application_date)} icon={Calendar} />}
                </div>
              </div>
            ))}
          </SubSection>
        )}

        {/* Empty State */}
        {validationState.completedSections === 0 && (
          <div className="text-center py-8 space-y-4">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <Tractor className="w-8 h-8 text-muted-foreground" aria-hidden="true" />
            </div>
            <div>
              <h3 className="font-medium text-foreground mb-1">No Farm Operations Data</h3>
              <p className="text-sm text-muted-foreground mb-4">
                No farm operations details from Step 2 have been provided yet.
              </p>
              <Button
                variant="outline"
                onClick={handleEdit}
                disabled={disabled}
                className="min-w-[120px]"
              >
                <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
                Add Details
              </Button>
            </div>
          </div>
        )}

        {/* Missing Sections Warning */}
        {validationState.completedSections > 0 && validationState.missingSections.length > 0 && (
          <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
            <h4 className="font-medium text-yellow-800 mb-2 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" aria-hidden="true" />
              Incomplete Sections
            </h4>
            <p className="text-sm text-yellow-700 mb-2">
              The following sections are missing data from Step 2:
            </p>
            <ul className="text-sm text-yellow-700 space-y-1">
              {validationState.missingSections.map((section, index) => (
                <li key={index} className="flex items-center gap-2">
                  <span className="w-1 h-1 bg-yellow-600 rounded-full" aria-hidden="true" />
                  {section}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Screen Reader Status */}
        <div className="sr-only" aria-live="polite" aria-atomic="true">
          {validationState.isValid && "Farm operations review is complete with all required information."}
          {!validationState.isValid && `Farm operations review is ${validationState.completionRate}% complete. ${validationState.missingSections.length} sections still need data.`}
        </div>
      </CardContent>
    </Card>
  );
});

FarmOpsReviewSection.displayName = 'FarmOpsReviewSection';