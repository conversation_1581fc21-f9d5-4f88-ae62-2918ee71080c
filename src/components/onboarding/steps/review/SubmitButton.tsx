import React, { memo, useMemo } from 'react';
import { Check, Loader2, Send, AlertCircle, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/utils/utils";

interface SubmitButtonProps {
  /** Whether the form is currently being submitted */
  isSubmitting: boolean;
  /** Whether the button should be disabled */
  disabled: boolean;
  /** Custom button text when not submitting */
  buttonText?: string;
  /** Custom text to show while submitting */
  submittingText?: string;
  /** Completion percentage (0-100) */
  completionRate?: number;
  /** Validation errors count */
  errorCount?: number;
  /** Success callback for submission completion */
  onSuccess?: () => void;
  /** Show completion status */
  showCompletionStatus?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Button variant */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  /** Button size */
  size?: 'default' | 'sm' | 'lg';
  /** Form ID this button submits (for accessibility) */
  form?: string;
}

/**
 * Enhanced SubmitButton component with comprehensive accessibility and UX features
 * 
 * Features:
 * - Progressive enhancement with completion status
 * - Mobile-responsive design with proper touch targets
 * - Comprehensive accessibility attributes and ARIA support
 * - Visual feedback for validation states and loading
 * - Type-safe implementation with proper error handling
 * - Support for different button variants and sizes
 */
export const SubmitButton = memo<SubmitButtonProps>(({
  isSubmitting,
  disabled,
  buttonText = "Complete Setup & Submit",
  submittingText = "Submitting Application...",
  completionRate = 100,
  errorCount = 0,
  onSuccess,
  showCompletionStatus = true,
  className,
  variant = 'default',
  size = 'default',
  form,
}) => {
  const isMobile = useIsMobile();

  // Determine button state and appearance
  const buttonState = useMemo(() => {
    if (isSubmitting) return 'submitting';
    if (errorCount > 0) return 'error';
    if (completionRate < 100) return 'incomplete';
    return 'ready';
  }, [isSubmitting, errorCount, completionRate]);

  // Dynamic button properties based on state
  const buttonProps = useMemo(() => {
    const baseProps = {
      disabled: disabled || isSubmitting,
      type: 'submit' as const,
      form,
    };

    switch (buttonState) {
      case 'submitting':
        return {
          ...baseProps,
          'aria-label': `${submittingText} Please wait while we process your application.`,
          'aria-describedby': 'submit-status',
        };
      case 'error':
        return {
          ...baseProps,
          disabled: true,
          'aria-label': `Cannot submit: ${errorCount} validation error${errorCount > 1 ? 's' : ''} need to be fixed`,
          'aria-describedby': 'submit-errors',
        };
      case 'incomplete':
        return {
          ...baseProps,
          disabled: true,
          'aria-label': `Application ${completionRate}% complete. Please finish all required sections before submitting.`,
          'aria-describedby': 'submit-completion',
        };
      case 'ready':
      default:
        return {
          ...baseProps,
          'aria-label': `${buttonText} - Submit your completed onboarding application`,
          'aria-describedby': showCompletionStatus ? 'submit-ready' : undefined,
        };
    }
  }, [buttonState, disabled, isSubmitting, submittingText, errorCount, completionRate, buttonText, showCompletionStatus, form]);

  // Dynamic styling based on state
  const buttonVariant = useMemo(() => {
    if (buttonState === 'error') return 'destructive';
    if (buttonState === 'incomplete') return 'outline';
    return variant;
  }, [buttonState, variant]);

  // Icon selection based on state
  const ButtonIcon = useMemo(() => {
    switch (buttonState) {
      case 'submitting':
        return Loader2;
      case 'error':
        return AlertCircle;
      case 'incomplete':
        return Send;
      case 'ready':
      default:
        return completionRate === 100 ? CheckCircle : Check;
    }
  }, [buttonState, completionRate]);

  // Button content based on state
  const buttonContent = useMemo(() => {
    switch (buttonState) {
      case 'submitting':
        return submittingText;
      case 'error':
        return `Fix ${errorCount} Error${errorCount > 1 ? 's' : ''} to Submit`;
      case 'incomplete':
        return `Complete Remaining ${100 - completionRate}%`;
      case 'ready':
      default:
        return buttonText;
    }
  }, [buttonState, submittingText, errorCount, completionRate, buttonText]);

  return (
    <div className={cn("flex flex-col items-end space-y-3", className)}>
      {/* Completion Status Indicator */}
      {showCompletionStatus && (
        <div className="flex items-center gap-2 text-sm">
          {buttonState === 'ready' ? (
            <div id="submit-ready" className="flex items-center gap-2 text-green-700">
              <CheckCircle className="w-4 h-4" aria-hidden="true" />
              <span>Ready to submit</span>
            </div>
          ) : buttonState === 'incomplete' ? (
            <div id="submit-completion" className="flex items-center gap-2 text-muted-foreground">
              <Badge variant="outline" className="text-xs">
                {completionRate}% Complete
              </Badge>
              <span>Complete all sections to submit</span>
            </div>
          ) : buttonState === 'error' ? (
            <div id="submit-errors" className="flex items-center gap-2 text-destructive">
              <AlertCircle className="w-4 h-4" aria-hidden="true" />
              <span>{errorCount} validation error{errorCount > 1 ? 's' : ''} to fix</span>
            </div>
          ) : (
            <div id="submit-status" className="flex items-center gap-2 text-blue-700">
              <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" />
              <span>Processing application...</span>
            </div>
          )}
        </div>
      )}

      {/* Submit Button */}
      <Button
        {...buttonProps}
        variant={buttonVariant}
        size={isMobile ? (size === 'lg' ? 'default' : 'sm') : size}
        className={cn(
          "min-w-[180px] sm:min-w-[200px] px-6 py-3",
          "text-base font-medium transition-all duration-200",
          "focus:ring-2 focus:ring-offset-2",
          isMobile && "w-full", // Full width on mobile for better touch targets
          isSubmitting && "cursor-wait",
          className
        )}
      >
        <div className="flex items-center justify-center gap-2">
          <ButtonIcon 
            className={cn(
              "h-5 w-5 flex-shrink-0",
              isSubmitting && "animate-spin"
            )} 
            aria-hidden="true"
          />
          <span className={cn(
            "transition-opacity duration-200",
            isSubmitting && "opacity-90"
          )}>
            {buttonContent}
          </span>
        </div>
      </Button>

      {/* Progress Indicator for Mobile */}
      {isMobile && showCompletionStatus && buttonState !== 'ready' && (
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div 
            className={cn(
              "h-2 rounded-full transition-all duration-300",
              buttonState === 'error' ? "bg-destructive" : "bg-primary"
            )}
            style={{ width: `${completionRate}%` }}
            role="progressbar"
            aria-valuenow={completionRate}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`Application completion: ${completionRate}%`}
          />
        </div>
      )}

      {/* Screen Reader Status */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {isSubmitting && "Submitting your application. Please do not close this page."}
        {buttonState === 'error' && `There are ${errorCount} validation errors that need to be fixed before submission.`}
        {buttonState === 'incomplete' && `Application is ${completionRate}% complete. Please complete all required sections.`}
        {buttonState === 'ready' && "Application is complete and ready for submission."}
      </div>

      {/* Additional Help Text */}
      {buttonState === 'submitting' && (
        <p className="text-xs text-muted-foreground text-center mt-2 max-w-xs">
          This may take a few moments. Please don't close this page.
        </p>
      )}
    </div>
  );
});

SubmitButton.displayName = 'SubmitButton';