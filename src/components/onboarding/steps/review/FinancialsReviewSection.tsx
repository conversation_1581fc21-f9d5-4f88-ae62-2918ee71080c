import React, { memo, useMemo } from 'react';
import { Landmark, Edit, DollarSign, ListChecks, CheckCircle, Loader2, Users, AlertCircle, Calendar, Hash, Package, Building, Shield, Clock, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/utils/utils";

import { Step3FinancialSystemsWithChildren, Bookkeeping, Payroll, Asset } from "@/types/onboarding";

interface FinancialsReviewSectionProps {
  step3Data?: Step3FinancialSystemsWithChildren | null;
  disabled?: boolean;
  onEdit?: () => void;
  className?: string;
}

interface ValidationState {
  isValid: boolean;
  completionRate: number;
  totalSections: number;
  completedSections: number;
  missingSections: string[];
  warnings: string[];
}

interface DetailItemProps {
  label: string;
  value?: React.ReactNode;
  icon?: React.ElementType;
  important?: boolean;
  secure?: boolean;
  className?: string;
}

const DetailItem = memo<DetailItemProps>(({ label, value, icon: Icon, important = false, secure = false, className }) => {
  const isMobile = useIsMobile();
  
  return (
    <div className={cn(
      "py-3 transition-colors hover:bg-muted/30 rounded-sm px-2 -mx-2",
      isMobile ? "space-y-1" : "sm:grid sm:grid-cols-3 sm:gap-4 items-start",
      className
    )}>
      <dt className={cn(
        "text-sm font-medium flex items-center",
        important ? "text-foreground" : "text-muted-foreground"
      )}>
        {Icon && <Icon className="w-4 h-4 mr-2 text-primary flex-shrink-0" aria-hidden="true" />}
        <span>{label}</span>
        {important && <span className="text-destructive ml-1" aria-label="important field">*</span>}
        {secure && <Shield className="w-3 h-3 ml-1 text-green-600" aria-label="secure field" />}
      </dt>
      <dd className={cn(
        "text-sm",
        isMobile ? "mt-1 pl-6" : "sm:mt-0 sm:col-span-2",
        value ? "text-foreground" : "text-muted-foreground italic"
      )}>
        {value || "Not provided"}
      </dd>
    </div>
  );
});

DetailItem.displayName = 'DetailItem';

interface SubSectionProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ElementType;
  status?: 'complete' | 'incomplete' | 'partial';
  count?: number;
  className?: string;
}

const SubSection = memo<SubSectionProps>(({ title, children, icon: Icon, status = 'incomplete', count, className }) => {
  const isMobile = useIsMobile();
  
  const statusBadge = {
    complete: { variant: 'default' as const, text: 'Complete', color: 'text-green-700' },
    partial: { variant: 'outline' as const, text: 'Partial', color: 'text-yellow-700' },
    incomplete: { variant: 'destructive' as const, text: 'Missing', color: 'text-destructive' }
  };
  
  return (
    <section className={cn(
      "pt-6 mt-6 border-t first:mt-0 first:pt-0 first:border-t-0",
      status === 'incomplete' && "opacity-60",
      className
    )} aria-labelledby={`section-${title.toLowerCase().replace(/\s+/g, '-')}`}>
      <div className={cn(
        "flex items-center justify-between mb-4",
        isMobile && "flex-col items-start gap-2"
      )}>
        <h4 id={`section-${title.toLowerCase().replace(/\s+/g, '-')}`} className="text-base font-semibold flex items-center">
          {Icon && <Icon className="w-5 h-5 mr-2 text-primary" aria-hidden="true" />}
          <span>{title}</span>
        </h4>
        <div className="flex items-center gap-2">
          {typeof count === 'number' && (
            <Badge variant="outline" className="text-xs">
              {count} {count === 1 ? 'item' : 'items'}
            </Badge>
          )}
          <Badge variant={statusBadge[status].variant} className="text-xs">
            {status === 'complete' && <CheckCircle className="w-3 h-3 mr-1" aria-hidden="true" />}
            {status === 'incomplete' && <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />}
            {statusBadge[status].text}
          </Badge>
        </div>
      </div>
      <dl className={cn(
        "space-y-1",
        !isMobile && "divide-y divide-border/50"
      )}>
        {children}
      </dl>
    </section>
  );
});

SubSection.displayName = 'SubSection';

/**
 * Enhanced FinancialsReviewSection component with comprehensive validation and UX improvements
 * 
 * Features:
 * - Comprehensive validation state tracking
 * - Responsive design with mobile optimization
 * - Secure field indicators and proper error handling
 * - Visual status indicators for each section
 * - Type-safe implementation with proper accessibility
 * - Enhanced asset management with renewal tracking
 */
export const FinancialsReviewSection = memo<FinancialsReviewSectionProps>(({ 
  step3Data, 
  disabled = false, 
  onEdit, 
  className 
}) => {
  const { updateCurrentStep } = useOnboarding();
  const isMobile = useIsMobile();

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      updateCurrentStep(3); // Navigate to Step 3: Financial Systems
    }
  };

  const bookkeepingData = step3Data?.bookkeeping;
  const payrollData = step3Data?.payroll;
  const assetsData = step3Data?.assets;

  // Validation state calculation
  const validationState = useMemo<ValidationState>(() => {
    if (!step3Data) {
      return {
        isValid: false,
        completionRate: 0,
        totalSections: 3,
        completedSections: 0,
        missingSections: ['Bookkeeping', 'Payroll', 'Assets'],
        warnings: []
      };
    }

    const sections = [
      { 
        name: 'Bookkeeping', 
        data: bookkeepingData, 
        isComplete: bookkeepingData?.current_software && bookkeepingData?.bas_lodgement_frequency
      },
      { 
        name: 'Payroll', 
        data: payrollData, 
        isComplete: payrollData && (
          !payrollData.is_payroll_processing_needed || 
          (payrollData.employee_count && payrollData.current_payroll_software)
        )
      },
      { 
        name: 'Assets', 
        data: assetsData, 
        isComplete: assetsData && assetsData.length > 0
      }
    ];

    const completedSections = sections.filter(section => section.isComplete).length;
    const missingSections = sections.filter(section => !section.isComplete).map(section => section.name);
    const totalSections = sections.length;
    const completionRate = Math.round((completedSections / totalSections) * 100);

    // Check for warnings
    const warnings: string[] = [];
    if (bookkeepingData && !bookkeepingData.access_credentials) {
      warnings.push('Bookkeeping software credentials not provided');
    }
    if (payrollData?.is_payroll_processing_needed && !payrollData.encrypted_access_credentials) {
      warnings.push('Payroll software credentials not provided');
    }
    if (assetsData) {
      const expiringSoon = assetsData.filter(asset => {
        if (!asset.renewal_date) return false;
        const renewalDate = new Date(asset.renewal_date);
        const threeMonthsFromNow = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
        return renewalDate < threeMonthsFromNow;
      });
      if (expiringSoon.length > 0) {
        warnings.push(`${expiringSoon.length} asset${expiringSoon.length > 1 ? 's' : ''} expiring within 3 months`);
      }
    }

    return {
      isValid: completedSections >= 2, // At least 2 sections should be completed
      completionRate,
      totalSections,
      completedSections,
      missingSections,
      warnings
    };
  }, [step3Data, bookkeepingData, payrollData, assetsData]);

  // Helper to determine section status
  const getSectionStatus = (sectionName: string): 'complete' | 'incomplete' | 'partial' => {
    switch (sectionName) {
      case 'bookkeeping': {
        if (!bookkeepingData) return 'incomplete';
        const hasBasics = bookkeepingData.current_software && bookkeepingData.bas_lodgement_frequency;
        const hasCredentials = bookkeepingData.access_credentials;
        if (hasBasics && hasCredentials) return 'complete';
        if (hasBasics) return 'partial';
        return 'incomplete';
      }
      case 'payroll': {
        if (!payrollData) return 'incomplete';
        if (!payrollData.is_payroll_processing_needed) return 'complete';
        const hasPayrollBasics = payrollData.employee_count && payrollData.current_payroll_software;
        const hasPayrollCredentials = payrollData.encrypted_access_credentials;
        if (hasPayrollBasics && hasPayrollCredentials) return 'complete';
        if (hasPayrollBasics) return 'partial';
        return 'incomplete';
      }
      case 'assets':
        if (!assetsData || assetsData.length === 0) return 'incomplete';
        return 'complete';
      default:
        return 'incomplete';
    }
  };

  // Helper to format dates safely
  const formatDate = (dateString: string | null | undefined): string | null => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString + 'T00:00:00');
      return date.toLocaleDateString('en-AU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Invalid date format:', dateString);
      return dateString;
    }
  };

  // Helper to check if asset is expiring soon
  const isAssetExpiringSoon = (renewalDate: string | null | undefined): boolean => {
    if (!renewalDate) return false;
    const renewal = new Date(renewalDate);
    const threeMonthsFromNow = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
    return renewal < threeMonthsFromNow;
  };

  if (!step3Data) {
    return (
      <Card className={cn("transition-all duration-200", className)}>
        <CardHeader className="pb-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
                <DollarSign className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
                <span>Financial Systems Review</span>
                <Badge variant="outline" className="text-xs">
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" aria-hidden="true" />
                  Loading
                </Badge>
              </CardTitle>
              <CardDescription className="mt-2">
                Loading financial systems data for review...
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size={isMobile ? "sm" : "default"}
              onClick={handleEdit} 
              disabled
              className="flex-shrink-0"
              aria-label="Edit financial systems - currently loading"
            >
              <Loader2 className="w-4 h-4 mr-2 animate-spin" aria-hidden="true" />
              <span className="hidden sm:inline">Loading Data...</span>
              <span className="sm:hidden">Loading...</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center space-y-2">
              <Loader2 className="w-8 h-8 animate-spin mx-auto text-muted-foreground" aria-hidden="true" />
              <p className="text-sm text-muted-foreground">Loading financial systems data...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "transition-all duration-200", 
      validationState.isValid && "border-green-200 bg-green-50/30",
      className
    )}>
      <CardHeader className="pb-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
          <div className="flex-1">
            <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
              <DollarSign className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
              <span>Financial Systems Review</span>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={validationState.isValid ? "default" : "destructive"}
                  className="text-xs"
                >
                  {validationState.completionRate}% Complete
                </Badge>
                {validationState.isValid ? (
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                    <CheckCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                    Ready
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs">
                    <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                    {validationState.missingSections.length} Missing
                  </Badge>
                )}
                {validationState.warnings.length > 0 && (
                  <Badge variant="outline" className="text-xs text-yellow-700">
                    {validationState.warnings.length} Warning{validationState.warnings.length > 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
            </CardTitle>
            <CardDescription className="mt-2">
              Review of your bookkeeping, payroll, and asset information from Step 3.
              {!validationState.isValid && (
                <span className="block mt-1 text-destructive font-medium">
                  {validationState.completedSections} of {validationState.totalSections} sections completed
                </span>
              )}
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size={isMobile ? "sm" : "default"}
            onClick={handleEdit} 
            disabled={disabled}
            className="flex-shrink-0"
            aria-label="Edit financial systems from Step 3"
          >
            <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
            <span className="hidden sm:inline">Edit Step 3</span>
            <span className="sm:hidden">Edit</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 rounded-lg bg-muted/30">
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">
              {getSectionStatus('bookkeeping') === 'complete' ? '✓' : getSectionStatus('bookkeeping') === 'partial' ? '⚠' : '✗'}
            </div>
            <div className="text-xs text-muted-foreground">Bookkeeping</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">
              {getSectionStatus('payroll') === 'complete' ? '✓' : getSectionStatus('payroll') === 'partial' ? '⚠' : '✗'}
            </div>
            <div className="text-xs text-muted-foreground">Payroll</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{assetsData?.length || 0}</div>
            <div className="text-xs text-muted-foreground">Assets</div>
          </div>
        </div>

        {/* Bookkeeping Section Review */}
        <SubSection 
          title="Bookkeeping & Software" 
          icon={Landmark} 
          status={getSectionStatus('bookkeeping')}
        >
          {bookkeepingData ? (
            <div className="space-y-3">
              <DetailItem 
                label="Current Software" 
                value={bookkeepingData.current_software} 
                important 
                icon={Settings}
              />
              <DetailItem 
                label="Bank Feeds Enabled" 
                value={bookkeepingData.has_bank_feeds_enabled ? 'Yes' : 'No'} 
                icon={Landmark}
              />
              <DetailItem 
                label="BAS Lodgement Frequency" 
                value={bookkeepingData.bas_lodgement_frequency} 
                important
                icon={Clock}
              />
              <DetailItem
                label="Software Credentials"
                value={bookkeepingData.access_credentials
                  ? <div className='flex items-center text-green-600'>
                      <CheckCircle className='w-4 h-4 mr-1' aria-hidden="true" />
                      <span>Stored Securely</span>
                    </div>
                  : <div className='flex items-center text-yellow-600'>
                      <AlertCircle className='w-4 h-4 mr-1' aria-hidden="true" />
                      <span>Not provided</span>
                    </div>
                }
                secure={!!bookkeepingData.access_credentials}
                icon={Shield}
              />
            </div>
          ) : (
            <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
              <p className="text-sm text-yellow-800 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" aria-hidden="true" />
                No bookkeeping information provided
              </p>
            </div>
          )}
        </SubSection>

        {/* Payroll Section Review */}
        <SubSection 
          title="Payroll Management" 
          icon={Users} 
          status={getSectionStatus('payroll')}
        >
          {payrollData ? (
            <div className="space-y-3">
              <DetailItem 
                label="Payroll Processing Needed" 
                value={payrollData.is_payroll_processing_needed ? 'Yes' : 'No'} 
                important
              />
              {payrollData.is_payroll_processing_needed && (
                <>
                  <DetailItem 
                    label="Number of Employees" 
                    value={String(payrollData.employee_count || 0)} 
                    important
                    icon={Users}
                  />
                  <DetailItem 
                    label="Current Payroll Software" 
                    value={payrollData.current_payroll_software} 
                    important
                    icon={Settings}
                  />
                  <DetailItem 
                    label="Access to Software Granted" 
                    value={payrollData.is_access_to_software_granted ? 'Yes' : 'No'} 
                  />
                  <DetailItem
                    label="Software Credentials"
                    value={payrollData.encrypted_access_credentials
                      ? <div className='flex items-center text-green-600'>
                          <CheckCircle className='w-4 h-4 mr-1' aria-hidden="true" />
                          <span>Stored Securely</span>
                        </div>
                      : <div className='flex items-center text-yellow-600'>
                          <AlertCircle className='w-4 h-4 mr-1' aria-hidden="true" />
                          <span>Not provided</span>
                        </div>
                    }
                    secure={!!payrollData.encrypted_access_credentials}
                    icon={Shield}
                  />
                </>
              )}
            </div>
          ) : (
            <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
              <p className="text-sm text-yellow-800 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" aria-hidden="true" />
                No payroll information provided
              </p>
            </div>
          )}
        </SubSection>

        {/* Asset Registry Review */}
        <SubSection 
          title="Asset Registry" 
          icon={ListChecks} 
          status={getSectionStatus('assets')}
          count={assetsData?.length || 0}
        >
          {assetsData && assetsData.length > 0 ? (
            <div className="space-y-4">
              {assetsData.map((asset: Asset, index: number) => {
                const isExpiring = isAssetExpiringSoon(asset.renewal_date);
                
                return (
                  <div key={asset.id || index} className="p-3 rounded-lg border bg-card/50">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-sm flex items-center">
                        <Hash className="w-3 h-3 mr-1 text-muted-foreground" aria-hidden="true" />
                        {asset.asset_type || `Asset ${index + 1}`}
                      </h5>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {asset.asset_category}
                        </Badge>
                        {isExpiring && (
                          <Badge variant="destructive" className="text-xs">
                            <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                            Expiring Soon
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="space-y-2">
                      {asset.make_or_provider && (
                        <DetailItem 
                          label="Make/Provider" 
                          value={asset.make_or_provider} 
                          icon={Building}
                        />
                      )}
                      {asset.registration_or_policy_number && (
                        <DetailItem 
                          label="Registration/Policy No." 
                          value={asset.registration_or_policy_number} 
                          icon={Hash}
                        />
                      )}
                      {asset.renewal_date && (
                        <DetailItem 
                          label="Renewal Date" 
                          value={formatDate(asset.renewal_date)} 
                          icon={Calendar}
                          className={isExpiring ? "text-destructive" : undefined}
                        />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
              <p className="text-sm text-yellow-800 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" aria-hidden="true" />
                No assets registered
              </p>
            </div>
          )}
        </SubSection>

        {/* Empty State */}
        {validationState.completedSections === 0 && (
          <div className="text-center py-8 space-y-4">
            <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <DollarSign className="w-8 h-8 text-muted-foreground" aria-hidden="true" />
            </div>
            <div>
              <h3 className="font-medium text-foreground mb-1">No Financial Systems Data</h3>
              <p className="text-sm text-muted-foreground mb-4">
                No financial systems details from Step 3 have been provided yet.
              </p>
              <Button 
                variant="outline" 
                onClick={handleEdit}
                disabled={disabled}
                className="min-w-[120px]"
              >
                <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
                Add Details
              </Button>
            </div>
          </div>
        )}

        {/* Warnings */}
        {validationState.warnings.length > 0 && (
          <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
            <h4 className="font-medium text-yellow-800 mb-2 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" aria-hidden="true" />
              Attention Required
            </h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              {validationState.warnings.map((warning, index) => (
                <li key={index} className="flex items-center gap-2">
                  <span className="w-1 h-1 bg-yellow-600 rounded-full" aria-hidden="true" />
                  {warning}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Missing Sections Warning */}
        {validationState.completedSections > 0 && validationState.missingSections.length > 0 && (
          <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
            <h4 className="font-medium text-yellow-800 mb-2 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" aria-hidden="true" />
              Incomplete Sections
            </h4>
            <p className="text-sm text-yellow-700 mb-2">
              The following sections are missing data from Step 3:
            </p>
            <ul className="text-sm text-yellow-700 space-y-1">
              {validationState.missingSections.map((section, index) => (
                <li key={index} className="flex items-center gap-2">
                  <span className="w-1 h-1 bg-yellow-600 rounded-full" aria-hidden="true" />
                  {section}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Screen Reader Status */}
        <div className="sr-only" aria-live="polite" aria-atomic="true">
          {validationState.isValid && "Financial systems review is complete with all required information."}
          {!validationState.isValid && `Financial systems review is ${validationState.completionRate}% complete. ${validationState.missingSections.length} sections still need data.`}
          {validationState.warnings.length > 0 && `${validationState.warnings.length} warnings require attention.`}
        </div>
      </CardContent>
    </Card>
  );
});

FinancialsReviewSection.displayName = 'FinancialsReviewSection'; 