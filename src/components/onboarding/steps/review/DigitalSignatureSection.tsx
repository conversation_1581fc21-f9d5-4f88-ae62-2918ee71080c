import React, { useRef, useState, useEffect, useCallback, memo } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw, Check, CheckCircle, PenTool, AlertCircle, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/utils/utils';

interface DigitalSignatureSectionProps {
    /** Callback when signature is confirmed */
    onConfirmSignature: (signatureDataUrl: string) => void;
    /** Whether the signature is being processed/saved */
    isProcessing: boolean;
    /** Whether the component is disabled */
    disabled?: boolean;
    /** Label for the agreement type (e.g., "Service Agreement") */
    agreementTypeLabel: string;
    /** Whether a signature has already been provided */
    hasSignatureBeenProvided?: boolean;
    /** Additional CSS classes */
    className?: string;
    /** Custom signature pad dimensions */
    dimensions?: {
        width?: number;
        height?: number;
    };
    /** Show instructions for first-time users */
    showInstructions?: boolean;
}

/**
 * Enhanced DigitalSignatureSection component with comprehensive accessibility and mobile optimization
 * 
 * Features:
 * - Mobile-responsive signature pad with touch optimization
 * - Comprehensive accessibility support with ARIA attributes
 * - Real-time validation and visual feedback
 * - Responsive design with proper touch targets
 * - Type-safe implementation with proper error handling
 * - Support for signature updates and clearing
 */
export const DigitalSignatureSection = memo<DigitalSignatureSectionProps>(({
    onConfirmSignature,
    isProcessing,
    disabled = false,
    agreementTypeLabel,
    hasSignatureBeenProvided = false,
    className,
    dimensions = { width: 500, height: 200 },
    showInstructions = true,
}) => {
    const sigPadRef = useRef<SignatureCanvas | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const [isPadEmpty, setIsPadEmpty] = useState(true);
    const [hasUserInteracted, setHasUserInteracted] = useState(false);
    const [signatureQuality, setSignatureQuality] = useState<'good' | 'poor' | null>(null);
    const { toast } = useToast();
    const isMobile = useIsMobile();

    // Dynamic dimensions based on container and device
    const responsiveDimensions = {
        width: isMobile ? Math.min(dimensions.width || 500, 400) : dimensions.width || 500,
        height: isMobile ? Math.min(dimensions.height || 200, 150) : dimensions.height || 200,
    };

    const clearSignature = useCallback(() => {
        sigPadRef.current?.clear();
        setIsPadEmpty(true);
        setSignatureQuality(null);
        setHasUserInteracted(false);
    }, []);

    // Analyze signature quality (simple heuristic)
    const analyzeSignatureQuality = useCallback(() => {
        if (!sigPadRef.current || sigPadRef.current.isEmpty()) {
            setSignatureQuality(null);
            return;
        }

        try {
            const data = sigPadRef.current.toData();
            const totalPoints = data.reduce((sum, stroke) => sum + stroke.length, 0);
            const strokeCount = data.length;
            
            // Simple quality heuristics
            if (totalPoints < 10 || strokeCount < 2) {
                setSignatureQuality('poor');
            } else {
                setSignatureQuality('good');
            }
        } catch (error) {
            setSignatureQuality(null);
        }
    }, []);

    const handleConfirm = useCallback(() => {
        if (!sigPadRef.current) {
            toast({
                title: "Technical Error",
                description: "Signature pad is not available. Please refresh the page.",
                variant: "destructive",
            });
            return;
        }

        if (sigPadRef.current.isEmpty()) {
            toast({
                title: "Signature Required",
                description: "Please provide your signature before confirming.",
                variant: "destructive",
            });
            setIsPadEmpty(true);
            return;
        }

        // Quality check
        if (signatureQuality === 'poor') {
            toast({
                title: "Signature Quality",
                description: "Your signature appears to be very brief. Consider adding more detail for better verification.",
                variant: "destructive",
                duration: 5000,
            });
            return;
        }

        try {
            // Get signature as a high-quality PNG data URL
            const signatureDataUrl = sigPadRef.current.toDataURL('image/png', 1.0);
            onConfirmSignature(signatureDataUrl);
            
            toast({
                title: "Signature Captured",
                description: `Your signature for ${agreementTypeLabel} has been recorded.`,
                duration: 3000,
            });
        } catch (error) {
            toast({
                title: "Signature Error",
                description: "Failed to capture signature. Please try again.",
                variant: "destructive",
            });
        }
    }, [onConfirmSignature, agreementTypeLabel, signatureQuality, toast]);

    // Handle signature pad events
    const handleSignatureEnd = useCallback(() => {
        if (sigPadRef.current) {
            const isEmpty = sigPadRef.current.isEmpty();
            setIsPadEmpty(isEmpty);
            setHasUserInteracted(!isEmpty);
            
            if (!isEmpty) {
                analyzeSignatureQuality();
            }
        }
    }, [analyzeSignatureQuality]);

    const handleSignatureBegin = useCallback(() => {
        setIsPadEmpty(false);
        setHasUserInteracted(true);
    }, []);

    // Clear signature when successfully provided
    useEffect(() => {
        if (hasSignatureBeenProvided) {
            clearSignature();
        }
    }, [hasSignatureBeenProvided, clearSignature]);

    // Handle container resize for responsive signature pad
    useEffect(() => {
        const handleResize = () => {
            if (sigPadRef.current && containerRef.current) {
                const container = containerRef.current;
                const rect = container.getBoundingClientRect();
                
                // Adjust canvas to container size while maintaining aspect ratio
                const canvas = sigPadRef.current.getCanvas();
                if (canvas) {
                    canvas.style.width = '100%';
                    canvas.style.height = '100%';
                }
            }
        };

        window.addEventListener('resize', handleResize);
        handleResize(); // Initial call

        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const isOperationDisabled = disabled || isProcessing;
    const showValidationError = isPadEmpty && hasUserInteracted && !hasSignatureBeenProvided;

    return (
        <Card className={cn("transition-all duration-200", className)}>
            <CardHeader className="pb-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                    <div className="flex-1">
                        <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
                            <PenTool className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
                            <span>Digital Signature</span>
                            <div className="flex items-center gap-2">
                                {hasSignatureBeenProvided ? (
                                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                        <CheckCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                                        Signed
                                    </Badge>
                                ) : (
                                    <Badge variant="destructive" className="text-xs">
                                        <AlertCircle className="w-3 h-3 mr-1" aria-hidden="true" />
                                        Required
                                    </Badge>
                                )}
                            </div>
                        </CardTitle>
                        <CardDescription className="mt-2">
                            Please sign below to confirm your agreement to the {agreementTypeLabel}.
                            Your digital signature will be legally binding.
                        </CardDescription>
                    </div>
                </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
                {/* Instructions */}
                {showInstructions && !hasSignatureBeenProvided && (
                    <div className="p-4 rounded-lg border border-blue-200 bg-blue-50">
                        <div className="flex items-start gap-3">
                            <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" aria-hidden="true" />
                            <div>
                                <h4 className="font-medium text-blue-900 mb-2">Signature Instructions</h4>
                                <ul className="text-sm text-blue-800 space-y-1">
                                    <li>• Use your finger or stylus to sign naturally in the box below</li>
                                    <li>• {isMobile ? 'Use a stable surface for best results' : 'Use your mouse to draw your signature'}</li>
                                    <li>• Make sure your signature is clear and recognizable</li>
                                    <li>• You can clear and re-sign if needed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                )}

                {/* Signature Status */}
                {hasSignatureBeenProvided && (
                    <div className="flex items-center p-4 rounded-lg bg-green-500/10 border border-green-600/30 text-green-700">
                        <CheckCircle className="w-5 h-5 mr-3 flex-shrink-0" aria-hidden="true" />
                        <div className="flex-1">
                            <p className="font-medium">Signature Successfully Recorded</p>
                            <p className="text-sm text-green-600 mt-1">
                                Your digital signature for {agreementTypeLabel} has been securely stored. 
                                You can update it by signing again below.
                            </p>
                        </div>
                    </div>
                )}

                {/* Signature Pad */}
                <div className="space-y-3">
                    <Label htmlFor="signaturePad" className="text-base font-medium">
                        Signature Pad
                        <span className="text-destructive ml-1" aria-label="required field">*</span>
                    </Label>
                    
                    <div
                        ref={containerRef}
                        id="signaturePad"
                        className={cn(
                            "border rounded-lg relative overflow-hidden transition-colors",
                            showValidationError ? "border-destructive bg-destructive/5" : "border-border",
                            isOperationDisabled && "opacity-60 cursor-not-allowed",
                            "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
                        )}
                        style={{ 
                            width: '100%', 
                            height: responsiveDimensions.height,
                            touchAction: 'none' // Prevent scrolling while signing
                        }}
                        role="img"
                        aria-label="Digital signature pad"
                        aria-describedby="signature-instructions"
                    >
                        <SignatureCanvas
                            ref={sigPadRef}
                            penColor="black"
                            canvasProps={{
                                width: responsiveDimensions.width,
                                height: responsiveDimensions.height,
                                className: 'sigCanvas w-full h-full',
                                style: { width: '100%', height: '100%', display: 'block' },
                                'aria-label': `Signature pad for ${agreementTypeLabel}`,
                            }}
                            onEnd={handleSignatureEnd}
                            onBegin={handleSignatureBegin}
                            clearOnResize={false}
                            backgroundColor="rgb(255,255,255)"
                            throttle={isMobile ? 16 : 8} // Adjust for mobile performance
                            minDistance={isMobile ? 3 : 1} // Better touch handling
                            disabled={isOperationDisabled}
                        />
                        
                        {/* Overlay for completed signature */}
                        {hasSignatureBeenProvided && !isProcessing && (
                            <div className="absolute inset-0 flex flex-col items-center justify-center bg-green-500/10 pointer-events-none">
                                <CheckCircle className="w-8 h-8 text-green-600 mb-1" aria-hidden="true" />
                                <p className="text-green-700 font-medium text-sm">Signature Provided</p>
                                <p className="text-xs text-green-600 text-center px-2">
                                    Sign again to update
                                </p>
                            </div>
                        )}
                        
                        {/* Processing overlay */}
                        {isProcessing && (
                            <div className="absolute inset-0 flex flex-col items-center justify-center bg-blue-500/10 pointer-events-none">
                                <Loader2 className="w-8 h-8 text-blue-600 mb-1 animate-spin" aria-hidden="true" />
                                <p className="text-blue-700 font-medium text-sm">Processing...</p>
                            </div>
                        )}
                    </div>
                    
                    <p id="signature-instructions" className="text-xs text-muted-foreground">
                        {isMobile 
                            ? "Use your finger or stylus to sign naturally in the box above"
                            : "Click and drag to sign with your mouse in the box above"
                        }
                    </p>
                </div>

                {/* Quality Feedback */}
                {signatureQuality === 'poor' && hasUserInteracted && (
                    <div className="p-3 rounded-lg border border-yellow-200 bg-yellow-50">
                        <p className="text-sm text-yellow-800 flex items-center gap-2">
                            <AlertCircle className="w-4 h-4" aria-hidden="true" />
                            Consider adding more detail to your signature for better verification.
                        </p>
                    </div>
                )}

                {/* Action Buttons */}
                <div className={cn(
                    "flex gap-3",
                    isMobile ? "flex-col" : "flex-row justify-end"
                )}>
                    <Button
                        type="button"
                        variant="outline"
                        onClick={clearSignature}
                        disabled={isOperationDisabled || (isPadEmpty && !hasSignatureBeenProvided)}
                        className={cn("transition-colors", isMobile && "w-full")}
                        aria-label="Clear signature and start over"
                    >
                        <RefreshCw className="w-4 h-4 mr-2" aria-hidden="true" />
                        Clear Signature
                    </Button>
                    
                    <Button
                        type="button"
                        onClick={handleConfirm}
                        disabled={isOperationDisabled || isPadEmpty}
                        className={cn("transition-colors", isMobile && "w-full")}
                        aria-label={hasSignatureBeenProvided ? `Update signature for ${agreementTypeLabel}` : `Confirm signature for ${agreementTypeLabel}`}
                    >
                        {isProcessing ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" aria-hidden="true" />
                        ) : (
                            <Check className="w-4 h-4 mr-2" aria-hidden="true" />
                        )}
                        {hasSignatureBeenProvided ? 'Update Signature' : 'Confirm Signature'}
                    </Button>
                </div>

                {/* Validation Error */}
                {showValidationError && (
                    <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
                        <p className="text-sm text-destructive flex items-center gap-2">
                            <AlertCircle className="w-4 h-4" aria-hidden="true" />
                            Please provide your signature in the box above before confirming.
                        </p>
                    </div>
                )}

                {/* Legal Notice */}
                <div className="text-xs text-muted-foreground space-y-1 pt-4 border-t">
                    <p className="flex items-center gap-2">
                        <CheckCircle className="w-3 h-3" aria-hidden="true" />
                        Your digital signature is legally equivalent to a handwritten signature
                    </p>
                    <p>
                        Signature timestamp and IP address are recorded for legal compliance
                    </p>
                </div>

                {/* Screen Reader Announcements */}
                <div className="sr-only" aria-live="polite" aria-atomic="true">
                    {isProcessing && "Processing your signature. Please wait."}
                    {hasSignatureBeenProvided && "Signature has been successfully recorded."}
                    {showValidationError && "Signature is required. Please sign in the box above."}
                </div>
            </CardContent>
        </Card>
    );
});

DigitalSignatureSection.displayName = 'DigitalSignatureSection';