import React, { memo, useMemo } from 'react';
import { Building2, Edit, Mail, Phone, MapPin, UserCircle, Briefcase, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useIsMobile } from "@/hooks/use-mobile";
import { FullOnboardingSessionData } from "@/types/onboarding";
import { cn } from "@/utils/utils";

interface ReviewBusinessInfoSectionProps {
  /** Business profile data from Step 1 */
  step1Data: FullOnboardingSessionData['step1_businessProfile'];
  /** Whether the section is in read-only mode */
  readOnly?: boolean;
  /** Whether to show edit controls */
  showEditControls?: boolean;
  /** Optional callback when edit is clicked */
  onEdit?: () => void;
  /** Additional CSS classes */
  className?: string;
}

interface DetailItemProps {
  icon?: React.ElementType;
  label: string;
  value: React.ReactNode;
  required?: boolean;
  isError?: boolean;
  className?: string;
}

const DetailItem = memo<DetailItemProps>(({
  icon: Icon,
  label,
  value,
  required = false,
  isError = false,
  className
}) => {
  const isEmpty = !value || (typeof value === 'string' && value.trim() === '');
  const showError = required && isEmpty;

  return (
    <div className={cn(
      "py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-4",
      "border-b border-border/50 last:border-b-0",
      className
    )}>
      <dt className="text-sm font-medium text-muted-foreground flex items-center gap-2">
        {Icon && (
          <Icon
            className={cn(
              "w-4 h-4",
              showError ? "text-destructive" : "text-primary"
            )}
            aria-hidden="true"
          />
        )}
        <span className="flex items-center gap-1">
          {label}
          {required && (
            <span className="text-destructive" aria-label="required field">
              *
            </span>
          )}
        </span>
      </dt>
      <dd className={cn(
        "mt-1 text-sm sm:mt-0 sm:col-span-2",
        "flex items-center gap-2",
        showError ? "text-destructive" : "text-foreground"
      )}>
        {isEmpty ? (
          <span className="flex items-center gap-2">
            {showError && <AlertCircle className="w-4 h-4" aria-hidden="true" />}
            <span className="italic text-muted-foreground">
              {showError ? 'Required - not provided' : 'Not provided'}
            </span>
          </span>
        ) : (
          <span className="flex items-center gap-2">
            {value}
            {required && !isEmpty && (
              <CheckCircle className="w-4 h-4 text-green-600" aria-hidden="true" />
            )}
          </span>
        )}
      </dd>
    </div>
  );
});

DetailItem.displayName = 'DetailItem';

/**
 * Enhanced BusinessInfoSection component with Radix UI compliance and accessibility features
 * 
 * Features:
 * - Comprehensive validation and error handling
 * - Mobile-responsive design with useIsMobile hook
 * - Proper ARIA attributes and semantic HTML
 * - Type-safe implementation with proper guards
 * - Visual status indicators and completion tracking
 * - Keyboard navigation support
 */
export const BusinessInfoSection = memo<ReviewBusinessInfoSectionProps>(({
  step1Data,
  readOnly = false,
  showEditControls = true,
  onEdit,
  className
}) => {
  const { updateCurrentStep } = useOnboarding();
  const isMobile = useIsMobile();

  // Memoized data extraction with proper type safety
  const businessReg = useMemo(() => step1Data?.businessRegistration, [step1Data?.businessRegistration]);

  const primaryContact = useMemo(() => {
    return step1Data?.contacts?.find(c => c.contact_type === 'Main Contact') || step1Data?.contacts?.[0];
  }, [step1Data?.contacts]);

  const primaryAddress = useMemo(() => {
    return step1Data?.addresses?.find(a => a.address_type === 'Property') || step1Data?.addresses?.[0];
  }, [step1Data?.addresses]);

  // Validation state
  const validationState = useMemo(() => {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!businessReg?.full_business_name?.trim()) {
      errors.push('Business name is required');
    }
    if (!businessReg?.abn?.trim()) {
      errors.push('ABN is required');
    }
    if (!primaryContact?.name?.trim()) {
      errors.push('Primary contact name is required');
    }
    if (!primaryContact?.email?.trim()) {
      errors.push('Primary contact email is required');
    }
    if (!primaryAddress?.full_address_text?.trim()) {
      errors.push('Primary address is required');
    }

    if (!businessReg?.trading_name?.trim()) {
      warnings.push('Trading name not provided');
    }
    if (!primaryContact?.phone?.trim()) {
      warnings.push('Primary contact phone not provided');
    }

    return {
      isValid: errors.length === 0,
      hasWarnings: warnings.length > 0,
      errors,
      warnings,
      completionRate: Math.round(
        ((businessReg ? 1 : 0) + (primaryContact ? 1 : 0) + (primaryAddress ? 1 : 0)) / 3 * 100
      )
    };
  }, [businessReg, primaryContact, primaryAddress]);

  const handleEdit = () => {
    if (onEdit) {
      onEdit();
    } else {
      updateCurrentStep(1); // Navigate to Step 1: Business Profile
    }
  };

  return (
    <Card className={cn("transition-all duration-200", className)}>
      <CardHeader className="pb-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
          <div className="flex-1">
            <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
              <Briefcase className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
              <span>Business Profile Review</span>
              <div className="flex items-center gap-2">
                <Badge
                  variant={validationState.isValid ? "default" : "destructive"}
                  className="text-xs"
                >
                  {validationState.completionRate}% Complete
                </Badge>
                {validationState.hasWarnings && (
                  <Badge variant="outline" className="text-xs">
                    {validationState.warnings.length} Warning{validationState.warnings.length > 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
            </CardTitle>
            <CardDescription className="mt-2">
              Review your business registration, primary contact, and address information from Step 1.
              {!validationState.isValid && (
                <span className="block mt-1 text-destructive font-medium">
                  {validationState.errors.length} required field{validationState.errors.length > 1 ? 's' : ''} missing
                </span>
              )}
            </CardDescription>
          </div>

          {showEditControls && !readOnly && (
            <Button
              variant="outline"
              size={isMobile ? "sm" : "default"}
              onClick={handleEdit}
              className="flex-shrink-0"
              aria-label="Edit business profile information"
            >
              <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
              <span className="hidden sm:inline">Edit Step 1</span>
              <span className="sm:hidden">Edit</span>
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* Business Registration Section */}
        <section aria-labelledby="business-registration-heading">
          <h3
            id="business-registration-heading"
            className="text-lg font-semibold flex items-center gap-2 mb-4"
          >
            <Building2 className="w-5 h-5 text-primary" aria-hidden="true" />
            <span>Business Registration</span>
            {!businessReg && (
              <Badge variant="destructive" className="text-xs">
                Missing
              </Badge>
            )}
          </h3>

          {businessReg ? (
            <dl className="space-y-0">
              <DetailItem
                label="Full Business Name"
                value={businessReg.full_business_name}
                required
              />
              <DetailItem
                label="Trading Name"
                value={businessReg.trading_name}
              />
              <DetailItem
                label="Business Structure"
                value={businessReg.business_structure}
                required
              />
              <DetailItem
                label="ABN"
                value={businessReg.abn}
                required
              />
              {businessReg.business_structure === 'Company' && (
                <DetailItem
                  label="ACN"
                  value={businessReg.acn}
                  required
                />
              )}
              <DetailItem
                label="Registered for GST"
                value={businessReg.is_gst_registered ? 'Yes' : 'No'}
              />
            </dl>
          ) : (
            <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
              <p className="text-sm text-destructive flex items-center gap-2">
                <AlertCircle className="w-4 h-4" aria-hidden="true" />
                Business registration information is missing. Please complete Step 1.
              </p>
            </div>
          )}
        </section>

        {/* Primary Contact Section */}
        <section aria-labelledby="primary-contact-heading">
          <h3
            id="primary-contact-heading"
            className="text-lg font-semibold flex items-center gap-2 mb-4"
          >
            <UserCircle className="w-5 h-5 text-primary" aria-hidden="true" />
            <span>Primary Contact</span>
            {!primaryContact && (
              <Badge variant="destructive" className="text-xs">
                Missing
              </Badge>
            )}
          </h3>

          {primaryContact ? (
            <dl className="space-y-0">
              <DetailItem
                label="Contact Name"
                value={primaryContact.name}
                required
              />
              <DetailItem
                icon={Mail}
                label="Contact Email"
                value={primaryContact.email}
                required
              />
              <DetailItem
                icon={Phone}
                label="Contact Phone"
                value={primaryContact.phone}
              />
              <DetailItem
                label="Role/Title"
                value={primaryContact.title_or_firm}
              />
              <DetailItem
                label="Contact Type"
                value={primaryContact.contact_type}
              />
            </dl>
          ) : (
            <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
              <p className="text-sm text-destructive flex items-center gap-2">
                <AlertCircle className="w-4 h-4" aria-hidden="true" />
                Primary contact information is missing. Please add contact details in Step 1.
              </p>
            </div>
          )}
        </section>

        {/* Primary Address Section */}
        <section aria-labelledby="primary-address-heading">
          <h3
            id="primary-address-heading"
            className="text-lg font-semibold flex items-center gap-2 mb-4"
          >
            <MapPin className="w-5 h-5 text-primary" aria-hidden="true" />
            <span>Primary Address</span>
            {!primaryAddress && (
              <Badge variant="destructive" className="text-xs">
                Missing
              </Badge>
            )}
          </h3>

          {primaryAddress ? (
            <dl className="space-y-0">
              <DetailItem
                label="Address Type"
                value={primaryAddress.address_type}
              />
              <DetailItem
                label="Full Address"
                value={primaryAddress.full_address_text}
                required
              />
              {primaryAddress.street && (
                <DetailItem
                  label="Street"
                  value={primaryAddress.street}
                />
              )}
              {primaryAddress.locality && (
                <DetailItem
                  label="Locality"
                  value={primaryAddress.locality}
                />
              )}
              {primaryAddress.state && (
                <DetailItem
                  label="State/Territory"
                  value={primaryAddress.state}
                />
              )}
              {primaryAddress.postcode && (
                <DetailItem
                  label="Postcode"
                  value={primaryAddress.postcode}
                />
              )}
              {primaryAddress.country && (
                <DetailItem
                  label="Country"
                  value={primaryAddress.country}
                />
              )}
            </dl>
          ) : (
            <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
              <p className="text-sm text-destructive flex items-center gap-2">
                <AlertCircle className="w-4 h-4" aria-hidden="true" />
                Primary address information is missing. Please add address details in Step 1.
              </p>
            </div>
          )}
        </section>

        {/* Overall Status Summary */}
        {!businessReg && !primaryContact && !primaryAddress && (
          <div className="p-6 text-center rounded-lg border border-dashed border-muted-foreground/30">
            <AlertCircle className="w-8 h-8 mx-auto mb-2 text-muted-foreground" aria-hidden="true" />
            <p className="text-muted-foreground mb-4">
              No business profile information has been provided yet.
            </p>
            {showEditControls && !readOnly && (
              <Button
                variant="outline"
                onClick={handleEdit}
                aria-label="Start adding business profile information"
              >
                <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
                Complete Step 1
              </Button>
            )}
          </div>
        )}

        {/* Validation Summary */}
        {(validationState.errors.length > 0 || validationState.warnings.length > 0) && (
          <div className="space-y-3">
            {validationState.errors.length > 0 && (
              <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
                <h4 className="font-medium text-destructive mb-2 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" aria-hidden="true" />
                  Required Information Missing
                </h4>
                <ul className="text-sm text-destructive space-y-1">
                  {validationState.errors.map((error, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-1 h-1 bg-destructive rounded-full" aria-hidden="true" />
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {validationState.warnings.length > 0 && (
              <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
                <h4 className="font-medium text-yellow-800 mb-2">
                  Optional Information
                </h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  {validationState.warnings.map((warning, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-1 h-1 bg-yellow-600 rounded-full" aria-hidden="true" />
                      {warning}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

BusinessInfoSection.displayName = 'BusinessInfoSection';