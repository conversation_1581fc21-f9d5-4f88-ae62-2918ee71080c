import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Edit, Cloud, Shield, AlertCircle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { useDebounce } from '@/hooks/useDebounce';
import { FullOnboardingSessionData, DataMigration, Permission } from '@/types/onboarding';
import { Database } from '@/types/database.types';
import { cn } from '@/utils/utils';

// Type aliases for component use
type DataMigrationRow = DataMigration;
type PermissionRow = Permission;
type TableInsert<T extends keyof Database['farms']['Tables']> = Database['farms']['Tables'][T]['Insert'];

// Constants
const CLOUD_STORAGE_OPTIONS = [
    { value: 'google_drive', label: 'Google Drive', description: 'Sync with Google Drive storage' },
    { value: 'dropbox', label: 'Dropbox', description: 'Connect to Dropbox files' },
    { value: 'one_drive', label: 'Microsoft OneDrive', description: 'Integrate with OneDrive' },
    { value: 'other', label: 'Other Cloud Provider', description: 'Specify custom cloud storage' },
    { value: 'none', label: 'None / Not Applicable', description: 'No cloud storage integration needed' },
] as const;

export const AVAILABLE_PERMISSIONS = [
    { 
        id: 'bookkeeping_records', 
        label: 'Bookkeeping Records Access', 
        description: 'Allow access to financial transaction data, ledgers, and accounting reports.',
        category: 'Financial'
    },
    { 
        id: 'cloud_storage_documents', 
        label: 'Cloud Storage Documents', 
        description: 'Allow access to relevant documents stored in your cloud provider.',
        category: 'Documents'
    },
    { 
        id: 'bank_feeds_transactions', 
        label: 'Bank Feeds & Transactions', 
        description: 'Allow secure connection to bank feeds for automated transaction retrieval.',
        category: 'Financial'
    },
] as const;

interface DataMigrationSectionProps {
    step4Id?: string | null;
    dataMigrationData?: DataMigrationRow | null;
    permissionsData?: PermissionRow[];
    disabled?: boolean;
    onEdit?: () => void;
    className?: string;
}

interface ValidationState {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    completionRate: number;
}

/**
 * Enhanced DataMigrationSection component with comprehensive form management
 * 
 * Features:
 * - Hook-based form field management with debouncing
 * - Real-time validation and error handling
 * - Mobile-responsive design patterns
 * - Accessibility-compliant ARIA attributes
 * - Visual feedback for permissions and cloud storage
 * - Type-safe implementation throughout
 */
export const DataMigrationSection = memo<DataMigrationSectionProps>(({
    step4Id,
    dataMigrationData,
    permissionsData = [],
    disabled = false,
    onEdit,
    className,
}) => {
    const {
        upsertRecord,
        setPermissions,
        loading: contextLoading,
        ensureLinkedRecord,
        refreshSessionData
    } = useOnboarding();
    const { toast } = useToast();
    const isMobile = useIsMobile();

    // Local state with proper typing
    const [primaryCloudStorage, setPrimaryCloudStorage] = useState<string>(
        dataMigrationData?.primary_cloud_storage || ''
    );
    const [filingSystemDescription, setFilingSystemDescription] = useState<string>(
        dataMigrationData?.filing_system_description || ''
    );
    const [selectedPermissions, setSelectedPermissions] = useState<string[]>(() =>
        permissionsData.map(p => p.data_type)
    );
    const [isSaving, setIsSaving] = useState(false);
    const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);

    // Debounced filing system description for auto-save
    const debouncedFilingDescription = useDebounce(filingSystemDescription, 1500);

    // Validation state
    const validationState = useMemo<ValidationState>(() => {
        const errors: string[] = [];
        const warnings: string[] = [];
        
        if (!primaryCloudStorage) {
            errors.push('Cloud storage provider selection is required');
        }
        
        if (primaryCloudStorage === 'other' && !filingSystemDescription?.trim()) {
            errors.push('Please describe your custom cloud storage setup');
        }
        
        if (selectedPermissions.length === 0) {
            warnings.push('No data access permissions granted');
        }
        
        if (primaryCloudStorage && primaryCloudStorage !== 'none' && !filingSystemDescription?.trim()) {
            warnings.push('Filing system description would help with data migration');
        }
        
        const totalFields = 3; // cloud storage, filing system, permissions
        const completedFields = [
            primaryCloudStorage ? 1 : 0,
            filingSystemDescription?.trim() ? 1 : 0,
            selectedPermissions.length > 0 ? 1 : 0
        ].reduce((sum, val) => sum + val, 0);
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completionRate: Math.round((completedFields / totalFields) * 100)
        };
    }, [primaryCloudStorage, filingSystemDescription, selectedPermissions]);

    // Update local state when props change
    useEffect(() => {
        setPrimaryCloudStorage(dataMigrationData?.primary_cloud_storage || '');
        setFilingSystemDescription(dataMigrationData?.filing_system_description || '');
        setSelectedPermissions(permissionsData.map(p => p.data_type));
    }, [dataMigrationData, permissionsData]);

    // Auto-save filing system description
    useEffect(() => {
        if (debouncedFilingDescription !== (dataMigrationData?.filing_system_description || '')) {
            handleDataMigrationChange('filing_system_description', debouncedFilingDescription);
        }
    }, [debouncedFilingDescription, dataMigrationData?.filing_system_description]);

    const handleDataMigrationChange = useCallback(async (
        field: keyof DataMigrationRow, 
        value: string | null
    ) => {
        if (!step4Id || disabled || contextLoading || isSaving) return;

        const updatePayload: Partial<DataMigrationRow> = { [field]: value };
        
        setIsSaving(true);
        try {
            // Ensure data_migration record exists for this step4Id
            const dmRecord = await ensureLinkedRecord({
                tableName: 'data_migration',
                linkCondition: { step_4_id: step4Id },
                defaultValues: { step_4_id: step4Id, ...updatePayload },
                existingRecordFromSession: dataMigrationData,
            });

            if (dmRecord?.id && dataMigrationData?.[field] !== value) {
                await upsertRecord('data_migration', { 
                    id: dmRecord.id, 
                    ...updatePayload 
                } as TableInsert<'data_migration'>);
                
                setLastSaveTime(new Date());
                
                toast({
                    title: "Data Migration Updated",
                    description: `${field === 'primary_cloud_storage' ? 'Cloud storage' : 'Filing system'} preferences saved successfully.`,
                    duration: 2000,
                });
            } else if (!dmRecord) {
                throw new Error("Failed to ensure data migration record exists.");
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            toast({ 
                title: "Error Saving Data Migration", 
                description: errorMessage, 
                variant: "destructive",
                duration: 5000
            });
        } finally {
            setIsSaving(false);
        }
    }, [step4Id, disabled, contextLoading, isSaving, ensureLinkedRecord, dataMigrationData, upsertRecord, toast]);

    const handlePermissionChange = useCallback(async (permissionId: string, checked: boolean) => {
        if (!step4Id || disabled || contextLoading || isSaving) return;

        setIsSaving(true);
        try {
            // Optimistic UI update
            const newSelectedPermissionIds = checked 
                ? [...selectedPermissions, permissionId]
                : selectedPermissions.filter(id => id !== permissionId);
            
            setSelectedPermissions(newSelectedPermissionIds);

            // Prepare payload for context's setPermissions function
            const permissionsToSetPayload: TableInsert<'permissions'>[] = newSelectedPermissionIds.map(dataType => ({
                step_4_id: step4Id,
                data_type: dataType,
                access_level: 'full_access' as const,
            }));

            const success = await setPermissions(permissionsToSetPayload, step4Id);

            if (success) {
                setLastSaveTime(new Date());
                toast({
                    title: "Permissions Updated",
                    description: `${checked ? 'Granted' : 'Revoked'} access permission successfully.`,
                    duration: 2000,
                });
            } else {
                // Revert optimistic update on failure
                setSelectedPermissions(permissionsData.map(p => p.data_type));
                toast({
                    title: "Permission Update Failed",
                    description: "Could not update permission. Please try again.",
                    variant: "destructive",
                    duration: 5000,
                });
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            toast({ 
                title: "Error Updating Permissions", 
                description: errorMessage, 
                variant: "destructive",
                duration: 5000
            });
            // Revert optimistic update
            setSelectedPermissions(permissionsData.map(p => p.data_type));
        } finally {
            setIsSaving(false);
        }
    }, [step4Id, disabled, contextLoading, isSaving, selectedPermissions, setPermissions, permissionsData, toast]);

    const isOperationDisabled = disabled || contextLoading || isSaving;

    return (
        <Card className={cn("transition-all duration-200", className)}>
            <CardHeader className="pb-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                    <div className="flex-1">
                        <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
                            <Cloud className="w-5 h-5 text-primary flex-shrink-0" aria-hidden="true" />
                            <span>Data Migration & Access Permissions</span>
                            <div className="flex items-center gap-2">
                                <Badge 
                                    variant={validationState.isValid ? "default" : "destructive"}
                                    className="text-xs"
                                >
                                    {validationState.completionRate}% Complete
                                </Badge>
                                {validationState.warnings.length > 0 && (
                                    <Badge variant="outline" className="text-xs">
                                        {validationState.warnings.length} Warning{validationState.warnings.length > 1 ? 's' : ''}
                                    </Badge>
                                )}
                            </div>
                        </CardTitle>
                        <CardDescription className="mt-2">
                            Configure your data storage preferences and grant necessary access permissions for our services.
                            {!validationState.isValid && (
                                <span className="block mt-1 text-destructive font-medium">
                                    {validationState.errors.length} required field{validationState.errors.length > 1 ? 's' : ''} missing
                                </span>
                            )}
                        </CardDescription>
                    </div>
                    
                    {onEdit && (
                        <Button 
                            variant="outline" 
                            size={isMobile ? "sm" : "default"}
                            onClick={onEdit} 
                            disabled={isOperationDisabled}
                            className="flex-shrink-0"
                            aria-label="Edit data migration settings"
                        >
                            <Edit className="w-4 h-4 mr-2" aria-hidden="true" />
                            <span className="hidden sm:inline">Edit Settings</span>
                            <span className="sm:hidden">Edit</span>
                        </Button>
                    )}
                </div>
            </CardHeader>
            
            <CardContent className="space-y-8">
                {/* Cloud Storage Selection */}
                <section aria-labelledby="cloud-storage-heading">
                    <div className="space-y-3">
                        <Label htmlFor="primaryCloudStorage" className="text-base font-medium">
                            Primary Cloud Storage Provider 
                            <span className="text-destructive ml-1" aria-label="required field">*</span>
                        </Label>
                        <Select
                            value={primaryCloudStorage}
                            onValueChange={(value) => {
                                setPrimaryCloudStorage(value);
                                handleDataMigrationChange('primary_cloud_storage', value);
                            }}
                            disabled={isOperationDisabled}
                            name="primaryCloudStorage"
                            required
                        >
                            <SelectTrigger 
                                id="primaryCloudStorage"
                                className={cn(
                                    "transition-colors",
                                    !primaryCloudStorage && "border-destructive/50"
                                )}
                                aria-describedby="cloud-storage-description"
                            >
                                <SelectValue placeholder="Select your primary cloud storage provider..." />
                            </SelectTrigger>
                            <SelectContent>
                                {CLOUD_STORAGE_OPTIONS.map(option => (
                                    <SelectItem key={option.value} value={option.value}>
                                        <div className="flex flex-col">
                                            <span className="font-medium">{option.label}</span>
                                            <span className="text-xs text-muted-foreground">{option.description}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <p id="cloud-storage-description" className="text-sm text-muted-foreground">
                            This helps us understand how to best migrate your existing data and documents.
                        </p>
                    </div>
                </section>

                {/* Filing System Description */}
                <section aria-labelledby="filing-system-heading">
                    <div className="space-y-3">
                        <Label htmlFor="filingSystemDescription" className="text-base font-medium">
                            Filing System Description
                            {primaryCloudStorage === 'other' && (
                                <span className="text-destructive ml-1" aria-label="required field">*</span>
                            )}
                            {!primaryCloudStorage || primaryCloudStorage === 'none' ? (
                                <span className="text-muted-foreground font-normal ml-2">(Optional)</span>
                            ) : (
                                <span className="text-muted-foreground font-normal ml-2">(Recommended)</span>
                            )}
                        </Label>
                        <Textarea
                            id="filingSystemDescription"
                            value={filingSystemDescription}
                            onChange={(e) => setFilingSystemDescription(e.target.value)}
                            placeholder="Describe your file organization system (e.g., 'Folders by year, then by supplier/customer; invoices use reference numbers...')"
                            rows={4}
                            disabled={isOperationDisabled}
                            maxLength={500}
                            className={cn(
                                "transition-colors",
                                primaryCloudStorage === 'other' && !filingSystemDescription?.trim() && "border-destructive/50"
                            )}
                            aria-describedby="filing-system-description"
                        />
                        <div className="flex justify-between items-center">
                            <p id="filing-system-description" className="text-sm text-muted-foreground">
                                Help us understand your current file organization for smoother data migration.
                            </p>
                            <span className="text-xs text-muted-foreground">
                                {filingSystemDescription.length}/500
                            </span>
                        </div>
                    </div>
                </section>

                {/* Access Permissions */}
                <section aria-labelledby="permissions-heading">
                    <div className="space-y-4">
                        <div>
                            <h3 id="permissions-heading" className="text-base font-medium flex items-center gap-2">
                                <Shield className="w-4 h-4 text-primary" aria-hidden="true" />
                                Data Access Permissions
                            </h3>
                            <p className="text-sm text-muted-foreground mt-1">
                                Grant us permission to access the following data types. This enables us to provide our services effectively.
                            </p>
                        </div>
                        
                        <div className="grid gap-4">
                            {AVAILABLE_PERMISSIONS.map((permission) => (
                                <div 
                                    key={permission.id} 
                                    className={cn(
                                        "flex items-start space-x-3 p-4 rounded-lg border transition-colors",
                                        selectedPermissions.includes(permission.id) 
                                            ? "bg-primary/5 border-primary/20" 
                                            : "bg-background/30 border-border",
                                        "hover:bg-primary/10"
                                    )}
                                >
                                    <Checkbox
                                        id={`permission-${permission.id}`}
                                        checked={selectedPermissions.includes(permission.id)}
                                        onCheckedChange={(checked) => handlePermissionChange(permission.id, !!checked)}
                                        disabled={isOperationDisabled}
                                        aria-labelledby={`permission-label-${permission.id}`}
                                        aria-describedby={`permission-desc-${permission.id}`}
                                        className="mt-1"
                                    />
                                    <div className="flex-1 grid gap-2">
                                        <div className="flex items-center gap-2">
                                            <Label 
                                                htmlFor={`permission-${permission.id}`} 
                                                id={`permission-label-${permission.id}`} 
                                                className="font-medium cursor-pointer"
                                            >
                                                {permission.label}
                                            </Label>
                                            <Badge variant="outline" className="text-xs">
                                                {permission.category}
                                            </Badge>
                                            {selectedPermissions.includes(permission.id) && (
                                                <CheckCircle className="w-4 h-4 text-green-600" aria-hidden="true" />
                                            )}
                                        </div>
                                        <p id={`permission-desc-${permission.id}`} className="text-sm text-muted-foreground">
                                            {permission.description}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        {selectedPermissions.length === 0 && (
                            <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
                                <p className="text-sm text-yellow-800 flex items-center gap-2">
                                    <AlertCircle className="w-4 h-4" aria-hidden="true" />
                                    No permissions granted. This may limit our ability to provide comprehensive services.
                                </p>
                            </div>
                        )}
                    </div>
                </section>

                {/* Status Indicator */}
                {(isSaving || lastSaveTime) && (
                    <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        {isSaving ? (
                            <div className="flex items-center text-sm text-muted-foreground">
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" aria-hidden="true" />
                                <span>Saving preferences...</span>
                            </div>
                        ) : lastSaveTime && (
                            <div className="flex items-center text-sm text-green-700">
                                <CheckCircle className="w-4 h-4 mr-2" aria-hidden="true" />
                                <span>
                                    Last saved at {lastSaveTime.toLocaleTimeString()}
                                </span>
                            </div>
                        )}
                    </div>
                )}

                {/* Validation Summary */}
                {validationState.errors.length > 0 && (
                    <div className="p-4 rounded-lg border border-destructive/20 bg-destructive/5">
                        <h4 className="font-medium text-destructive mb-2 flex items-center gap-2">
                            <AlertCircle className="w-4 h-4" aria-hidden="true" />
                            Required Information Missing
                        </h4>
                        <ul className="text-sm text-destructive space-y-1">
                            {validationState.errors.map((error, index) => (
                                <li key={index} className="flex items-center gap-2">
                                    <span className="w-1 h-1 bg-destructive rounded-full" aria-hidden="true" />
                                    {error}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </CardContent>
        </Card>
    );
});

DataMigrationSection.displayName = 'DataMigrationSection';