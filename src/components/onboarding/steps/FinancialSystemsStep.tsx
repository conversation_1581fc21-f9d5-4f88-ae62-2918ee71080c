import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { useFormManagement } from '@/hooks/use-form-management';
import { useSelectiveRefresh } from '@/hooks/use-selective-refresh';
import { useToast } from '@/hooks/use-toast';
import { StepLoadingState, StepErrorState } from '@/components/ui/step-status';
import { BookkeepingForm } from '@/components/form_components/BookkeepingForm';
import { PayrollForm } from '@/components/form_components/PayrollForm';
import { AssetForm } from '@/components/form_components/AssetForm';
import { AssetCsvImport } from '@/components/form_components/AssetCsvImport';
import { EntityFormList } from '@/components/form_components/EntityFormList';
import { FormSection } from '@/components/form_components/FormSection';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Upload, Download, AlertTriangle, CheckCircle } from 'lucide-react';
import {
  BookkeepingFormData,
  PayrollFormData,
  AssetFormData,
  EntityFormRenderProps,
} from '@/types/form-components';
import { TableRow, TableInsert, TableUpdate } from '@/contexts/OnboardingContext';
import { getStepId } from '@/utils/onboarding-context-helpers';
import { logger } from '@/utils/logger';
import { isValidUUID } from '@/utils/uuid-validation';
import { FullOnboardingSessionData } from '@/types/onboarding';

/**
 * FinancialSystemsStep Component - Step 3 of Onboarding
 * 
 * Manages bookkeeping, payroll, and asset information for the farm business.
 * Uses consolidated hooks for form management and entity list management.
 * 
 * Architecture:
 * 1. Initialize step using useEnhancedStepInit
 * 2. Set up form management for bookkeeping and payroll (single records)
 * 3. Set up entity list management for assets (multiple records)
 * 4. Provide CSV import functionality for assets
 */
export const FinancialSystemsStep: React.FC = () => {
  const {
    sessionData,
    createRecord,
    updateRecord,
    deleteRecord,
    loading,
    error,
    ensureStep3FinancialSystemsRecordExists
  } = useOnboarding();

  const { toast } = useToast();
  const [csvImportError, setCsvImportError] = useState<string>('');
  const [csvImportSuccess, setCsvImportSuccess] = useState<boolean>(false);

  // Extract session ID with validation
  const sessionId = useMemo(() => {
    if (!sessionData?.onboardingSession?.id) {
      logger.warn('FinancialSystemsStep: Missing session ID');
      return '';
    }

    if (!isValidUUID(sessionData.onboardingSession.id)) {
      logger.error('FinancialSystemsStep: Invalid session ID format:', sessionData.onboardingSession.id);
      return '';
    }

    return sessionData.onboardingSession.id;
  }, [sessionData?.onboardingSession?.id]);

  // Step initialization with comprehensive error handling
  const {
    isLoading: stepLoading,
    effectiveStepId,
    initializationError
  } = useEnhancedStepInit({
    stepName: 'FinancialSystemsStep',
    sessionId,
    contextLoading: loading,
    existingStepId: getStepId(sessionData, 'step3'),
    ensureStepFunction: ensureStep3FinancialSystemsRecordExists,
  });

  // Early return for loading or error states
  if (stepLoading || loading) {
    return <StepLoadingState stepName="FinancialSystemsStep" message="Initializing financial systems configuration..." />;
  }

  if (initializationError || error) {
    return (
      <StepErrorState
        stepName="FinancialSystemsStep"
        error={initializationError || error || 'Unknown error occurred'}
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (!effectiveStepId) {
    return (
      <StepErrorState
        stepName="FinancialSystemsStep"
        error="Unable to initialize financial systems step. Please try again."
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (!isValidUUID(effectiveStepId)) {
    logger.error('FinancialSystemsStep: Invalid step ID format:', effectiveStepId);
    return (
      <StepErrorState
        stepName="FinancialSystemsStep"
        error="Invalid step configuration. Please contact support."
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <FinancialSystemsStepContent
      stepId={effectiveStepId}
      sessionData={sessionData}
      onCsvImportError={setCsvImportError}
      onCsvImportSuccess={setCsvImportSuccess}
      csvImportError={csvImportError}
      csvImportSuccess={csvImportSuccess}
    />
  );
};

/**
 * Main content component for Financial Systems Step
 * Separated for better performance and cleaner code organization
 */
interface FinancialSystemsStepContentProps {
  stepId: string;
  sessionData: FullOnboardingSessionData;
  onCsvImportError: (error: string) => void;
  onCsvImportSuccess: (success: boolean) => void;
  csvImportError: string;
  csvImportSuccess: boolean;
}

const FinancialSystemsStepContent: React.FC<FinancialSystemsStepContentProps> = React.memo(({
  stepId,
  sessionData,
  onCsvImportError,
  onCsvImportSuccess,
  csvImportError,
  csvImportSuccess,
}) => {
  const {
    createRecord,
    updateRecord,
    deleteRecord,
    loading
  } = useOnboarding();

  const { toast } = useToast();

  // Selective refresh for optimized updates
  const { selectiveRefresh } = useSelectiveRefresh();

  // Extract existing data with proper type safety - FIX: Access single records directly, not as arrays
  const existingBookkeeping = useMemo(() => {
    const data = sessionData?.step3_financialSystems?.bookkeeping; // Remove [0] - it's a single record
    if (!data) return null;

    // Ensure data matches BookkeepingFormData structure
    return {
      id: data.id,
      step_3_id: data.step_3_id || stepId,
      current_software: data.current_software || '',
      software_version: data.software_version || null,
      access_credentials: data.access_credentials || null,
      accountant_access_level: data.accountant_access_level || null,
      accountant_has_access: Boolean(data.accountant_has_access),
      bas_lodgement_frequency: data.bas_lodgement_frequency || 'Quarterly',
      has_bank_feeds_enabled: Boolean(data.has_bank_feeds_enabled),
      created_at: data.created_at,
      updated_at: data.updated_at,
    } satisfies BookkeepingFormData & Record<string, unknown>;
  }, [sessionData?.step3_financialSystems?.bookkeeping, stepId]);

  const existingPayroll = useMemo(() => {
    const data = sessionData?.step3_financialSystems?.payroll; // Remove [0] - it's a single record
    if (!data) return null;

    // Ensure data matches PayrollFormData structure
    return {
      id: data.id,
      step_3_id: data.step_3_id || stepId,
      is_payroll_processing_needed: Boolean(data.is_payroll_processing_needed),
      employee_count: data.employee_count || null,
      current_payroll_software: data.current_payroll_software || null,
      payroll_frequency: data.payroll_frequency || null,
      superannuation_fund: data.superannuation_fund || null,
      workers_compensation_policy: data.workers_compensation_policy || null,
      encrypted_access_credentials: data.encrypted_access_credentials || null,
      is_access_to_software_granted: Boolean(data.is_access_to_software_granted),
      created_at: data.created_at,
      updated_at: data.updated_at,
    } satisfies PayrollFormData & Record<string, unknown>;
  }, [sessionData?.step3_financialSystems?.payroll, stepId]);

  const existingAssets = useMemo(() => {
    const assets = sessionData?.step3_financialSystems?.assets || [];
    return assets.map((asset) => ({
      id: asset.id,
      step_3_id: asset.step_3_id || stepId,
      asset_category: asset.asset_category,
      asset_type: asset.asset_type || '',
      make_or_provider: asset.make_or_provider || null,
      registration_or_policy_number: asset.registration_or_policy_number || null,
      renewal_date: asset.renewal_date || null,
      coverage_amount: asset.coverage_amount || null,
      excess_amount: asset.excess_amount || null,
      model_year: asset.model_year || null,
      policy_type: asset.policy_type || null,
      purchase_date: asset.purchase_date || null,
      purchase_price: asset.purchase_price || null,
      serial_number: asset.serial_number || null,
      created_at: asset.created_at,
      updated_at: asset.updated_at,
    } satisfies AssetFormData));
  }, [sessionData?.step3_financialSystems?.assets, stepId]);

  // Bookkeeping form management
  const bookkeepingFormManager = useFormManagement<BookkeepingFormData & Record<string, unknown>>({
    stepId: existingBookkeeping?.id || null,
    tableName: 'bookkeeping',
    initialData: existingBookkeeping,
    displayName: 'Bookkeeping Configuration',
    debounceMs: 1000,
  });

  // Payroll form management
  const payrollFormManager = useFormManagement<PayrollFormData & Record<string, unknown>>({
    stepId: existingPayroll?.id || null,
    tableName: 'payroll',
    initialData: existingPayroll,
    displayName: 'Payroll Configuration',
    debounceMs: 1000,
  });

  // Asset list management - FIX: Use initialEntities instead of entities
  const assetManagement = useEntityListManagement({
    stepId,
    tableName: 'assets',
    initialEntities: existingAssets, // FIX: Changed from 'entities' to 'initialEntities'
    createDefaultEntity: (stepId: string): Partial<AssetFormData> => ({
      step_3_id: stepId,
      asset_category: 'Vehicle',
      asset_type: '',
      make_or_provider: null,
      registration_or_policy_number: null,
      renewal_date: null,
      coverage_amount: null,
      excess_amount: null,
      model_year: null,
      policy_type: null,
      purchase_date: null,
      purchase_price: null,
      serial_number: null,
    }),
    entityDisplayName: 'Asset',
  });

  // Asset form renderer for EntityFormList
  const renderAssetForm = useCallback((props: EntityFormRenderProps<AssetFormData>) => {
    return <AssetForm {...props} />;
  }, []);

  // CSV import handler
  const handleCsvImportComplete = useCallback(() => {
    onCsvImportSuccess(true);
    onCsvImportError('');

    // FIX: Use selectiveRefresh instead of triggerRefresh
    selectiveRefresh({
      tables: ['assets'],
      debounceMs: 1000
    });

    // Clear success message after 5 seconds
    setTimeout(() => {
      onCsvImportSuccess(false);
    }, 5000);
  }, [onCsvImportSuccess, onCsvImportError, selectiveRefresh]);

  // Calculate completion status
  const completionStatus = useMemo(() => {
    const hasBookkeeping = bookkeepingFormManager.formData?.current_software;
    const hasPayrollDecision = payrollFormManager.formData?.is_payroll_processing_needed !== undefined;
    const payrollComplete = !payrollFormManager.formData?.is_payroll_processing_needed ||
      (payrollFormManager.formData?.employee_count && payrollFormManager.formData?.payroll_frequency);
    const hasAssets = existingAssets.length > 0;

    return {
      bookkeeping: Boolean(hasBookkeeping),
      payroll: Boolean(hasPayrollDecision && payrollComplete),
      assets: Boolean(hasAssets),
      overall: Boolean(hasBookkeeping && hasPayrollDecision && payrollComplete),
    };
  }, [bookkeepingFormManager.formData, payrollFormManager.formData, existingAssets.length]);

  // Determine if forms should be disabled
  const baseDisabled = loading;
  const formsDisabled = baseDisabled || !stepId;

  return (
    <div className="space-y-8" role="main" aria-label="Financial Systems Configuration">
      {/* Progress indicator */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Financial Systems Setup</span>
            <div className="flex items-center space-x-2">
              {completionStatus.overall && (
                <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Complete
                </Badge>
              )}
              <Badge variant="outline">
                Step 3 of 4
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Configure your financial systems, including bookkeeping software, payroll processing,
            and asset management. This information helps us set up proper integrations and reporting.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              {completionStatus.bookkeeping ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <div className="h-4 w-4 rounded-full border-2 border-muted-foreground" />
              )}
              <span className={completionStatus.bookkeeping ? 'text-green-700' : 'text-muted-foreground'}>
                Bookkeeping Setup
              </span>
            </div>

            <div className="flex items-center space-x-2">
              {completionStatus.payroll ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <div className="h-4 w-4 rounded-full border-2 border-muted-foreground" />
              )}
              <span className={completionStatus.payroll ? 'text-green-700' : 'text-muted-foreground'}>
                Payroll Configuration
              </span>
            </div>

            <div className="flex items-center space-x-2">
              {completionStatus.assets ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <div className="h-4 w-4 rounded-full border-2 border-muted-foreground" />
              )}
              <span className={completionStatus.assets ? 'text-green-700' : 'text-muted-foreground'}>
                Asset Registry
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* CSV Import Status */}
      {csvImportSuccess && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Assets have been successfully imported from your CSV file.
          </AlertDescription>
        </Alert>
      )}

      {csvImportError && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {csvImportError}
          </AlertDescription>
        </Alert>
      )}

      {/* Bookkeeping Configuration */}
      <BookkeepingForm
        formManager={bookkeepingFormManager}
        disabled={formsDisabled}
      />

      <Separator />

      {/* Payroll Configuration */}
      <PayrollForm
        formManager={payrollFormManager}
        disabled={formsDisabled}
      />

      <Separator />

      {/* Asset Management */}
      <div className="space-y-6">
        <FormSection
          title="Asset Registry"
          description="Manage your farm assets including vehicles, equipment, and insurance policies. You can add assets individually or import them from a CSV file."
          disabled={formsDisabled}
        >
          {/* CSV Import Component */}
          <div className="mb-6 p-4 border rounded-lg bg-muted/50">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-sm font-medium mb-1">Bulk Asset Import</h4>
                <p className="text-xs text-muted-foreground">
                  Import multiple assets from a CSV file to save time
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Upload className="h-4 w-4 text-muted-foreground" />
                <Download className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            <AssetCsvImport
              stepId={stepId}
              onImportComplete={handleCsvImportComplete}
              disabled={formsDisabled}
            />
          </div>

          {/* Individual Asset Management */}
          <EntityFormList
            title="Individual Assets"
            description="Add and manage individual assets. Each asset can include registration details, insurance information, and supporting documents."
            data={existingAssets}
            stepId={stepId}
            tableName="assets"
            defaultValues={{
              step_3_id: stepId,
              asset_category: 'Vehicle',
              asset_type: '',
            }}
            renderForm={renderAssetForm}
            disabled={formsDisabled}
            entityDisplayName="Asset"
            entityDisplayNamePlural="Assets"
            showSectionWrapper={false}
            addButtonText="Add New Asset"
            showEntityCount={true}
            maxEntities={50}
            emptyStateMessage="No assets have been added yet. Start by adding your first asset or importing from CSV."
            confirmDelete={true}
          />
        </FormSection>
      </div>
    </div>
  );
});

FinancialSystemsStepContent.displayName = 'FinancialSystemsStepContent';