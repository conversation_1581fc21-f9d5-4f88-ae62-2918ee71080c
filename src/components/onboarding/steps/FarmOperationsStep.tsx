import React from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { StepLoadingState, StepErrorState } from '@/components/ui/step-status';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Loader2, AlertTriangle } from 'lucide-react';
import { ActivityForm } from '@/components/form_components/ActivityForm';
import { LicenseForm } from '@/components/form_components/LicenseForm';
import { SupplierForm } from '@/components/form_components/SupplierForm';
import { ContractForm } from '@/components/form_components/ContractForm';
import { ChemicalUsageForm } from '@/components/form_components/ChemicalUsageForm';
import { DocumentIndicator } from '@/components/form_components/DocumentIndicator';
import {
  ActivityFormData,
  LicenseFormData,
  SupplierFormData,
  ContractFormData,
  ChemicalUsageFormData
} from '@/types/form-components';
import { Database } from '@/types/database.types';

// Database type aliases for entity management
type FarmsSchema = Database['farms'];
type ActivityInsert = FarmsSchema['Tables']['activities']['Insert'];
type ActivityUpdate = FarmsSchema['Tables']['activities']['Update'];
type LicenseInsert = FarmsSchema['Tables']['licenses']['Insert'];
type LicenseUpdate = FarmsSchema['Tables']['licenses']['Update'];
type SupplierInsert = FarmsSchema['Tables']['suppliers']['Insert'];
type SupplierUpdate = FarmsSchema['Tables']['suppliers']['Update'];
type ContractInsert = FarmsSchema['Tables']['contracts']['Insert'];
type ContractUpdate = FarmsSchema['Tables']['contracts']['Update'];
type ChemicalUsageInsert = FarmsSchema['Tables']['chemical_usage']['Insert'];
type ChemicalUsageUpdate = FarmsSchema['Tables']['chemical_usage']['Update'];

/**
 * Entity section component for consistent entity list rendering
 */
interface EntitySectionProps<T extends { id: string }> {
  title: string;
  description: string;
  entities: T[];
  isLoading: boolean;
  canAddEntity: boolean;
  canDeleteEntity: boolean;
  addEntity: () => Promise<void>;
  deleteEntity: (id: string) => Promise<void>;
  operationError: string | null;
  clearOperationError: () => void;
  entityDisplayName: string;
  entityDisplayNamePlural: string;
  addButtonText: string;
  emptyStateMessage: string;
  renderForm: (entity: T, disabled: boolean) => React.ReactNode;
  disabled: boolean;
  minEntities?: number;
  maxEntities?: number;
  entityType: string;
}

const EntitySection = <T extends { id: string }>({
  title,
  description,
  entities,
  isLoading,
  canAddEntity,
  canDeleteEntity,
  addEntity,
  deleteEntity,
  operationError,
  clearOperationError,
  entityDisplayName,
  entityDisplayNamePlural,
  addButtonText,
  emptyStateMessage,
  renderForm,
  disabled,
  minEntities,
  maxEntities,
  entityType,
}: EntitySectionProps<T>) => {
  const [deletingIds, setDeletingIds] = React.useState<Set<string>>(new Set());

  const handleDelete = React.useCallback(async (entityId: string) => {
    if (!canDeleteEntity) return;

    const confirmMessage = `Are you sure you want to delete this ${entityDisplayName}? This action cannot be undone.`;
    if (!window.confirm(confirmMessage)) return;

    setDeletingIds(prev => new Set(prev).add(entityId));
    try {
      await deleteEntity(entityId);
    } finally {
      setDeletingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(entityId);
        return newSet;
      });
    }
  }, [canDeleteEntity, entityDisplayName, deleteEntity]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Entity list */}
        {entities.map((entity, index) => (
          <Card key={entity.id} className="relative">
            <CardContent className="pt-4">
              {/* Entity header with document indicator and delete button */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    {entityDisplayName.charAt(0).toUpperCase() + entityDisplayName.slice(1)} {index + 1}
                  </h4>
                  <DocumentIndicator
                    entityType={entityType}
                    entityId={entity.id}
                    entityName={entityDisplayName}
                    showDetails={false}
                    className="flex-shrink-0"
                  />
                </div>
                
                {/* Delete button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-muted-foreground hover:text-destructive flex-shrink-0"
                  onClick={() => handleDelete(entity.id)}
                  disabled={disabled || deletingIds.has(entity.id) || isLoading || !canDeleteEntity}
                  aria-label={`Delete ${entityDisplayName} ${index + 1}`}
                >
                  {deletingIds.has(entity.id) ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Entity form */}
              {renderForm(entity, disabled || isLoading)}
            </CardContent>
          </Card>
        ))}

        {/* Empty state */}
        {entities.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p className="text-sm">{emptyStateMessage}</p>
          </div>
        )}

        {/* Operation error */}
        {operationError && (
          <div className="rounded-md border border-destructive/20 bg-destructive/5 p-4">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm text-destructive">{operationError}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearOperationError}
                  className="mt-2 h-auto p-0 text-xs text-destructive hover:text-destructive"
                >
                  Dismiss
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Add button and count */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={addEntity}
            disabled={disabled || isLoading || !canAddEntity}
            className="flex items-center space-x-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Plus className="h-4 w-4" />
            )}
            <span>{addButtonText}</span>
          </Button>

          <div className="text-sm text-muted-foreground">
            {entities.length} {entities.length === 1 ? entityDisplayName : entityDisplayNamePlural}
            {maxEntities && ` of ${maxEntities} max`}
          </div>
        </div>

        {/* Constraints info */}
        {(minEntities || maxEntities) && (
          <div className="text-xs text-muted-foreground">
            {minEntities && `Minimum: ${minEntities} ${minEntities === 1 ? entityDisplayName : entityDisplayNamePlural}`}
            {minEntities && maxEntities && ' • '}
            {maxEntities && `Maximum: ${maxEntities} ${maxEntities === 1 ? entityDisplayName : entityDisplayNamePlural}`}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Refactored FarmOperationsStep using consolidated hook system
 * 
 * Key improvements:
 * - Uses useEntityListManagement directly for each entity type
 * - Eliminates EntityFormList wrapper to fix synchronization issues
 * - Proper database state management through consolidated hooks
 * - Consistent error handling and loading states
 * - ~70% reduction in wrapper complexity
 */
export const FarmOperationsStep = () => {
  const {
    sessionData,
    loading: contextLoading,
    sessionId,
    ensureStep2FarmOperationsRecordExists,
  } = useOnboarding();

  // Enhanced step initialization
  const {
    isStepInitializing,
    initializationError,
    handleRetryInitialization,
    isLoading,
    effectiveStepId,
  } = useEnhancedStepInit({
    stepName: 'FarmOperations',
    sessionId,
    contextLoading,
    existingStepId: sessionData?.step2_farmOperations?.id,
    ensureStepFunction: ensureStep2FarmOperationsRecordExists,
  });

  // Base disabled state for all operations
  const baseDisabled = !effectiveStepId || contextLoading || isStepInitializing;

  // Activities Management
  const activitiesManager = useEntityListManagement<ActivityFormData, ActivityInsert, ActivityUpdate>({
    stepId: effectiveStepId,
    tableName: 'activities',
    initialEntities: sessionData?.step2_farmOperations?.activities || [],
    createDefaultEntity: (stepId) => ({
      step_2_id: stepId,
      activity_type: 'Cropping',
      activity_description: null,
      approximate_numbers: null,
      crop_type: null,
      crop_varieties: null,
      livestock_type: null,
    }),
    entityDisplayName: 'activity',
    disabled: baseDisabled,
    minEntities: 1,
    showNotifications: true,
  });

  // Licenses Management
  const licensesManager = useEntityListManagement<LicenseFormData, LicenseInsert, LicenseUpdate>({
    stepId: effectiveStepId,
    tableName: 'licenses',
    initialEntities: sessionData?.step2_farmOperations?.licenses || [],
    createDefaultEntity: (stepId) => ({
      step_2_id: stepId,
      license_type: 'Chemical Permit',
      license_number: null,
      issuing_authority: null,
      issue_date: null,
      expiry_date: null,
    }),
    entityDisplayName: 'license',
    disabled: baseDisabled,
    showNotifications: true,
  });

  // Suppliers Management
  const suppliersManager = useEntityListManagement<SupplierFormData, SupplierInsert, SupplierUpdate>({
    stepId: effectiveStepId,
    tableName: 'suppliers',
    initialEntities: sessionData?.step2_farmOperations?.suppliers || [],
    createDefaultEntity: (stepId) => ({
      step_2_id: stepId,
      supplier_name: '',
      supplier_category: 'Seeds & Plants',
      contact_person: null,
      contact_details: null,
      services_provided: '',
    }),
    entityDisplayName: 'supplier',
    disabled: baseDisabled,
    showNotifications: true,
  });

  // Contracts Management
  const contractsManager = useEntityListManagement<ContractFormData, ContractInsert, ContractUpdate>({
    stepId: effectiveStepId,
    tableName: 'contracts',
    initialEntities: sessionData?.step2_farmOperations?.contracts || [],
    createDefaultEntity: (stepId) => ({
      step_2_id: stepId,
      contract_type: 'Labour Contract',
      contract_description: '',
      start_date: null,
      expiry_date: null,
      contract_value: null,
      supplier_id: null,
    }),
    entityDisplayName: 'contract',
    disabled: baseDisabled,
    showNotifications: true,
  });

  // Chemical Usage Management
  const chemicalUsageManager = useEntityListManagement<ChemicalUsageFormData, ChemicalUsageInsert, ChemicalUsageUpdate>({
    stepId: effectiveStepId,
    tableName: 'chemical_usage',
    initialEntities: sessionData?.step2_farmOperations?.chemicalUsage || [],
    createDefaultEntity: (stepId) => ({
      step_2_id: stepId,
      product_name: '',
      usage_purpose: '',
      application_method: null,
      application_rate: null,
      last_application_date: null,
      manufacturer: null,
      withholding_period_days: null,
    }),
    entityDisplayName: 'chemical usage record',
    disabled: baseDisabled,
    showNotifications: true,
  });

  // Loading state
  if (isLoading && !initializationError) {
    return <StepLoadingState stepName="Farm Operations" message="Loading farm operations essentials..." />;
  }

  // Error state
  if (initializationError) {
    return (
      <StepErrorState
        stepName="Farm Operations"
        error={initializationError}
        onRetry={handleRetryInitialization}
        isRetrying={isStepInitializing}
      />
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold">Step 2: Farm Operations</h2>
        <p className="text-muted-foreground">
          Tell us about your farming activities, licenses, suppliers, and operational details.
        </p>
      </div>

      {/* Farming Activities Section */}
      <EntitySection
        title="Farming Activities"
        description="List your main farming activities (e.g., Cropping, Livestock). Data is stored in the `activities` table."
        entities={activitiesManager.entities}
        isLoading={activitiesManager.isLoading}
        canAddEntity={activitiesManager.canAddEntity}
        canDeleteEntity={activitiesManager.canDeleteEntity}
        addEntity={activitiesManager.addEntity}
        deleteEntity={activitiesManager.deleteEntity}
        operationError={activitiesManager.operationError}
        clearOperationError={activitiesManager.clearOperationError}
        entityDisplayName="activity"
        entityDisplayNamePlural="activities"
        addButtonText="Add Activity"
        emptyStateMessage="No farming activities added yet. Add your primary activity to get started."
        renderForm={(entity, disabled) => (
          <ActivityForm
            entity={entity}
            disabled={disabled}
            onUpdate={activitiesManager.updateEntity}
            onDelete={activitiesManager.deleteEntity}
          />
        )}
        disabled={baseDisabled}
        minEntities={1}
        entityType="activities"
      />

      {/* Licenses & Certifications Section */}
      <EntitySection
        title="Licenses & Certifications"
        description="Record your farming licenses, certifications, and permits. Data is stored in the `licenses` table."
        entities={licensesManager.entities}
        isLoading={licensesManager.isLoading}
        canAddEntity={licensesManager.canAddEntity}
        canDeleteEntity={licensesManager.canDeleteEntity}
        addEntity={licensesManager.addEntity}
        deleteEntity={licensesManager.deleteEntity}
        operationError={licensesManager.operationError}
        clearOperationError={licensesManager.clearOperationError}
        entityDisplayName="license"
        entityDisplayNamePlural="licenses"
        addButtonText="Add License"
        emptyStateMessage="No licenses added yet. Add your required permits and certifications."
        renderForm={(entity, disabled) => (
          <LicenseForm
            entity={entity}
            disabled={disabled}
            onUpdate={licensesManager.updateEntity}
            onDelete={licensesManager.deleteEntity}
          />
        )}
        disabled={baseDisabled}
        entityType="licenses"
      />

      {/* Suppliers Section */}
      <EntitySection
        title="Suppliers"
        description="Manage your supplier relationships and contact information. Data is stored in the `suppliers` table."
        entities={suppliersManager.entities}
        isLoading={suppliersManager.isLoading}
        canAddEntity={suppliersManager.canAddEntity}
        canDeleteEntity={suppliersManager.canDeleteEntity}
        addEntity={suppliersManager.addEntity}
        deleteEntity={suppliersManager.deleteEntity}
        operationError={suppliersManager.operationError}
        clearOperationError={suppliersManager.clearOperationError}
        entityDisplayName="supplier"
        entityDisplayNamePlural="suppliers"
        addButtonText="Add Supplier"
        emptyStateMessage="No suppliers added yet. Add your key suppliers and service providers."
        renderForm={(entity, disabled) => (
          <SupplierForm
            entity={entity}
            disabled={disabled}
            onUpdate={suppliersManager.updateEntity}
            onDelete={suppliersManager.deleteEntity}
          />
        )}
        disabled={baseDisabled}
        entityType="suppliers"
      />

      {/* Contracts Section */}
      <EntitySection
        title="Farm Contracts"
        description="Track your farming contracts and agreements. Data is stored in the `contracts` table."
        entities={contractsManager.entities}
        isLoading={contractsManager.isLoading}
        canAddEntity={contractsManager.canAddEntity}
        canDeleteEntity={contractsManager.canDeleteEntity}
        addEntity={contractsManager.addEntity}
        deleteEntity={contractsManager.deleteEntity}
        operationError={contractsManager.operationError}
        clearOperationError={contractsManager.clearOperationError}
        entityDisplayName="contract"
        entityDisplayNamePlural="contracts"
        addButtonText="Add Contract"
        emptyStateMessage="No contracts added yet. Add your service agreements and contracts."
        renderForm={(entity, disabled) => (
          <ContractForm
            entity={entity}
            disabled={disabled}
            onUpdate={contractsManager.updateEntity}
            onDelete={contractsManager.deleteEntity}
          />
        )}
        disabled={baseDisabled}
        entityType="contracts"
      />

      {/* Chemical Usage Section */}
      <EntitySection
        title="Chemical Usage Registry"
        description="Maintain records of chemical applications for compliance and safety. Data is stored in the `chemical_usage` table."
        entities={chemicalUsageManager.entities}
        isLoading={chemicalUsageManager.isLoading}
        canAddEntity={chemicalUsageManager.canAddEntity}
        canDeleteEntity={chemicalUsageManager.canDeleteEntity}
        addEntity={chemicalUsageManager.addEntity}
        deleteEntity={chemicalUsageManager.deleteEntity}
        operationError={chemicalUsageManager.operationError}
        clearOperationError={chemicalUsageManager.clearOperationError}
        entityDisplayName="chemical usage record"
        entityDisplayNamePlural="chemical usage records"
        addButtonText="Add Chemical Record"
        emptyStateMessage="No chemical usage records added yet. Add records for compliance tracking."
        renderForm={(entity, disabled) => (
          <ChemicalUsageForm
            entity={entity}
            disabled={disabled}
            onUpdate={chemicalUsageManager.updateEntity}
            onDelete={chemicalUsageManager.deleteEntity}
          />
        )}
        disabled={baseDisabled}
        entityType="chemical_usage"
      />
    </div>
  );
};