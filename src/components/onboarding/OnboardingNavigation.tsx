
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";

interface WizardNavigationProps {
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onBack: () => void;
  canGoNext: boolean;
  canGoBack: boolean;
  nextLabel?: string;
  backLabel?: string;
}

export const OnboardingNavigation = ({
  currentStep,
  totalSteps,
  onNext,
  onBack,
  canGoNext,
  canGoBack,
  nextLabel = "Continue",
  backLabel = "Back"
}: WizardNavigationProps) => {
  const isLastStep = currentStep === totalSteps;

  return (
    <div className="flex justify-between items-center mt-8">
      <Button
        variant="outline"
        onClick={onBack}
        disabled={!canGoBack}
        className="flex items-center space-x-2"
      >
        <ArrowLeft className="w-4 h-4" />
        <span>{backLabel}</span>
      </Button>

      <div className="text-sm text-muted-foreground">
        Step {currentStep} of {totalSteps}
      </div>

      <Button
        onClick={onNext}
        disabled={!canGoNext}
        className="flex items-center space-x-2"
      >
        <span>{isLastStep ? "Complete Setup" : nextLabel}</span>
        <ArrowRight className="w-4 h-4" />
      </Button>
    </div>
  );
};
