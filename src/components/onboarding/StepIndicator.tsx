import { Check } from "lucide-react";
import { cn } from "@/utils/utils";

interface Step {
  id: number;
  title: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
}

/**
 * Displays a horizontal list of steps with the current step highlighted.
 * Features modern design with smooth animations and rounded aesthetics.
 */
export const StepIndicator = ({ steps, currentStep }: StepIndicatorProps) => {
  return (
    <div className="flex items-center justify-between mb-12 px-4">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center flex-1 relative">
          <div className="flex flex-col items-center z-10">
            {/* Step Circle */}
            <div
              className={cn(
                "w-12 h-12 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-500 ease-out shadow-lg",
                step.id < currentStep
                  ? "bg-gradient-to-br from-green-500 to-green-600 text-white scale-110 shadow-green-500/30"
                  : step.id === currentStep
                    ? "bg-gradient-to-br from-primary to-primary/80 text-primary-foreground scale-125 shadow-primary/40 ring-4 ring-primary/20 animate-pulse"
                    : "bg-gradient-to-br from-gray-100 to-gray-200 text-gray-500 hover:scale-105 shadow-gray-200/50"
              )}
            >
              {step.id < currentStep ? (
                <Check className="w-6 h-6 animate-in fade-in-50 zoom-in-50 duration-300" />
              ) : (
                <span className="transition-all duration-300">
                  {step.id}
                </span>
              )}
            </div>

            {/* Step Title */}
            <span
              className={cn(
                "mt-3 text-sm text-center max-w-28 font-medium transition-all duration-300",
                step.id < currentStep
                  ? "text-green-700 font-semibold"
                  : step.id === currentStep
                    ? "text-primary font-semibold"
                    : "text-gray-500"
              )}
            >
              {step.title}
            </span>
          </div>

          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div className="flex-1 h-1 mx-6 relative overflow-hidden rounded-full bg-gray-200">
              <div
                className={cn(
                  "absolute inset-0 rounded-full transition-all duration-700 ease-out",
                  step.id < currentStep
                    ? "bg-gradient-to-r from-green-500 to-green-600 w-full shadow-sm"
                    : "bg-gray-200 w-0"
                )}
              />
              {/* Animated progress bar for current step */}
              {step.id === currentStep - 1 && (
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-green-500 to-green-600 animate-in slide-in-from-left-full duration-1000 ease-out" />
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};
