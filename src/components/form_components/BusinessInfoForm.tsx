import React from 'react';
import { AutoSaveFormField } from './FormField';
import { FormSection } from './FormSection';
import { BusinessRegistrationFormData, BUSINESS_STRUCTURES } from '@/types/form-components';
import { validators, formatters } from '@/utils/validation-patterns';

interface BusinessInfoFormProps {
  businessRegistrationData: BusinessRegistrationFormData;
  disabled: boolean;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
  updateFn: (tableName: string, id: string, data: Record<string, unknown>) => Promise<void>;
}

export const BusinessInfoForm: React.FC<BusinessInfoFormProps> = ({
  businessRegistrationData,
  disabled,
  isLoading,
  error,
  onRetry,
  updateFn,
}) => {
  return (
    <FormSection
      title="Business Information"
      description="Enter the core details of your business entity. This data is stored in `business_registration`."
      disabled={disabled}
      isLoading={isLoading}
      error={error}
      onRetry={onRetry}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Full Business Name (Legal Name)"
          name="full_business_name"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.full_business_name}
          placeholder="e.g., John Smith Pty Ltd"
          maxLength={200}
          required
          validationRules={[
            { required: true, message: 'Business name is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Trading Name (if different)"
          name="trading_name"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.trading_name}
          placeholder="e.g., JS Farms"
          maxLength={200}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="ABN (Australian Business Number)"
          name="abn"
          type="text"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.abn}
          placeholder="e.g., **************"
          maxLength={14} // Formatted with spaces
          formatter={formatters.abn}
          validationRules={[
            { custom: validators.abn, message: 'Please enter a valid ABN' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="ACN (Australian Company Number)"
          name="acn"
          type="text"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.acn}
          placeholder="e.g., ***********"
          maxLength={11} // Formatted with spaces
          formatter={(value: string) => {
            const digits = value.replace(/\D/g, '');
            if (digits.length === 0) return '';
            let formatted = '';
            if (digits.length > 0) formatted += digits.substring(0, 3);
            if (digits.length > 3) formatted += ' ' + digits.substring(3, 6);
            if (digits.length > 6) formatted += ' ' + digits.substring(6, 9);
            return formatted.trim();
          }}
          validationRules={[
            { 
              custom: (value: string) => {
                if (!value?.trim()) return null;
                const digits = value.replace(/\D/g, '');
                if (digits.length !== 9) return 'ACN must be 9 digits';
                return null;
              }, 
              message: 'Please enter a valid ACN' 
            }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Business Structure"
          name="business_structure"
          type="select"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.business_structure}
          options={BUSINESS_STRUCTURES}
          required
          validationRules={[
            { required: true, message: 'Business structure is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Phone Number"
          name="primary_business_phone"
          type="tel"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.primary_business_phone}
          placeholder="e.g., +61 2 1234 5678"
          formatter={formatters.phone}
          validationRules={[
            { custom: validators.phone, message: 'Please enter a valid Australian phone number' }
          ]}
          disabled={disabled}
        />
      </div>

      <div className="pt-4">
        <AutoSaveFormField
          label="GST Registered"
          name="is_gst_registered"
          type="select"
          entityId={businessRegistrationData.id}
          tableName="business_registration"
          updateFn={updateFn}
          value={businessRegistrationData.is_gst_registered ? 'true' : 'false'}
          options={[
            { value: 'true', label: 'Yes, GST Registered' },
            { value: 'false', label: 'No, Not GST Registered' }
          ]}
          description="Select if your business is registered for GST"
          disabled={disabled}
        />
      </div>
    </FormSection>
  );
};