import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle } from 'lucide-react';
import { cn } from '@/utils/utils';

/**
 * Props for the FormSection component
 */
export interface FormSectionProps {
    /** Section title */
    title: string;
    /** Section description */
    description: string;
    /** Section content */
    children: React.ReactNode;
    /** Whether the section is in a loading state */
    isLoading?: boolean;
    /** Error message to display */
    error?: string | null;
    /** Function to retry on error */
    onRetry?: () => void;
    /** Whether the entire section is disabled */
    disabled?: boolean;
    /** Additional CSS classes for the card */
    className?: string;
    /** Additional content for the header (e.g., action buttons) */
    headerActions?: React.ReactNode;
    /** Whether to show a border variant */
    variant?: 'default' | 'outlined' | 'elevated';
    /** Size variant */
    size?: 'default' | 'sm' | 'lg';
}

/**
 * Standardized card-based section wrapper for form content
 * Provides consistent styling, loading states, and error handling
 */
export const FormSection: React.FC<FormSectionProps> = ({
    title,
    description,
    children,
    isLoading = false,
    error = null,
    onRetry,
    disabled = false,
    className,
    headerActions,
    variant = 'default',
    size = 'default',
}) => {
    // Define variant styles
    const variantStyles = {
        default: '',
        outlined: 'border-2',
        elevated: 'shadow-md',
    };

    // Define size styles
    const sizeStyles = {
        default: '',
        sm: '[&>*]:space-y-3',
        lg: '[&>*]:space-y-8',
    };

    const cardClasses = cn(
        variantStyles[variant],
        sizeStyles[size],
        disabled && 'opacity-60',
        className
    );

    // If there's an error, show error state
    if (error) {
        return (
            <Card className={cardClasses}>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="space-y-1">
                            <CardTitle className="flex items-center space-x-2">
                                <AlertCircle className="h-5 w-5 text-destructive" aria-hidden="true" />
                                <span>{title}</span>
                            </CardTitle>
                            <CardDescription>{description}</CardDescription>
                        </div>
                        {headerActions}
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="rounded-md border border-destructive/20 bg-destructive/5 p-4" role="alert">
                            <p className="text-sm text-destructive">{error}</p>
                        </div>
                        {onRetry && (
                            <div className="flex justify-start">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={onRetry}
                                    disabled={isLoading}
                                >
                                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    Retry
                                </Button>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        );
    }

    // If loading, show loading state
    if (isLoading) {
        return (
            <Card className={cardClasses}>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="space-y-1">
                            <CardTitle>{title}</CardTitle>
                            <CardDescription>{description}</CardDescription>
                        </div>
                        {headerActions}
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center space-x-2 py-8" role="status" aria-live="polite">
                        <Loader2 className="h-6 w-6 animate-spin" aria-hidden="true" />
                        <p className="text-muted-foreground">
                            <span className="sr-only">Loading {title}: </span>
                            Loading...
                        </p>
                    </div>
                </CardContent>
            </Card>
        );
    }

    // Normal state
    return (
        <Card className={cardClasses}>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div className="space-y-1">
                        <CardTitle>{title}</CardTitle>
                        <CardDescription>{description}</CardDescription>
                    </div>
                    {headerActions}
                </div>
            </CardHeader>
            <CardContent>
                <fieldset disabled={disabled} className="space-y-4">
                    {children}
                </fieldset>
            </CardContent>
        </Card>
    );
};

/**
 * Props for FormSubSection component
 */
export interface FormSubSectionProps {
    /** Subsection title */
    title?: string;
    /** Subsection description */
    description?: string;
    /** Subsection content */
    children: React.ReactNode;
    /** Whether the subsection is collapsible */
    collapsible?: boolean;
    /** Whether the subsection is initially collapsed */
    defaultCollapsed?: boolean;
    /** Additional CSS classes */
    className?: string;
}

/**
 * Lightweight subsection component for organizing content within FormSection
 */
export const FormSubSection: React.FC<FormSubSectionProps> = ({
    title,
    description,
    children,
    collapsible = false,
    defaultCollapsed = false,
    className,
}) => {
    const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

    if (!title && !description) {
        return <div className={cn('space-y-4', className)}>{children}</div>;
    }

    return (
        <div className={cn('space-y-3', className)}>
            {(title || description) && (
                <div
                    className={cn(
                        'space-y-1',
                        collapsible && 'cursor-pointer select-none'
                    )}
                    onClick={collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}
                >
                    {title && (
                        <h4 className="text-sm font-medium leading-none">
                            {title}
                            {collapsible && (
                                <span className="ml-2 text-xs text-muted-foreground">
                                    {isCollapsed ? '▼' : '▲'}
                                </span>
                            )}
                        </h4>
                    )}
                    {description && (
                        <p className="text-xs text-muted-foreground">{description}</p>
                    )}
                </div>
            )}

            {(!collapsible || !isCollapsed) && (
                <div className="space-y-4">
                    {children}
                </div>
            )}
        </div>
    );
}; 