import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { LicenseFormData, LICENSE_TYPES, EntityFormComponentProps } from '@/types/form-components';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';

export const LicenseForm: React.FC<EntityFormComponentProps<LicenseFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { toast } = useToast();
  const { uploadAndFinalizeDocument } = useOnboarding();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Create wrapper for updateFn to match expected signature
  const updateWrapper = async (tableName: string, id: string, data: Record<string, unknown>) => {
    await onUpdate(id, data as Partial<LicenseFormData>);
  };

  // Handle file upload for license documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'licenses', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "License document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    // Implementation would depend on your document management system
    toast({
      title: "Document Removed",
      description: "License document has been removed",
      variant: "default",
    });
  }, [toast]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="License Type"
          name="license_type"
          type="select"
          entityId={entity.id}
          tableName="licenses"
          updateFn={updateWrapper}
          value={entity.license_type}
          options={LICENSE_TYPES}
          required
          validationRules={[
            { required: true, message: 'License type is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="License Number"
          name="license_number"
          entityId={entity.id}
          tableName="licenses"
          updateFn={updateWrapper}
          value={entity.license_number}
          placeholder="e.g., CHM-12345"
          maxLength={100}
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Issuing Authority"
          name="issuing_authority"
          entityId={entity.id}
          tableName="licenses"
          updateFn={updateWrapper}
          value={entity.issuing_authority}
          placeholder="e.g., NSW Department of Primary Industries"
          maxLength={200}
          disabled={disabled}
        />


      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Issue Date"
          name="issue_date"
          type="date"
          entityId={entity.id}
          tableName="licenses"
          updateFn={updateWrapper}
          value={entity.issue_date}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Expiry Date"
          name="expiry_date"
          type="date"
          entityId={entity.id}
          tableName="licenses"
          updateFn={updateWrapper}
          value={entity.expiry_date}
          validationRules={[
            {
              custom: (value: string) => {
                if (!value || !entity.issue_date) return null;
                const issueDate = new Date(entity.issue_date);
                const expiryDate = new Date(value);
                if (expiryDate <= issueDate) {
                  return 'Expiry date must be after issue date';
                }
                return null;
              },
              message: 'Invalid expiry date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="License Document"
          name="license_document"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload a copy of this license/certification (PDF, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload license document"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};