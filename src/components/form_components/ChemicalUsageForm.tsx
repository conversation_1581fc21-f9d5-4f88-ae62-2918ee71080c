import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { ChemicalUsageFormData, EntityFormComponentProps } from '@/types/form-components';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';

const APPLICATION_METHODS = [
  { value: 'Spray', label: 'Spray Application' },
  { value: 'Granular', label: 'Granular Application' },
  { value: 'Injection', label: 'Soil Injection' },
  { value: 'Dust', label: 'Dust Application' },
  { value: 'Fumigation', label: 'Fumigation' },
  { value: 'Bait', label: 'Bait Application' },
  { value: 'Dip', label: 'Dip Treatment' },
  { value: 'Other', label: 'Other Method' },
];


export const ChemicalUsageForm: React.FC<EntityFormComponentProps<ChemicalUsageFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<ChemicalUsageFormData>(onUpdate);
  const { uploadAndFinalizeDocument } = useOnboarding();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Handle file upload for chemical usage documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'chemical_usage', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Chemical usage document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    // Implementation would depend on your document management system
    toast({
      title: "Document Removed",
      description: "Chemical usage document has been removed",
      variant: "default",
    });
  }, [toast]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Product Name"
          name="product_name"
          entityId={entity.id}
          tableName="chemical_usage"
          updateFn={updateWrapper}
          value={entity.product_name}
          placeholder="e.g., Roundup, Glyphosate 360"
          maxLength={200}
          required
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Manufacturer"
          name="manufacturer"
          entityId={entity.id}
          tableName="chemical_usage"
          updateFn={updateWrapper}
          value={entity.manufacturer}
          placeholder="e.g., Bayer, Dow AgroSciences"
          maxLength={200}
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Application Method"
          name="application_method"
          type="select"
          entityId={entity.id}
          tableName="chemical_usage"
          updateFn={updateWrapper}
          value={entity.application_method}
          options={APPLICATION_METHODS}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Usage Purpose"
          name="usage_purpose"
          entityId={entity.id}
          tableName="chemical_usage"
          updateFn={updateWrapper}
          value={entity.usage_purpose}
          placeholder="e.g., Pesticide, Herbicide, Fertilizer"
          maxLength={200}
          required
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Application Rate"
          name="application_rate"
          entityId={entity.id}
          tableName="chemical_usage"
          updateFn={updateWrapper}
          value={entity.application_rate}
          placeholder="e.g., 1L per 100L water, 5kg per hectare"
          maxLength={100}
          description="Include units and concentration"
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Withholding Period (Days)"
          name="withholding_period_days"
          type="number"
          entityId={entity.id}
          tableName="chemical_usage"
          updateFn={updateWrapper}
          value={entity.withholding_period_days}
          placeholder="0"
          min={0}
          description="Days between application and safe harvest/consumption"
          disabled={disabled}
        />
      </div>

      <AutoSaveFormField
        label="Last Application Date"
        name="last_application_date"
        type="date"
        entityId={entity.id}
        tableName="chemical_usage"
        updateFn={updateWrapper}
        value={entity.last_application_date}
        validationRules={[
          {
            custom: (value: string) => {
              if (!value) return null;
              const applicationDate = new Date(value);
              const today = new Date();
              if (applicationDate > today) {
                return 'Application date cannot be in the future';
              }
              return null;
            },
            message: 'Invalid application date'
          }
        ]}
        disabled={disabled}
      />

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="Safety Data Sheet / Documentation"
          name="chemical_document"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload safety data sheet (SDS), application record, or chemical label (PDF, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload chemical documentation"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};