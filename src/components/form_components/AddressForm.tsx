import React from 'react';
import { AutoSaveFormField } from './FormField';
import { AddressFormData, ADDRESS_TYPES, EntityFormComponentProps } from '@/types/form-components';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';

const AUSTRALIAN_STATES = [
  { value: 'NSW', label: 'New South Wales' },
  { value: 'VIC', label: 'Victoria' },
  { value: 'QLD', label: 'Queensland' },
  { value: 'WA', label: 'Western Australia' },
  { value: 'SA', label: 'South Australia' },
  { value: 'TAS', label: 'Tasmania' },
  { value: 'ACT', label: 'Australian Capital Territory' },
  { value: 'NT', label: 'Northern Territory' },
];

export const AddressForm: React.FC<EntityFormComponentProps<AddressFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<AddressFormData>(onUpdate);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Address Type"
          name="address_type"
          type="select"
          entityId={entity.id}
          tableName="addresses"
          updateFn={updateWrapper}
          value={entity.address_type}
          options={ADDRESS_TYPES}
          required
          validationRules={[
            { required: true, message: 'Address type is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Country"
          name="country"
          entityId={entity.id}
          tableName="addresses"
          updateFn={updateWrapper}
          value={entity.country || 'Australia'}
          placeholder="Australia"
          maxLength={50}
          disabled={disabled}
        />
      </div>

      <AutoSaveFormField
        label="Full Address"
        name="full_address_text"
        type="textarea"
        entityId={entity.id}
        tableName="addresses"
        updateFn={updateWrapper}
        value={entity.full_address_text}
        placeholder="e.g., 123 Farm Road, Rural Valley NSW 2000"
        maxLength={500}
        required
        validationRules={[
          { required: true, message: 'Full address is required' }
        ]}
        description="Enter the complete address as it would appear on mail"
        disabled={disabled}
      />

      <AutoSaveFormField
        label="Street"
        name="street"
        entityId={entity.id}
        tableName="addresses"
        updateFn={updateWrapper}
        value={entity.street}
        placeholder="e.g., 123 Farm Road"
        maxLength={200}
        disabled={disabled}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Locality/Suburb"
          name="locality"
          entityId={entity.id}
          tableName="addresses"
          updateFn={updateWrapper}
          value={entity.locality}
          placeholder="e.g., Rural Valley"
          maxLength={100}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="State"
          name="state"
          type="select"
          entityId={entity.id}
          tableName="addresses"
          updateFn={updateWrapper}
          value={entity.state}
          options={AUSTRALIAN_STATES}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Postcode"
          name="postcode"
          entityId={entity.id}
          tableName="addresses"
          updateFn={updateWrapper}
          value={entity.postcode}
          placeholder="e.g., 2000"
          maxLength={10}
          validationRules={[
            {
              custom: (value: string) => {
                if (!value?.trim()) return null;
                if (!/^\d{4}$/.test(value)) return 'Postcode must be 4 digits';
                return null;
              },
              message: 'Please enter a valid Australian postcode'
            }
          ]}
          disabled={disabled}
        />
      </div>
    </div>
  );
};