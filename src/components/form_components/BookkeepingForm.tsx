import React from 'react';
import { FormField } from './FormField';
import { FormSection } from './FormSection';
import { BookkeepingFormData, BAS_LODGEMENT_FREQUENCIES } from '@/types/form-components';
import { UseFormManagementReturn } from '@/hooks/use-form-management';

const ACCOUNTING_METHODS = [
  { value: 'Cash', label: 'Cash Accounting' },
  { value: 'Accrual', label: 'Accrual Accounting' },
];

const MONTHS = [
  { value: '01', label: 'January' },
  { value: '02', label: 'February' },
  { value: '03', label: 'March' },
  { value: '04', label: 'April' },
  { value: '05', label: 'May' },
  { value: '06', label: 'June' },
  { value: '07', label: 'July' },
  { value: '08', label: 'August' },
  { value: '09', label: 'September' },
  { value: '10', label: 'October' },
  { value: '11', label: 'November' },
  { value: '12', label: 'December' },
];

interface BookkeepingFormProps {
  formManager: UseFormManagementReturn<BookkeepingFormData & Record<string, unknown>>;
  disabled: boolean;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
}

export const BookkeepingForm: React.FC<BookkeepingFormProps> = ({
  formManager,
  disabled,
  isLoading,
  error,
  onRetry,
}) => {
  const { formData, handleFieldChange, fieldErrors, isDirty, isSaving } = formManager;

  if (!formData) {
    return (
      <FormSection
        title="Bookkeeping & Accounting"
        description="Loading bookkeeping configuration..."
        disabled={true}
        isLoading={true}
      >
        <div>Loading...</div>
      </FormSection>
    );
  }

  return (
    <FormSection
      title="Bookkeeping & Accounting"
      description="Configure your bookkeeping and accounting preferences. This data is stored in the `bookkeeping` table."
      disabled={disabled}
      isLoading={isLoading}
      error={error}
      onRetry={onRetry}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Current Bookkeeping Software"
          name="current_software"
          value={formData.current_software || ''}
          onChange={(value) => handleFieldChange('current_software', value as string)}
          placeholder="e.g., Xero, MYOB, QuickBooks"
          maxLength={100}
          disabled={disabled || isSaving}
          error={fieldErrors.current_software}
        />

        <FormField
          label="Software Version"
          name="software_version"
          value={formData.software_version || ''}
          onChange={(value) => handleFieldChange('software_version', value as string)}
          placeholder="e.g., Xero Standard, MYOB Business"
          maxLength={100}
          disabled={disabled || isSaving}
          error={fieldErrors.software_version}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Bank Feeds Enabled"
          name="has_bank_feeds_enabled"
          type="select"
          value={formData.has_bank_feeds_enabled ? 'true' : 'false'}
          onChange={(value) => handleFieldChange('has_bank_feeds_enabled', value === 'true')}
          options={[
            { value: 'true', label: 'Yes, Bank Feeds Enabled' },
            { value: 'false', label: 'No, Manual Entry' }
          ]}
          description="Are your bank accounts connected to your accounting software?"
          disabled={disabled || isSaving}
          error={fieldErrors.has_bank_feeds_enabled}
        />

        <FormField
          label="Accountant Has Access"
          name="accountant_has_access"
          type="select"
          value={formData.accountant_has_access ? 'true' : 'false'}
          onChange={(value) => handleFieldChange('accountant_has_access', value === 'true')}
          options={[
            { value: 'true', label: 'Yes, Accountant Has Access' },
            { value: 'false', label: 'No Access Granted' }
          ]}
          disabled={disabled || isSaving}
          error={fieldErrors.accountant_has_access}
        />
      </div>

      <FormField
        label="BAS Lodgement Frequency"
        name="bas_lodgement_frequency"
        type="select"
        value={formData.bas_lodgement_frequency || ''}
        onChange={(value) => handleFieldChange('bas_lodgement_frequency', value as string)}
        options={BAS_LODGEMENT_FREQUENCIES}
        description="How often do you lodge your Business Activity Statement?"
        disabled={disabled || isSaving}
        error={fieldErrors.bas_lodgement_frequency}
      />

      <FormField
        label="Accountant Access Level"
        name="accountant_access_level"
        value={formData.accountant_access_level || ''}
        onChange={(value) => handleFieldChange('accountant_access_level', value as string)}
        placeholder="e.g., Read Only, Full Access, Admin"
        maxLength={100}
        description="What level of access does your accountant have?"
        disabled={disabled || isSaving}
        error={fieldErrors.accountant_access_level}
      />

      {/* Status indicator */}
      {isDirty && (
        <div className="text-xs text-muted-foreground mt-2">
          {isSaving ? 'Saving changes...' : 'Changes will be saved automatically'}
        </div>
      )}
    </FormSection>
  );
};