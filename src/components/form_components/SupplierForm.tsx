import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { SupplierFormData, EntityFormComponentProps } from '@/types/form-components';
import { validators, formatters } from '@/utils/validation-patterns';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';

const SUPPLIER_TYPES = [
  { value: 'Seeds & Plants', label: 'Seeds & Plants' },
  { value: 'Fertilizers', label: 'Fertilizers' },
  { value: 'Chemicals', label: 'Chemicals' },
  { value: 'Equipment', label: 'Equipment' },
  { value: 'Feed', label: 'Feed' },
  { value: 'Fuel', label: 'Fuel' },
  { value: 'Veterinary', label: 'Veterinary Services' },
  { value: 'Transport', label: 'Transport' },
  { value: 'Professional Services', label: 'Professional Services' },
  { value: 'Other', label: 'Other' },
];

export const SupplierForm: React.FC<EntityFormComponentProps<SupplierFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<SupplierFormData>(onUpdate);
  const { uploadAndFinalizeDocument } = useOnboarding();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Handle file upload for supplier documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'suppliers', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Supplier document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    // Implementation would depend on your document management system
    toast({
      title: "Document Removed",
      description: "Supplier document has been removed",
      variant: "default",
    });
  }, [toast]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Supplier Name"
          name="supplier_name"
          entityId={entity.id}
          tableName="suppliers"
          updateFn={updateWrapper}
          value={entity.supplier_name}
          placeholder="e.g., ABC Agricultural Supplies"
          maxLength={200}
          required
          validationRules={[
            { required: true, message: 'Supplier name is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Supplier Type"
          name="supplier_type"
          type="select"
          entityId={entity.id}
          tableName="suppliers"
          updateFn={updateWrapper}
          value={entity.supplier_type}
          options={SUPPLIER_TYPES}
          required
          validationRules={[
            { required: true, message: 'Supplier type is required' }
          ]}
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Contact Person"
          name="contact_person"
          entityId={entity.id}
          tableName="suppliers"
          updateFn={updateWrapper}
          value={entity.contact_person}
          placeholder="e.g., John Smith"
          maxLength={100}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Phone"
          name="phone"
          type="tel"
          entityId={entity.id}
          tableName="suppliers"
          updateFn={updateWrapper}
          value={entity.phone}
          placeholder="e.g., +61 2 1234 5678"
          formatter={formatters.phone}
          validationRules={[
            { custom: validators.phone, message: 'Please enter a valid Australian phone number' }
          ]}
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Email"
          name="email"
          type="email"
          entityId={entity.id}
          tableName="suppliers"
          updateFn={updateWrapper}
          value={entity.email}
          placeholder="e.g., <EMAIL>"
          maxLength={100}
          validationRules={[
            { custom: validators.email, message: 'Please enter a valid email address' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Products/Services"
          name="products_services"
          entityId={entity.id}
          tableName="suppliers"
          updateFn={updateWrapper}
          value={entity.products_services}
          placeholder="e.g., Organic fertilizers, soil conditioners"
          maxLength={255}
          disabled={disabled}
        />
      </div>

      <AutoSaveFormField
        label="Address"
        name="address"
        type="textarea"
        entityId={entity.id}
        tableName="suppliers"
        updateFn={updateWrapper}
        value={entity.address}
        placeholder="Full supplier address"
        maxLength={500}
        disabled={disabled}
      />

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="Supplier Documentation"
          name="supplier_document"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload supplier agreement, catalog, or certification documents (PDF, Word, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload supplier document"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};