import React, { useCallback, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { cn } from '@/utils/utils';
import { useFieldManagement } from '@/hooks/use-form-management';
import { ValidationConfig } from '@/utils/form-validation';

/**
 * Option for select fields
 */
export interface SelectOption {
    value: string;
    label: string;
    disabled?: boolean;
}

/**
 * Props for the FormField component
 */
export interface FormFieldProps {
    /** Field label */
    label: string;
    /** Field name/identifier */
    name: string;
    /** Field type */
    type?: 'text' | 'email' | 'tel' | 'number' | 'date' | 'password' | 'textarea' | 'select' | 'checkbox';
    /** Current field value */
    value: string | number | boolean | string[] | null | undefined;
    /** Change handler */
    onChange: (value: string | number | boolean | string[] | null | undefined) => void;
    /** Blur handler (optional, for manual control) */
    onBlur?: () => void;
    /** Whether the field is required */
    required?: boolean;
    /** Placeholder text */
    placeholder?: string;
    /** Whether the field is disabled */
    disabled?: boolean;
    /** Maximum length for text inputs */
    maxLength?: number;
    /** Minimum value for number inputs */
    min?: number;
    /** Maximum value for number inputs */
    max?: number;
    /** Step value for number inputs */
    step?: number;
    /** Options for select fields */
    options?: SelectOption[];
    /** Custom error message */
    error?: string;
    /** Whether field is in a saving state */
    isSaving?: boolean;
    /** Additional CSS classes */
    className?: string;
    /** Additional description/help text */
    description?: string;
    /** Validation rules (for auto-save fields) */
    validationRules?: ValidationConfig[];
    /** Custom formatter function */
    formatter?: (value: string | number | boolean | string[] | null | undefined) => string | number | boolean | string[] | null | undefined;
    /** Whether to show unsaved changes indicator */
    showUnsavedIndicator?: boolean;
    /** Whether field has unsaved changes */
    hasUnsavedChanges?: boolean;
}

/**
 * Props for FormField with auto-save functionality
 */
export interface AutoSaveFormFieldProps extends Omit<FormFieldProps, 'onChange' | 'onBlur' | 'error' | 'isSaving' | 'hasUnsavedChanges'> {
    /** Entity ID for auto-save */
    entityId: string;
    /** Table name for auto-save */
    tableName: string;
    /** Update function for auto-save */
    updateFn: (tableName: string, id: string, data: Record<string, unknown>) => Promise<void>;
    /** Debounce delay for auto-save */
    debounceMs?: number;
    /** Field display name for logging */
    fieldDisplayName?: string;
}

/**
 * Reusable form field component with validation and error display
 */
export const FormField: React.FC<FormFieldProps> = ({
    label,
    name,
    type = 'text',
    value,
    onChange,
    onBlur,
    required = false,
    placeholder,
    disabled = false,
    maxLength,
    min,
    max,
    step,
    options = [],
    error,
    isSaving = false,
    className,
    description,
    showUnsavedIndicator = false,
    hasUnsavedChanges = false,
}) => {
    // Generate unique ID for accessibility
    const fieldId = useMemo(() => `field-${name}-${Math.random().toString(36).substr(2, 9)}`, [name]);
    const errorId = `${fieldId}-error`;
    const descriptionId = `${fieldId}-description`;

    // Handle input changes with proper type conversion
    const handleChange = useCallback((newValue: string | number | boolean | string[] | null | undefined) => {
        let processedValue = newValue;

        // Type-specific processing
        if (type === 'number') {
            if (newValue === '' || newValue === null) {
                processedValue = null;
            } else {
                const numericValue = parseFloat(String(newValue));
                processedValue = isNaN(numericValue) ? value : numericValue;
            }
        }

        onChange(processedValue);
    }, [onChange, type, value]);

    // Handle key down events
    const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && onBlur && type !== 'textarea') {
            e.preventDefault();
            onBlur();
        }
        // Escape key clears focus for better keyboard navigation
        if (e.key === 'Escape') {
            (e.currentTarget as HTMLElement).blur();
        }
    }, [onBlur, type]);

    // Render the appropriate input component
    const renderInput = () => {
        const baseProps = {
            id: fieldId,
            name,
            value: value ?? '',
            disabled,
            placeholder,
            onKeyDown: handleKeyDown,
            'aria-describedby': cn(
                error && errorId,
                description && descriptionId
            ).trim() || undefined,
            'aria-invalid': !!error,
            className: cn(
                error && 'border-destructive focus-visible:ring-destructive',
                className
            ),
        };

        switch (type) {
            case 'select':
                return (
                    <Select
                        value={String(value || '')}
                        onValueChange={handleChange}
                        disabled={disabled}
                    >
                        <SelectTrigger {...baseProps} value={String(value || '')}>
                            <SelectValue placeholder={placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                            {options.map(option => (
                                <SelectItem
                                    key={option.value}
                                    value={option.value}
                                    disabled={option.disabled}
                                >
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                );

            case 'textarea':
                return (
                    <Textarea
                        {...baseProps}
                        value={String(value || '')}
                        onChange={(e) => handleChange(e.target.value)}
                        onBlur={onBlur}
                        maxLength={maxLength}
                        rows={4}
                    />
                );

            case 'number':
                return (
                    <Input
                        {...baseProps}
                        type="number"
                        value={value === null || value === undefined ? '' : String(value)}
                        onChange={(e) => handleChange(e.target.value)}
                        onBlur={onBlur}
                        min={min}
                        max={max}
                        step={step}
                    />
                );

            case 'checkbox':
                return (
                    <div className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id={fieldId}
                            name={name}
                            checked={Boolean(value)}
                            onChange={(e) => handleChange(e.target.checked)}
                            onBlur={onBlur}
                            disabled={disabled}
                            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            aria-describedby={cn(
                                error && errorId,
                                description && descriptionId
                            ).trim() || undefined}
                            aria-invalid={!!error}
                        />
                        <Label htmlFor={fieldId} className="text-sm font-medium cursor-pointer">
                            {label}
                            {required && <span className="text-destructive ml-1">*</span>}
                        </Label>
                    </div>
                );

            default:
                return (
                    <Input
                        {...baseProps}
                        type={type}
                        value={String(value || '')}
                        onChange={(e) => handleChange(e.target.value)}
                        onBlur={onBlur}
                        maxLength={maxLength}
                    />
                );
        }
    };

    return (
        <div className="space-y-2">
            {type === 'checkbox' ? (
                // Special layout for checkbox
                <div className="flex items-center justify-between">
                    {renderInput()}
                    <div className="flex items-center space-x-2">
                        {showUnsavedIndicator && hasUnsavedChanges && (
                            <span className="text-xs text-amber-600">Unsaved</span>
                        )}
                        {isSaving && (
                            <div className="flex items-center text-xs text-muted-foreground">
                                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                Saving...
                            </div>
                        )}
                    </div>
                </div>
            ) : (
                // Standard layout for other input types
                <>
                    <div className="flex items-center justify-between">
                        <Label htmlFor={fieldId} className="text-sm font-medium">
                            {label}
                            {required && <span className="text-destructive ml-1">*</span>}
                        </Label>

                        {/* Status indicators */}
                        <div className="flex items-center space-x-2">
                            {showUnsavedIndicator && hasUnsavedChanges && (
                                <span className="text-xs text-amber-600">Unsaved</span>
                            )}
                            {isSaving && (
                                <div className="flex items-center text-xs text-muted-foreground">
                                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                    Saving...
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Input field */}
                    {renderInput()}
                </>
            )}

            {/* Description text */}
            {description && (
                <p id={descriptionId} className="text-xs text-muted-foreground">
                    {description}
                </p>
            )}

            {/* Error message */}
            {error && (
                <p id={errorId} className="text-sm text-destructive" role="alert">
                    {error}
                </p>
            )}
        </div>
    );
};

/**
 * FormField with built-in auto-save functionality
 */
export const AutoSaveFormField: React.FC<AutoSaveFormFieldProps> = ({
    entityId,
    tableName,
    name,
    value: initialValue,
    updateFn,
    debounceMs,
    validationRules,
    formatter,
    fieldDisplayName,
    disabled,
    showUnsavedIndicator = true,
    ...props
}) => {
    const {
        value,
        onChange,
        onBlur,
        error,
        isSaving,
        hasUnsavedChanges,
    } = useFieldManagement({
        entityId,
        tableName,
        fieldName: name,
        initialValue,
        updateFn,
        debounceMs,
        validationRules,
        formatter,
        disabled,
        fieldDisplayName,
    });

    return (
        <FormField
            {...props}
            name={name}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            error={error}
            isSaving={isSaving}
            disabled={disabled}
            showUnsavedIndicator={showUnsavedIndicator}
            hasUnsavedChanges={hasUnsavedChanges}
        />
    );
}; 