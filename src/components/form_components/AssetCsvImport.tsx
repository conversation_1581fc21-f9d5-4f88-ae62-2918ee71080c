import React, { useState, useCallback } from 'react';
import { FileUploadField } from './FileUploadField';
import { FormSection } from './FormSection';
import { Button } from '@/components/ui/button';
import { Download, Upload } from 'lucide-react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';

interface AssetCsvImportProps {
  stepId: string | null;
  disabled: boolean;
  onImportComplete: () => void;
}

export const AssetCsvImport: React.FC<AssetCsvImportProps> = ({
  stepId,
  disabled,
  onImportComplete,
}) => {
  const { invokeImportAssetsCSV } = useOnboarding();
  const { toast } = useToast();
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string>('');

  // Handle CSV file upload and import
  const handleCsvUpload = useCallback(async (file: File) => {
    if (!stepId) {
      setImportError('Step ID not available. Please try again.');
      return;
    }

    setIsImporting(true);
    setImportError('');

    try {
      const result = await invokeImportAssetsCSV(stepId, file);
      
      if (result.success) {
        toast({
          title: "Import Successful",
          description: `Successfully imported ${result.importedCount || 0} assets`,
          variant: "default",
        });
        onImportComplete();
      } else {
        const errorMessage = result.error || 'Import failed';
        setImportError(errorMessage);
        toast({
          title: "Import Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Import failed';
      setImportError(errorMessage);
      
      toast({
        title: "Import Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  }, [stepId, invokeImportAssetsCSV, toast, onImportComplete]);

  // Download CSV template
  const handleDownloadTemplate = useCallback(() => {
    const csvContent = [
      'asset_category,asset_type,make_or_provider,registration_or_policy_number,renewal_date',
      'Vehicle,Tractor,John Deere,JD001,2024-12-31',
      'Equipment,Harvester,Case IH,CH001,',
      'Insurance,Farm Property,CGU,POL123456,2024-06-30',
      'Vehicle,Truck,Ford,ABC123,2025-03-15',
      'Equipment,Irrigation Equipment,Valley,IRR001,',
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'asset_import_template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast({
      title: "Template Downloaded",
      description: "CSV template has been downloaded to your computer",
      variant: "default",
    });
  }, [toast]);

  return (
    <FormSection
      title="Bulk Asset Import"
      description="Import multiple assets from a CSV file to save time on data entry."
      disabled={disabled}
    >
      <div className="space-y-6">
        {/* Template Download */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start justify-between">
            <div>
              <h4 className="text-sm font-medium text-blue-900 mb-2">Step 1: Download Template</h4>
              <p className="text-xs text-blue-700 mb-3">
                Download our CSV template with the correct format and example data.
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadTemplate}
              className="ml-4"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>
        </div>

        {/* File Upload */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-md">
          <h4 className="text-sm font-medium text-green-900 mb-2">Step 2: Upload Your CSV</h4>
          <p className="text-xs text-green-700 mb-4">
            Fill in the template with your asset data and upload it here.
          </p>
          
          <FileUploadField
            label="Upload Asset CSV"
            name="asset_csv"
            onUpload={handleCsvUpload}
            disabled={disabled || isImporting}
            accept=".csv"
            validation={{
              allowedTypes: ['text/csv', 'application/vnd.ms-excel'],
              maxSize: 5 * 1024 * 1024, // 5MB
            }}
            isUploading={isImporting}
            error={importError}
            placeholder="Click or drag to upload your asset CSV file"
            description="CSV files only, maximum 5MB"
            showDropZone={true}
          />
        </div>

        {/* Import Guidelines */}
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Import Guidelines</h4>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>• Use the exact column headers from the template</li>
            <li>• Asset category must be: Vehicle, Equipment, or Insurance</li>
            <li>• Dates should be in YYYY-MM-DD format (e.g., 2024-12-31)</li>
            <li>• Leave cells empty rather than using "N/A" or "-"</li>
            <li>• Maximum 1000 assets per import</li>
            <li>• Duplicate registration numbers will be flagged</li>
          </ul>
        </div>
      </div>
    </FormSection>
  );
};