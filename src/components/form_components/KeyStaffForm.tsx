import React from 'react';
import { AutoSave<PERSON>orm<PERSON>ield } from './FormField';
import { KeyStaffFormData, EntityFormComponentProps } from '@/types/form-components';
import { validators, formatters } from '@/utils/validation-patterns';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';

export const KeyStaffForm: React.FC<EntityFormComponentProps<KeyStaffFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<KeyStaffFormData>(onUpdate);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <AutoSaveFormField
        label="Staff Name"
        name="staff_name"
        entityId={entity.id}
        tableName="key_staff"
        updateFn={updateWrapper}
        value={entity.staff_name}
        placeholder="e.g., <PERSON>"
        maxLength={200}
        required
        validationRules={[
          { required: true, message: 'Staff name is required' }
        ]}
        disabled={disabled}
      />

      <AutoSaveFormField
        label="Role or Title"
        name="role_or_title"
        entityId={entity.id}
        tableName="key_staff"
        updateFn={updateWrapper}
        value={entity.role_or_title}
        placeholder="e.g., Farm Manager, Head Groundskeeper"
        maxLength={100}
        disabled={disabled}
      />

      <AutoSaveFormField
        label="Contact Email"
        name="contact_email"
        type="email"
        entityId={entity.id}
        tableName="key_staff"
        updateFn={updateWrapper}
        value={entity.contact_email}
        placeholder="e.g., <EMAIL>"
        validationRules={[
          { custom: validators.email, message: 'Please enter a valid email address' }
        ]}
        disabled={disabled}
      />

      <AutoSaveFormField
        label="Contact Phone"
        name="contact_phone"
        type="tel"
        entityId={entity.id}
        tableName="key_staff"
        updateFn={updateWrapper}
        value={entity.contact_phone}
        placeholder="e.g., +61 2 1234 5678"
        formatter={formatters.phone}
        validationRules={[
          { custom: validators.phone, message: 'Please enter a valid Australian phone number' }
        ]}
        disabled={disabled}
      />
    </div>
  );
};