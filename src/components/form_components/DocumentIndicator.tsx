import React, { useState, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Download, 
  Eye, 
  AlertCircle, 
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/utils/logger';
import { Document } from '@/types/onboarding';

interface DocumentIndicatorProps {
  entityType: string;
  entityId: string;
  entityName?: string;
  showDetails?: boolean;
  className?: string;
}

/**
 * DocumentIndicator displays document count and management capabilities for entities
 * Used within entity lists to show document status and provide access to document management
 */
export const DocumentIndicator: React.FC<DocumentIndicatorProps> = ({
  entityType,
  entityId,
  entityName,
  showDetails = false,
  className = ''
}) => {
  const { getSignedDocumentUrl, sessionData } = useOnboarding();
  const { toast } = useToast();
  
  const [isLoadingDocument, setIsLoadingDocument] = useState<string | null>(null);

  // Filter documents from session data for this entity
  const documents = useMemo(() => {
    if (!sessionData?.documents) return [];
    
    return sessionData.documents.filter(doc => 
      doc.related_to_entity === entityType && doc.related_to_id === entityId
    );
  }, [sessionData?.documents, entityType, entityId]);

  // Handle document download/view
  const handleDocumentAccess = useCallback(async (document: Document, action: 'view' | 'download') => {
    if (!document.storage_bucket_path) return;

    setIsLoadingDocument(document.id);
    
    try {
      const signedUrl = await getSignedDocumentUrl(document.storage_bucket_path);
      
      if (signedUrl) {
        if (action === 'view') {
          window.open(signedUrl, '_blank');
        } else {
          // Create download link
          const link = document.createElement('a');
          link.href = signedUrl;
          link.download = document.document_name;
          link.click();
        }
        
        toast({
          title: "Success",
          description: `Document ${action === 'view' ? 'opened' : 'downloaded'} successfully`,
          variant: "default",
        });
      } else {
        throw new Error('Failed to get document URL');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to access document';
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      logger.error('Document access failed', {
        error: errorMessage,
        documentId: document.id,
        action,
        entityType,
        entityId
      });
    } finally {
      setIsLoadingDocument(null);
    }
  }, [getSignedDocumentUrl, toast, entityType, entityId]);

  const documentCount = documents.length;
  const hasDocuments = documentCount > 0;

  if (!showDetails) {
    // Simple indicator for entity lists
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {hasDocuments ? (
          <>
            <CheckCircle className="h-4 w-4 text-green-600" />
            <Badge variant="secondary" className="text-xs">
              {documentCount} doc{documentCount !== 1 ? 's' : ''}
            </Badge>
          </>
        ) : (
          <>
            <AlertCircle className="h-4 w-4 text-amber-500" />
            <Badge variant="outline" className="text-xs">
              No docs
            </Badge>
          </>
        )}
      </div>
    );
  }

  // Detailed view for expanded entity management
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">
            Documents ({documentCount})
          </span>
        </div>
        {hasDocuments && (
          <Badge variant={hasDocuments ? "default" : "secondary"}>
            {hasDocuments ? "Complete" : "Missing"}
          </Badge>
        )}
      </div>

      {hasDocuments && (
        <div className="space-y-1">
          {documents.map((document) => (
            <div key={document.id} className="flex items-center justify-between p-2 bg-muted/50 rounded">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{document.document_name}</p>
                <p className="text-xs text-muted-foreground">
                  {document.document_category} • {document.file_size_bytes ? 
                    `${(document.file_size_bytes / 1024 / 1024).toFixed(1)} MB` : 
                    'Unknown size'}
                </p>
              </div>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDocumentAccess(document, 'view')}
                  disabled={isLoadingDocument === document.id}
                  className="h-6 w-6 p-0"
                >
                  {isLoadingDocument === document.id ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Eye className="h-3 w-3" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDocumentAccess(document, 'download')}
                  disabled={isLoadingDocument === document.id}
                  className="h-6 w-6 p-0"
                >
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {!hasDocuments && (
        <p className="text-xs text-muted-foreground">
          No documents uploaded for this {entityName || 'item'} yet.
        </p>
      )}
    </div>
  );
};