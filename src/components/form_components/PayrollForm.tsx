import React from 'react';
import { FormField } from './FormField';
import { FormSection } from './FormSection';
import { PayrollFormData } from '@/types/form-components';
import { UseFormManagementReturn } from '@/hooks/use-form-management';

const PAY_FREQUENCIES = [
  { value: 'Weekly', label: 'Weekly' },
  { value: 'Fortnightly', label: 'Fortnightly' },
  { value: 'Monthly', label: 'Monthly' },
  { value: 'Casual', label: 'Casual/As needed' },
];

interface PayrollFormProps {
  formManager: UseFormManagementReturn<PayrollFormData & Record<string, unknown>>;
  disabled: boolean;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
}

export const PayrollForm: React.FC<PayrollFormProps> = ({
  formManager,
  disabled,
  isLoading,
  error,
  onRetry,
}) => {
  const { formData, handleFieldChange, fieldErrors, isDirty, isSaving } = formManager;

  if (!formData) {
    return (
      <FormSection
        title="Payroll & Employment"
        description="Loading payroll configuration..."
        disabled={true}
        isLoading={true}
      >
        <div>Loading...</div>
      </FormSection>
    );
  }

  const isPayrollNeeded = formData.is_payroll_processing_needed;

  return (
    <FormSection
      title="Payroll & Employment"
      description="Configure your payroll processing requirements. This data is stored in the `payroll` table."
      disabled={disabled}
      isLoading={isLoading}
      error={error}
      onRetry={onRetry}
    >
      <FormField
        label="Do you need payroll processing?"
        name="is_payroll_processing_needed"
        type="select"
        value={formData.is_payroll_processing_needed ? 'true' : 'false'}
        onChange={(value) => handleFieldChange('is_payroll_processing_needed', value === 'true')}
        options={[
          { value: 'true', label: 'Yes, I need payroll processing' },
          { value: 'false', label: 'No, I handle payroll myself' }
        ]}
        required
        disabled={disabled || isSaving}
        error={fieldErrors.is_payroll_processing_needed}
      />

      {isPayrollNeeded && (
        <div className="space-y-4 pt-4 border-t">
          <h4 className="text-sm font-medium">Payroll Details</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Number of Employees"
              name="employee_count"
              type="number"
              value={formData.employee_count?.toString() || ''}
              onChange={(value) => handleFieldChange('employee_count', typeof value === 'string' && value ? parseInt(value, 10) : null)}
              placeholder="e.g., 5"
              min={1}
              max={1000}
              required
              disabled={disabled || isSaving}
              error={fieldErrors.employee_count}
            />

            <FormField
              label="Payroll Frequency"
              name="payroll_frequency"
              type="select"
              value={formData.payroll_frequency || ''}
              onChange={(value) => handleFieldChange('payroll_frequency', value as string)}
              options={PAY_FREQUENCIES}
              required
              disabled={disabled || isSaving}
              error={fieldErrors.payroll_frequency}
            />
          </div>

          <FormField
            label="Current Payroll Software"
            name="current_payroll_software"
            value={formData.current_payroll_software || ''}
            onChange={(value) => handleFieldChange('current_payroll_software', value as string)}
            placeholder="e.g., MYOB, Xero Payroll, Manual spreadsheets"
            maxLength={100}
            description="What system do you currently use for payroll?"
            disabled={disabled || isSaving}
            error={fieldErrors.current_payroll_software}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Superannuation Fund"
              name="superannuation_fund"
              value={formData.superannuation_fund || ''}
              onChange={(value) => handleFieldChange('superannuation_fund', value as string)}
              placeholder="e.g., Australian Super, REST Industry Super"
              maxLength={150}
              description="Primary super fund for employees"
              disabled={disabled || isSaving}
              error={fieldErrors.superannuation_fund}
            />

            <FormField
              label="Workers' Compensation Policy"
              name="workers_compensation_policy"
              value={formData.workers_compensation_policy || ''}
              onChange={(value) => handleFieldChange('workers_compensation_policy', value as string)}
              placeholder="e.g., CGU, Allianz, WorkCover"
              maxLength={150}
              description="Your workers' compensation insurance provider"
              disabled={disabled || isSaving}
              error={fieldErrors.workers_compensation_policy}
            />
          </div>
        </div>
      )}

      {!isPayrollNeeded && (
        <div className="pt-4 border-t">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              No problem! You can always add payroll processing services later if your needs change.
            </p>
          </div>
        </div>
      )}

      {/* Status indicator */}
      {isDirty && (
        <div className="text-xs text-muted-foreground mt-2">
          {isSaving ? 'Saving changes...' : 'Changes will be saved automatically'}
        </div>
      )}
    </FormSection>
  );
};