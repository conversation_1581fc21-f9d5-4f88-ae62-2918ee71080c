import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { ActivityFormData, ACTIVITY_TYPES, EntityFormComponentProps } from '@/types/form-components';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';
import { validateEntityForUpload } from '@/utils/uuid-validation';

const LIVESTOCK_TYPES = [
  { value: 'Cattle', label: 'Cattle' },
  { value: 'Sheep', label: 'Sheep' },
  { value: 'Goats', label: 'Goats' },
  { value: 'Pigs', label: 'Pigs' },
  { value: 'Poultry', label: 'Poultry' },
  { value: 'Other', label: 'Other' },
];

const SCALE_OPTIONS = [
  { value: 'Small', label: 'Small Scale (Hobby/Small Family Farm)' },
  { value: 'Medium', label: 'Medium Scale (Commercial Family Farm)' },
  { value: 'Large', label: 'Large Scale (Corporate/Industrial)' },
];

export const ActivityForm: React.FC<EntityFormComponentProps<ActivityFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<ActivityFormData>(onUpdate);
  const { uploadAndFinalizeDocument } = useOnboarding();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  const showCropFields = entity.activity_type === 'Cropping' || entity.activity_type === 'Mixed Farming';
  const showLivestockFields = entity.activity_type === 'Livestock' || entity.activity_type === 'Mixed Farming';

  // Handle file upload for activity documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) {
      setUploadError('Activity must be saved before uploading documents');
      return;
    }

    // Validate entity ID format before upload
    try {
      validateEntityForUpload(entity.id, 'activity');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid activity ID format';
      setUploadError(errorMessage);
      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'activities', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Activity document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    toast({
      title: "Document Removed",
      description: "Activity document has been removed",
      variant: "default",
    });
  }, [toast]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Activity Type"
          name="activity_type"
          type="select"
          entityId={entity.id}
          tableName="activities"
          updateFn={updateWrapper}
          value={entity.activity_type}
          options={ACTIVITY_TYPES}
          required
          validationRules={[
            { required: true, message: 'Activity type is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Approximate Numbers"
          name="approximate_numbers"
          type="number"
          entityId={entity.id}
          tableName="activities"
          updateFn={updateWrapper}
          value={entity.approximate_numbers}
          placeholder="e.g., 100 (head of cattle, hectares)"
          min={0}
          disabled={disabled}
        />
      </div>

      {showCropFields && (
        <div className="space-y-4">
          <AutoSaveFormField
            label="Crop Type"
            name="crop_type"
            entityId={entity.id}
            tableName="activities"
            updateFn={updateWrapper}
            value={entity.crop_type}
            placeholder="e.g., Wheat, Barley, Apples"
            maxLength={255}
            disabled={disabled}
          />
          <AutoSaveFormField
            label="Crop Varieties"
            name="crop_varieties"
            type="textarea"
            entityId={entity.id}
            tableName="activities"
            updateFn={updateWrapper}
            value={entity.crop_varieties ? entity.crop_varieties.join(', ') : ''}
            placeholder="e.g., Fuji, Gala (comma-separated)"
            maxLength={500}
            disabled={disabled}
            formatter={(value: string) => value ? value.split(',').map(v => v.trim()).filter(Boolean) : []}
          />
        </div>
      )}

      {showLivestockFields && (
        <AutoSaveFormField
          label="Livestock Type"
          name="livestock_type"
          type="select"
          entityId={entity.id}
          tableName="activities"
          updateFn={updateWrapper}
          value={entity.livestock_type}
          options={LIVESTOCK_TYPES}
          disabled={disabled}
        />
      )}

      <AutoSaveFormField
        label="Activity Description"
        name="activity_description"
        type="textarea"
        entityId={entity.id}
        tableName="activities"
        updateFn={updateWrapper}
        value={entity.activity_description}
        placeholder="Describe your farming activities in detail"
        maxLength={500}
        disabled={disabled}
      />

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="Activity Documentation"
          name="activity_document"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload activity plans, permits, or related documentation (PDF, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload activity documentation"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};