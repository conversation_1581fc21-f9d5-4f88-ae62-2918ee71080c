import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { AssetFormData, ASSET_CATEGORIES, EntityFormComponentProps } from '@/types/form-components';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';

const VEHICLE_TYPES = [
  { value: 'Tractor', label: 'Tractor' },
  { value: 'Truck', label: 'Truck' },
  { value: 'Utility Vehicle', label: 'Utility Vehicle/Ute' },
  { value: 'ATV/Quad', label: 'ATV/Quad Bike' },
  { value: 'Trailer', label: 'Trailer' },
  { value: 'Other Vehicle', label: 'Other Vehicle' },
];

const EQUIPMENT_TYPES = [
  { value: 'Harvester', label: 'Harvester' },
  { value: 'Plough', label: 'Plough' },
  { value: 'Seeder', label: 'Seeder' },
  { value: 'Cultivator', label: 'Cultivator' },
  { value: 'Mower', label: 'Mower' },
  { value: 'Sprayer', label: 'Sprayer' },
  { value: 'Irrigation Equipment', label: 'Irrigation Equipment' },
  { value: 'Generator', label: 'Generator' },
  { value: 'Other Equipment', label: 'Other Equipment' },
];

const INSURANCE_TYPES = [
  { value: 'Farm Property', label: 'Farm Property Insurance' },
  { value: 'Crop Insurance', label: 'Crop Insurance' },
  { value: 'Livestock Insurance', label: 'Livestock Insurance' },
  { value: 'Equipment Insurance', label: 'Equipment Insurance' },
  { value: 'Vehicle Insurance', label: 'Vehicle Insurance' },
  { value: 'Public Liability', label: 'Public Liability' },
  { value: 'Business Interruption', label: 'Business Interruption' },
  { value: 'Other Insurance', label: 'Other Insurance' },
];

export const AssetForm: React.FC<EntityFormComponentProps<AssetFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<AssetFormData>(onUpdate);
  const { uploadAndFinalizeDocument } = useOnboarding();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Handle file upload for asset documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'assets', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Asset document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    // Implementation would depend on your document management system
    toast({
      title: "Document Removed",
      description: "Asset document has been removed",
      variant: "default",
    });
  }, [toast]);
  // Determine asset type options based on category
  const getAssetTypeOptions = () => {
    switch (entity.asset_category) {
      case 'Vehicle':
        return VEHICLE_TYPES;
      case 'Equipment':
        return EQUIPMENT_TYPES;
      case 'Insurance':
        return INSURANCE_TYPES;
      default:
        return [];
    }
  };

  const assetTypeOptions = getAssetTypeOptions();
  const showMakeProvider = entity.asset_category === 'Vehicle' || entity.asset_category === 'Equipment';
  const showRegistrationPolicy = true; // All categories can have registration/policy numbers
  const showRenewalDate = entity.asset_category === 'Insurance' || entity.asset_type?.includes('License');

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Asset Category"
          name="asset_category"
          type="select"
          entityId={entity.id}
          tableName="assets"
          updateFn={updateWrapper}
          value={entity.asset_category}
          options={ASSET_CATEGORIES}
          required
          disabled={disabled}
        />

        {assetTypeOptions.length > 0 && (
          <AutoSaveFormField
            label="Asset Type"
            name="asset_type"
            type="select"
            entityId={entity.id}
            tableName="assets"
            updateFn={updateWrapper}
            value={entity.asset_type}
            options={assetTypeOptions}
            required
            disabled={disabled}
          />
        )}
      </div>

      {showMakeProvider && (
        <AutoSaveFormField
          label={entity.asset_category === 'Insurance' ? 'Insurance Provider' : 'Make/Brand'}
          name="make_or_provider"
          entityId={entity.id}
          tableName="assets"
          updateFn={updateWrapper}
          value={entity.make_or_provider}
          placeholder={
            entity.asset_category === 'Insurance'
              ? 'e.g., CGU, Allianz, Suncorp'
              : 'e.g., John Deere, Case IH, Ford'
          }
          maxLength={100}
          disabled={disabled}
        />
      )}

      {showRegistrationPolicy && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <AutoSaveFormField
            label={entity.asset_category === 'Insurance' ? 'Policy Number' : 'Registration Number'}
            name="registration_or_policy_number"
            entityId={entity.id}
            tableName="assets"
            updateFn={updateWrapper}
            value={entity.registration_or_policy_number}
            placeholder={
              entity.asset_category === 'Insurance'
                ? 'e.g., POL123456789'
                : 'e.g., ABC123 or Serial #12345'
            }
            maxLength={50}
            disabled={disabled}
          />

          {showRenewalDate && (
            <AutoSaveFormField
              label={entity.asset_category === 'Insurance' ? 'Renewal Date' : 'Expiry Date'}
              name="renewal_date"
              type="date"
              entityId={entity.id}
              tableName="assets"
              updateFn={updateWrapper}
              value={entity.renewal_date}
              description={
                entity.asset_category === 'Insurance'
                  ? 'When does this policy need to be renewed?'
                  : 'When does this registration expire?'
              }
              disabled={disabled}
            />
          )}
        </div>
      )}

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label={`${entity.asset_category || 'Asset'} Document`}
          name="asset_document"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description={
            entity.asset_category === 'Insurance'
              ? 'Upload insurance policy document (PDF, JPG, PNG, Word - max 10MB)'
              : entity.asset_category === 'Vehicle'
              ? 'Upload registration or compliance certificate (PDF, JPG, PNG, Word - max 10MB)'
              : 'Upload asset documentation (PDF, JPG, PNG, Word - max 10MB)'
          }
          placeholder="Click or drag to upload asset document"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};