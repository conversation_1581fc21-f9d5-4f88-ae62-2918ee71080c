import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { ContractFormData, EntityFormComponentProps } from '@/types/form-components';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';

const CONTRACT_TYPES = [
  { value: 'Labour Contract', label: 'Labour Contract' },
  { value: 'Equipment Rental', label: 'Equipment Rental' },
  { value: 'Service Agreement', label: 'Service Agreement' },
  { value: 'Supply Contract', label: 'Supply Contract' },
  { value: 'Land Lease', label: 'Land Lease' },
  { value: 'Harvesting', label: 'Harvesting Contract' },
  { value: 'Transport', label: 'Transport Contract' },
  { value: 'Maintenance', label: 'Maintenance Agreement' },
  { value: 'Other', label: 'Other' },
];

const PAYMENT_TERMS = [
  { value: 'Net 7', label: 'Net 7 days' },
  { value: 'Net 14', label: 'Net 14 days' },
  { value: 'Net 30', label: 'Net 30 days' },
  { value: 'Net 60', label: 'Net 60 days' },
  { value: 'Due on completion', label: 'Due on completion' },
  { value: 'Monthly', label: 'Monthly' },
  { value: 'Quarterly', label: 'Quarterly' },
  { value: 'Seasonally', label: 'Seasonally' },
  { value: 'Other', label: 'Other' },
];

export const ContractForm: React.FC<EntityFormComponentProps<ContractFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<ContractFormData>(onUpdate);
  const { uploadAndFinalizeDocument } = useOnboarding();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Handle file upload for contract documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'contracts', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Contract document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    // Implementation would depend on your document management system
    toast({
      title: "Document Removed",
      description: "Contract document has been removed",
      variant: "default",
    });
  }, [toast]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Contract Type"
          name="contract_type"
          type="select"
          entityId={entity.id}
          tableName="contracts"
          updateFn={updateWrapper}
          value={entity.contract_type}
          options={CONTRACT_TYPES}
          required
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Contract Description"
          name="contract_description"
          entityId={entity.id}
          tableName="contracts"
          updateFn={updateWrapper}
          value={entity.contract_description}
          placeholder="Brief description of the contract"
          maxLength={200}
          required
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Start Date"
          name="start_date"
          type="date"
          entityId={entity.id}
          tableName="contracts"
          updateFn={updateWrapper}
          value={entity.start_date}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Expiry Date"
          name="expiry_date"
          type="date"
          entityId={entity.id}
          tableName="contracts"
          updateFn={updateWrapper}
          value={entity.expiry_date}
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Contract Value (AUD)"
          name="contract_value"
          type="number"
          entityId={entity.id}
          tableName="contracts"
          updateFn={updateWrapper}
          value={entity.contract_value}
          placeholder="e.g., 25000"
          min={0}
          step={100}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Supplier ID"
          name="supplier_id"
          entityId={entity.id}
          tableName="contracts"
          updateFn={updateWrapper}
          value={entity.supplier_id}
          placeholder="Optional - Link to supplier"
          disabled={disabled}
        />
      </div>

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="Contract Document"
          name="contract_document"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload a copy of this contract agreement (PDF, Word, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload contract document"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};