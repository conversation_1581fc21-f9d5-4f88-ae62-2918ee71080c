# Form Components & Utilities

This directory contains reusable form components, hooks, and utilities designed to reduce code duplication and improve maintainability across the onboarding wizard.

## Overview

The abstractions are organized into three main categories:

1. **Custom Hooks** (`src/hooks/`) - Business logic and state management
2. **UI Components** (`src/components/form_components/`) - Reusable form UI elements
3. **Utility Functions** (`src/utils/`) - Validation, formatting, and helper functions

## Quick Start

```typescript
// Import everything you need
import { FormSection, FormField, EntityFormList } from '@/components/form_components';
import { useStepInitialization, useAutoSaveFormField } from '@/hooks';
import { validateFormField, formatPhoneNumber } from '@/utils';
```

## Components

### FormField

A standardized form field component with built-in validation, error display, and auto-save functionality.

```typescript
// Basic usage
<FormField
  label="Business Name"
  name="businessName"
  value={formData.businessName}
  onChange={(value) => setFormData(prev => ({ ...prev, businessName: value }))}
  required
  error={errors.businessName}
/>

// With auto-save
<AutoSaveFormField
  label="ABN"
  name="abn"
  value={businessData.abn}
  entityId={businessId}
  tableName="business_registration"
  updateFn={updateRecord}
  formatter={formatAbn}
  validationRules={[
    { rule: 'required' },
    { rule: 'abn' }
  ]}
/>
```

### FormSection

Standardized card-based wrapper for form sections with loading and error states.

```typescript
<FormSection
  title="Business Information"
  description="Core business details for registration"
  isLoading={isInitializing}
  error={initializationError}
  onRetry={retryInitialization}
>
  {/* Your form content here */}
</FormSection>
```

### EntityFormList

Generic component for managing dynamic lists of entities (contacts, addresses, etc.).

```typescript
<EntityFormList
  title="Contacts"
  description="Key business contacts"
  data={contactsData}
  stepId={step1Id}
  tableName="contacts"
  defaultValues={{ contact_type: 'Main Contact' }}
  renderForm={({ entity, onUpdate, disabled }) => (
    <ContactForm
      contact={entity}
      onUpdate={onUpdate}
      disabled={disabled}
    />
  )}
  createFn={createRecord}
  updateFn={updateRecord}
  deleteFn={deleteRecord}
  entityDisplayName="contact"
/>
```

### FileUploadField

File upload component with drag-and-drop, validation, and progress tracking.

```typescript
<FileUploadField
  label="License Document"
  name="licenseFile"
  onUpload={handleFileUpload}
  accept=".pdf,.jpg,.png"
  validation={{
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    maxSize: 10 * 1024 * 1024, // 10MB
  }}
  isUploading={isUploading}
  uploadProgress={uploadProgress}
  error={uploadError}
/>
```

## Hooks

### useStepInitialization

Manages the common initialization pattern for onboarding steps.

```typescript
const { isInitializing, error, isReady, retry } = useStepInitialization({
  sessionId,
  contextLoading,
  existingData: sessionData?.step1_businessProfile,
  initializeFn: ensureStep1BusinessProfileRecordExists,
  stepName: 'Business Profile',
});
```

### useAutoSaveFormField

Auto-saves form fields with debouncing and validation.

```typescript
const {
  value,
  onChange,
  onBlur,
  error,
  isSaving,
  hasUnsavedChanges,
} = useAutoSaveFormField({
  entityId: businessId,
  tableName: 'business_registration',
  fieldName: 'full_business_name',
  initialValue: businessData.full_business_name,
  updateFn: updateRecord,
  validationRules: [{ rule: 'required' }],
});
```

### useDynamicEntityList

Manages lists of entities with add/delete operations.

```typescript
const {
  addEntity,
  deleteEntity,
  isOperating,
  operationError,
} = useDynamicEntityList({
  stepId: step1Id,
  tableName: 'contacts',
  defaultValues: { contact_type: 'Main Contact' },
  disabled: !isReady,
  createFn: createRecord,
  updateFn: updateRecord,
  deleteFn: deleteRecord,
  entityDisplayName: 'contact',
});
```

### useFormValidation

Comprehensive form validation with error state management.

```typescript
const validation = useFormValidation({
  validationSchema: {
    businessName: [{ rule: 'required' }],
    email: [{ rule: 'required' }, { rule: 'email' }],
    abn: [{ rule: 'abn' }],
  },
  validateOnChange: true,
});

// Validate entire form
const errors = validation.validateAll(formData);
```

## Utilities

### Validation Functions

```typescript
import { validateFormField, ValidationConfig } from '@/utils';

const rules: ValidationConfig[] = [
  { rule: 'required' },
  { rule: 'email' }
];

const error = validateFormField(email, rules);
```

### Formatting Functions

```typescript
import { formatPhoneNumber, formatAbn, formatAcn } from '@/utils';

const formattedPhone = formatPhoneNumber('0412345678');
// Result: "+61 4 1234 5678"

const formattedAbn = formatAbn('***********');
// Result: "12 ***********"
```

## Migration Guide

To migrate existing components to use these abstractions:

### Before (Business Profile Step)
```typescript
const [isStepInitializing, setIsStepInitializing] = useState(true);
const [initializationError, setInitializationError] = useState(null);
// ... complex initialization logic
```

### After
```typescript
const { isInitializing, error, isReady, retry } = useStepInitialization({
  sessionId,
  contextLoading,
  existingData: sessionData?.step1_businessProfile,
  initializeFn: ensureStep1BusinessProfileRecordExists,
  stepName: 'Business Profile',
});
```

### Before (Form Fields)
```typescript
<div className="space-y-2">
  <Label htmlFor="businessName">Business Name *</Label>
  <Input
    id="businessName"
    value={formData.businessName}
    onChange={(e) => handleInputChange('businessName', e.target.value)}
    onBlur={() => handleSaveField('businessName')}
  />
  {errors.businessName && <p className="text-destructive">{errors.businessName}</p>}
</div>
```

### After
```typescript
<AutoSaveFormField
  label="Business Name"
  name="businessName"
  value={businessData.businessName}
  entityId={businessId}
  tableName="business_registration"
  updateFn={updateRecord}
  required
  validationRules={[{ rule: 'required' }]}
/>
```

## Type Safety

All components and hooks are fully typed with TypeScript:

```typescript
// Generic components work with your data types
type ContactData = {
  id: string;
  name: string;
  email: string;
  contact_type: string;
};

const ContactList = EntityFormList<ContactData>;
```

## Performance Considerations

- **Debouncing**: Auto-save fields debounce changes to prevent excessive API calls
- **Memoization**: Components use React.memo and useMemo where appropriate
- **Memory Management**: All hooks include cleanup logic to prevent memory leaks
- **Optimistic Updates**: Form changes update UI immediately while saving in background

## Testing

Each abstraction includes comprehensive TypeScript types and error handling for robust testing:

```typescript
// Components accept test IDs and support testing utilities
<FormField
  data-testid="business-name-field"
  label="Business Name"
  // ... other props
/>
```

## Best Practices

1. **Use AutoSaveFormField** for fields that should save automatically
2. **Use FormSection** for consistent card-based layouts
3. **Use EntityFormList** for dynamic lists instead of custom implementations
4. **Combine hooks** for complex state management scenarios
5. **Import from index files** for cleaner imports
6. **Provide meaningful validation rules** and error messages
7. **Use TypeScript strictly** for better development experience 