import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Trash2, Plus, AlertTriangle } from 'lucide-react';
import { cn } from '@/utils/utils';
import { FormSection } from './FormSection';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';

/**
 * Props for individual entity forms
 */
export interface EntityFormProps<T> {
    /** Entity data */
    entity: T;
    /** Whether the form is disabled */
    disabled: boolean;
    /** Function to handle entity updates */
    onUpdate: (entityId: string, data: Partial<T>) => Promise<void>;
    /** Function to handle entity deletion */
    onDelete: (entityId: string) => Promise<void>;
    /** Whether delete operation is in progress */
    isDeleting?: boolean;
}

/**
 * Props for the EntityFormList component
 */
export interface EntityFormListProps<T extends { id: string }> {
    /** Section title */
    title: string;
    /** Section description */
    description: string;
    /** Array of entity data */
    data: T[];
    /** Parent step ID for linking new entities */
    stepId?: string;
    /** Database table name */
    tableName: string;
    /** Default values for new entities */
    defaultValues: Partial<T>;
    /** Function to render individual entity forms */
    renderForm: (props: EntityFormProps<T>) => React.ReactNode;
    /** Whether the entire list is disabled */
    disabled?: boolean;
    /** Entity display name (singular) */
    entityDisplayName?: string;
    /** Entity display name (plural) */
    entityDisplayNamePlural?: string;
    /** Additional CSS classes */
    className?: string;
    /** Whether to show the section wrapper */
    showSectionWrapper?: boolean;
    /** Custom add button text */
    addButtonText?: string;
    /** Whether to show entity count */
    showEntityCount?: boolean;
    /** Maximum number of entities allowed */
    maxEntities?: number;
    /** Minimum number of entities required */
    minEntities?: number;
    /** Custom empty state message */
    emptyStateMessage?: string;
    /** Whether to confirm before delete */
    confirmDelete?: boolean;
}

/**
 * Generic component for managing lists of form entities
 * Provides add, edit, delete functionality with consistent UI
 */
export const EntityFormList = <T extends { id: string }>({
    title,
    description,
    data,
    stepId,
    tableName,
    defaultValues,
    renderForm,
    disabled = false,
    entityDisplayName = 'item',
    entityDisplayNamePlural,
    className,
    showSectionWrapper = true,
    addButtonText,
    showEntityCount = true,
    maxEntities,
    minEntities,
    emptyStateMessage,
    confirmDelete = false,
}: EntityFormListProps<T>) => {
    const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());

    const pluralName = entityDisplayNamePlural || `${entityDisplayName}s`;
    const finalAddButtonText = addButtonText || `Add ${entityDisplayName}`;

    // Use the entity list management hook
    const {
        addEntity,
        deleteEntity: deleteEntityHook,
        updateEntity,
        isLoading: isOperating,
        operationError,
        clearOperationError: clearError,
        canAddEntity: canAdd,
        canDeleteEntity: canDelete,
    } = useEntityListManagement({
        stepId,
        tableName,
        initialEntities: data,
        createDefaultEntity: () => defaultValues,
        entityDisplayName,
        disabled,
        maxEntities,
        minEntities,
    });

    // Handle entity deletion with confirmation
    const handleDelete = useCallback(async (entityId: string) => {
        if (confirmDelete) {
            const confirmMessage = `Are you sure you want to delete this ${entityDisplayName}? This action cannot be undone.`;
            if (!window.confirm(confirmMessage)) {
                return;
            }
        }

        // Check if delete is allowed
        if (!canDelete) {
            return;
        }

        setDeletingIds(prev => new Set(prev).add(entityId));

        try {
            await deleteEntityHook(entityId);
        } finally {
            setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(entityId);
                return newSet;
            });
        }
    }, [confirmDelete, entityDisplayName, canDelete, deleteEntityHook]);

    // Handle adding new entity
    const handleAdd = useCallback(async () => {
        clearError();
        await addEntity();
    }, [addEntity, clearError]);

    // Calculate if add button should be disabled
    const isAddDisabled = !canAdd || isOperating;

    // Render the content
    const renderContent = () => (
        <div className={cn('space-y-4', className)}>
            {/* Entity list */}
            {data.map((entity, index) => (
                <Card key={entity.id} className="relative">
                    <CardContent className="pt-4">
                        {/* Delete button */}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="absolute top-2 right-2 text-muted-foreground hover:text-destructive"
                            onClick={() => handleDelete(entity.id)}
                            disabled={disabled || deletingIds.has(entity.id) || isOperating}
                            aria-label={`Delete ${entityDisplayName} ${index + 1}`}
                            title={`Delete ${entityDisplayName}`}
                        >
                            {deletingIds.has(entity.id) ? (
                                <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
                            ) : (
                                <Trash2 className="h-4 w-4" aria-hidden="true" />
                            )}
                            <span className="sr-only">
                                {deletingIds.has(entity.id) ? `Deleting ${entityDisplayName}...` : `Delete ${entityDisplayName}`}
                            </span>
                        </Button>

                        {/* Entity form */}
                        {renderForm({
                            entity,
                            disabled: disabled || isOperating,
                            onUpdate: updateEntity,
                            onDelete: handleDelete,
                            isDeleting: deletingIds.has(entity.id),
                        })}
                    </CardContent>
                </Card>
            ))}

            {/* Empty state */}
            {data.length === 0 && (
                <div className="text-center py-8 text-muted-foreground" role="status">
                    <p className="text-sm">
                        {emptyStateMessage || `No ${pluralName} added yet.`}
                    </p>
                </div>
            )}

            {/* Operation error */}
            {operationError && (
                <div className="rounded-md border border-destructive/20 bg-destructive/5 p-4" role="alert">
                    <div className="flex items-start space-x-2">
                        <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" aria-hidden="true" />
                        <div className="flex-1">
                            <p className="text-sm text-destructive">{operationError}</p>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={clearError}
                                className="mt-2 h-auto p-0 text-xs text-destructive hover:text-destructive"
                                aria-label="Dismiss error message"
                            >
                                Dismiss
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* Add button and count */}
            <div className="flex items-center justify-between">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAdd}
                    disabled={isAddDisabled}
                    className="flex items-center space-x-2"
                    aria-label={isOperating ? `Adding ${entityDisplayName}...` : finalAddButtonText}
                >
                    {isOperating ? (
                        <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
                    ) : (
                        <Plus className="h-4 w-4" aria-hidden="true" />
                    )}
                    <span>{finalAddButtonText}</span>
                </Button>

                {showEntityCount && (
                    <div className="text-sm text-muted-foreground">
                        {data.length} {data.length === 1 ? entityDisplayName : pluralName}
                        {maxEntities && ` of ${maxEntities} max`}
                    </div>
                )}
            </div>

            {/* Constraints info */}
            {(minEntities || maxEntities) && (
                <div className="text-xs text-muted-foreground">
                    {minEntities && `Minimum: ${minEntities} ${minEntities === 1 ? entityDisplayName : pluralName}`}
                    {minEntities && maxEntities && ' • '}
                    {maxEntities && `Maximum: ${maxEntities} ${maxEntities === 1 ? entityDisplayName : pluralName}`}
                </div>
            )}
        </div>
    );

    // Render with or without section wrapper
    if (showSectionWrapper) {
        return (
            <FormSection
                title={title}
                description={description}
                disabled={disabled}
            >
                {renderContent()}
            </FormSection>
        );
    }

    return renderContent();
};

/**
 * Higher-order component for creating typed EntityFormList components
 * Useful for creating reusable entity lists with specific types
 */
export function createEntityFormList<T extends { id: string }>() {
    return EntityFormList<T>;
}

/**
 * Props for simple entity forms (for basic use cases)
 */
export interface SimpleEntityFormProps<T> extends EntityFormProps<T> {
    /** Field configurations for auto-generation */
    fields: Array<{
        key: keyof T;
        label: string;
        type?: 'text' | 'email' | 'tel' | 'number' | 'date' | 'select';
        required?: boolean;
        options?: Array<{ value: string; label: string }>;
        placeholder?: string;
    }>;
}

/**
 * Simple entity form component for basic CRUD operations
 * Auto-generates form fields based on configuration
 */
export const SimpleEntityForm = <T extends { id: string }>({
    entity,
    disabled,
    onUpdate,
    fields,
}: SimpleEntityFormProps<T>) => {
    const [formData, setFormData] = useState<Partial<T>>({});

    // Initialize form data
    React.useEffect(() => {
        const initialData: Partial<T> = {};
        fields.forEach(field => {
            initialData[field.key] = entity[field.key];
        });
        setFormData(initialData);
    }, [entity, fields]);

    const handleFieldChange = (key: keyof T, value: string | number | boolean | null) => {
        setFormData(prev => ({ ...prev, [key]: value }));
    };

    const handleFieldBlur = async (key: keyof T) => {
        const value = formData[key];
        if (value !== entity[key]) {
            try {
                await onUpdate(entity.id, { [key]: value } as Partial<T>);
            } catch (error) {
                console.error('Failed to update field:', error);
                // Reset to original value on error
                setFormData(prev => ({ ...prev, [key]: entity[key] }));
            }
        }
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {fields.map(field => (
                <div key={String(field.key)} className="space-y-2">
                    <label className="text-sm font-medium">
                        {field.label}
                        {field.required && <span className="text-destructive ml-1">*</span>}
                    </label>

                    {field.type === 'select' ? (
                        <select
                            value={String(formData[field.key] || '')}
                            onChange={(e) => handleFieldChange(field.key, e.target.value)}
                            onBlur={() => handleFieldBlur(field.key)}
                            disabled={disabled}
                            className="w-full px-3 py-2 border rounded-md"
                            required={field.required}
                        >
                            <option value="">Select...</option>
                            {field.options?.map(option => (
                                <option key={option.value} value={option.value}>
                                    {option.label}
                                </option>
                            ))}
                        </select>
                    ) : (
                        <input
                            type={field.type || 'text'}
                            value={String(formData[field.key] || '')}
                            onChange={(e) => handleFieldChange(field.key, e.target.value)}
                            onBlur={() => handleFieldBlur(field.key)}
                            placeholder={field.placeholder}
                            disabled={disabled}
                            required={field.required}
                            className="w-full px-3 py-2 border rounded-md"
                        />
                    )}
                </div>
            ))}
        </div>
    );
}; 