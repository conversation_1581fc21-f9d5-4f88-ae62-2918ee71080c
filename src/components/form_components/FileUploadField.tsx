import React, { useState, useRef, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Loader2, Upload, File, X, CheckCircle2 } from 'lucide-react';
import { cn } from '@/utils/utils';

/**
 * File validation configuration
 */
export interface FileValidationConfig {
    /** Allowed file types (MIME types) */
    allowedTypes?: string[];
    /** Maximum file size in bytes */
    maxSize?: number;
    /** Minimum file size in bytes */
    minSize?: number;
    /** Custom validation function */
    customValidator?: (file: File) => string | null;
}

/**
 * Props for the FileUploadField component
 */
export interface FileUploadFieldProps {
    /** Field label */
    label: string;
    /** Field name/identifier */
    name: string;
    /** Upload handler function */
    onUpload: (file: File) => Promise<void>;
    /** Whether the field is disabled */
    disabled?: boolean;
    /** Accept attribute for file input */
    accept?: string;
    /** File validation configuration */
    validation?: FileValidationConfig;
    /** Current upload progress (0-100) */
    uploadProgress?: number;
    /** Whether upload is in progress */
    isUploading?: boolean;
    /** Current error message */
    error?: string;
    /** Success message */
    successMessage?: string;
    /** Field description/help text */
    description?: string;
    /** Additional CSS classes */
    className?: string;
    /** Whether to show drag and drop area */
    showDropZone?: boolean;
    /** Placeholder text */
    placeholder?: string;
    /** Whether field is required */
    required?: boolean;
    /** Currently uploaded file info */
    uploadedFile?: {
        name: string;
        size: number;
        uploadedAt: Date;
    };
    /** Function to remove uploaded file */
    onRemoveFile?: () => void;
}

/**
 * Default validation configuration
 */
const DEFAULT_VALIDATION: FileValidationConfig = {
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
    maxSize: 10 * 1024 * 1024, // 10MB
    minSize: 1024, // 1KB
};

/**
 * Format file size for display
 */
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file type description
 */
const getFileTypeDescription = (allowedTypes?: string[]): string => {
    if (!allowedTypes || allowedTypes.length === 0) return 'any file type';

    const typeMap: Record<string, string> = {
        'application/pdf': 'PDF',
        'image/jpeg': 'JPEG',
        'image/jpg': 'JPG',
        'image/png': 'PNG',
        'image/gif': 'GIF',
        'text/csv': 'CSV',
        'application/vnd.ms-excel': 'Excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
    };

    const descriptions = allowedTypes.map(type => typeMap[type] || type.split('/')[1]?.toUpperCase());

    if (descriptions.length === 1) return descriptions[0];
    if (descriptions.length === 2) return descriptions.join(' or ');

    return descriptions.slice(0, -1).join(', ') + ', or ' + descriptions[descriptions.length - 1];
};

/**
 * Validate a file against the validation configuration
 */
const validateFile = (file: File, config: FileValidationConfig): string | null => {
    const { allowedTypes, maxSize, minSize, customValidator } = config;

    // Check file size
    if (maxSize && file.size > maxSize) {
        return `File size must be less than ${formatFileSize(maxSize)}`;
    }

    if (minSize && file.size < minSize) {
        return `File size must be at least ${formatFileSize(minSize)}`;
    }

    // Check file type
    if (allowedTypes && allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        const typeDesc = getFileTypeDescription(allowedTypes);
        return `Please select a ${typeDesc} file`;
    }

    // Custom validation
    if (customValidator) {
        return customValidator(file);
    }

    return null;
};

/**
 * Reusable file upload component with validation, progress tracking, and error handling
 */
export const FileUploadField: React.FC<FileUploadFieldProps> = ({
    label,
    name,
    onUpload,
    disabled = false,
    accept,
    validation = DEFAULT_VALIDATION,
    uploadProgress = 0,
    isUploading = false,
    error,
    successMessage,
    description,
    className,
    showDropZone = true,
    placeholder = 'Choose file or drag and drop',
    required = false,
    uploadedFile,
    onRemoveFile,
}) => {
    const [isDragOver, setIsDragOver] = useState(false);
    const [localError, setLocalError] = useState<string>('');
    const fileInputRef = useRef<HTMLInputElement>(null);

    const currentError = error || localError;
    const validationConfig = { ...DEFAULT_VALIDATION, ...validation };

    // Handle file selection
    const handleFileSelect = useCallback(async (file: File) => {
        setLocalError('');

        // Validate file
        const validationError = validateFile(file, validationConfig);
        if (validationError) {
            setLocalError(validationError);
            return;
        }

        try {
            await onUpload(file);
            // Clear the file input after successful upload
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        } catch (uploadError) {
            // Error handling is typically done by the parent component
            console.error('Upload error:', uploadError);
        }
    }, [onUpload, validationConfig]);

    // Handle input change
    const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
    }, [handleFileSelect]);

    // Handle drag and drop
    const handleDrop = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        setIsDragOver(false);

        if (disabled || isUploading) return;

        const file = event.dataTransfer.files[0];
        if (file) {
            handleFileSelect(file);
        }
    }, [handleFileSelect, disabled, isUploading]);

    const handleDragOver = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        if (!disabled && !isUploading) {
            setIsDragOver(true);
        }
    }, [disabled, isUploading]);

    const handleDragLeave = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        setIsDragOver(false);
    }, []);

    // Handle click to open file dialog
    const handleClick = useCallback(() => {
        if (!disabled && !isUploading && fileInputRef.current) {
            fileInputRef.current.click();
        }
    }, [disabled, isUploading]);

    // Clear error when props change
    React.useEffect(() => {
        if (error !== localError) {
            setLocalError('');
        }
    }, [error, localError]);

    const acceptAttribute = accept || validationConfig.allowedTypes?.join(',');
    const typeDescription = getFileTypeDescription(validationConfig.allowedTypes);
    const maxSizeDesc = validationConfig.maxSize ? formatFileSize(validationConfig.maxSize) : '';

    return (
        <div className={cn('space-y-2', className)}>
            {/* Label */}
            <Label className="text-sm font-medium">
                {label}
                {required && <span className="text-destructive ml-1">*</span>}
            </Label>

            {/* File input (hidden) */}
            <Input
                ref={fileInputRef}
                type="file"
                accept={acceptAttribute}
                onChange={handleInputChange}
                disabled={disabled || isUploading}
                className="hidden"
                name={name}
            />

            {/* Upload area */}
            {showDropZone ? (
                <div
                    className={cn(
                        'border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer',
                        isDragOver && 'border-primary bg-primary/5',
                        currentError && 'border-destructive',
                        disabled && 'opacity-50 cursor-not-allowed',
                        isUploading && 'cursor-not-allowed',
                        !isDragOver && !currentError && 'border-border hover:border-primary/50'
                    )}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onClick={handleClick}
                >
                    <div className="flex flex-col items-center space-y-2 text-center">
                        {isUploading ? (
                            <>
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                                <p className="text-sm text-muted-foreground">Uploading...</p>
                                {uploadProgress > 0 && (
                                    <div className="w-full max-w-xs">
                                        <div className="bg-secondary rounded-full h-2">
                                            <div
                                                className="bg-primary h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${uploadProgress}%` }}
                                            />
                                        </div>
                                        <p className="text-xs text-muted-foreground mt-1">{uploadProgress}%</p>
                                    </div>
                                )}
                            </>
                        ) : uploadedFile ? (
                            <>
                                <CheckCircle2 className="h-8 w-8 text-green-600" />
                                <div className="space-y-1">
                                    <p className="text-sm font-medium">{uploadedFile.name}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {formatFileSize(uploadedFile.size)} • Uploaded {uploadedFile.uploadedAt.toLocaleDateString()}
                                    </p>
                                </div>
                                {onRemoveFile && (
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onRemoveFile();
                                        }}
                                        className="mt-2"
                                    >
                                        <X className="h-4 w-4 mr-1" />
                                        Remove
                                    </Button>
                                )}
                            </>
                        ) : (
                            <>
                                <Upload className="h-8 w-8 text-muted-foreground" />
                                <div className="space-y-1">
                                    <p className="text-sm font-medium">{placeholder}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {typeDescription} files up to {maxSizeDesc}
                                    </p>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            ) : (
                <div className="flex items-center space-x-2">
                    <Input
                        type="text"
                        value=""
                        placeholder={placeholder}
                        readOnly
                        onClick={handleClick}
                        className={cn(
                            'cursor-pointer',
                            currentError && 'border-destructive',
                            disabled && 'cursor-not-allowed'
                        )}
                        disabled={disabled || isUploading}
                    />
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleClick}
                        disabled={disabled || isUploading}
                    >
                        {isUploading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <File className="h-4 w-4" />
                        )}
                    </Button>
                </div>
            )}

            {/* Description */}
            {description && (
                <p className="text-xs text-muted-foreground">{description}</p>
            )}

            {/* Success message */}
            {successMessage && !currentError && (
                <p className="text-sm text-green-600">{successMessage}</p>
            )}

            {/* Error message */}
            {currentError && (
                <p className="text-sm text-destructive" role="alert">
                    {currentError}
                </p>
            )}
        </div>
    );
}; 