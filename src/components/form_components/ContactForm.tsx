import React from 'react';
import { AutoSaveFormField } from './FormField';
import { ContactFormData, CONTACT_TYPES, EntityFormComponentProps } from '@/types/form-components';
import { validators, formatters } from '@/utils/validation-patterns';
import { useFormUpdateWrapper } from '@/hooks/use-form-update-wrapper';

export const ContactForm: React.FC<EntityFormComponentProps<ContactFormData>> = ({
  entity,
  disabled,
  onUpdate,
}) => {
  const { updateWrapper } = useFormUpdateWrapper<ContactFormData>(onUpdate);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <AutoSaveFormField
        label="Contact Name"
        name="name"
        entityId={entity.id}
        tableName="contacts"
        updateFn={updateWrapper}
        value={entity.name}
        placeholder="e.g., <PERSON>"
        maxLength={100}
        required
        disabled={disabled}
      />

      <AutoSaveFormField
        label="Contact Type"
        name="contact_type"
        type="select"
        entityId={entity.id}
        tableName="contacts"
        updateFn={updateWrapper}
        value={entity.contact_type}
        options={CONTACT_TYPES}
        required
        disabled={disabled}
      />

      <AutoSaveFormField
        label="Email"
        name="email"
        type="email"
        entityId={entity.id}
        tableName="contacts"
        updateFn={updateWrapper}
        value={entity.email}
        placeholder="e.g., <EMAIL>"
        maxLength={100}
        disabled={disabled}
        validator={validators.email}
      />

      <AutoSaveFormField
        label="Phone"
        name="phone"
        type="tel"
        entityId={entity.id}
        tableName="contacts"
        updateFn={updateWrapper}
        value={entity.phone}
        placeholder="e.g., +61 2 1234 5678"
        formatter={formatters.phone}
        validator={validators.phone}
        disabled={disabled}
      />

      <div className="md:col-span-2">
        <AutoSaveFormField
          label="Title/Firm"
          name="title_or_firm"
          entityId={entity.id}
          tableName="contacts"
          updateFn={updateWrapper}
          value={entity.title_or_firm}
          placeholder="e.g., Farm Manager, Smith & Associates"
          maxLength={100}
          disabled={disabled}
        />
      </div>
    </div>
  );
};