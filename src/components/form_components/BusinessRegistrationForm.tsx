import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { useFormManagement, useBusinessRegistrationManagement } from '@/hooks/use-form-management';
import { FormSection } from './FormSection';
import { AutoSaveFormField } from './FormField';
import { BusinessRegistrationFormData, BUSINESS_STRUCTURES } from '@/types/form-components';
import { validators, formatters } from '@/utils/validation-patterns';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { logger } from '@/utils/logger';

interface BusinessRegistrationFormProps {
    /** Step 1 ID for linking the business registration */
    stepId: string | null;
    /** Existing business registration data (if any) */
    businessRegistrationData?: BusinessRegistrationFormData | null;
    /** Whether the form is disabled */
    disabled: boolean;
    /** Loading state */
    isLoading?: boolean;
    /** Error state */
    error?: string | null;
    /** Retry function */
    onRetry?: () => void;
}

interface BusinessRegistrationForm {
    full_business_name: string;
    trading_name: string;
    abn: string;
    acn: string;
    business_structure: string;
    is_gst_registered: boolean;
    primary_business_phone: string;
}

export const BusinessRegistrationForm: React.FC<BusinessRegistrationFormProps> = ({
    stepId,
    businessRegistrationData,
    disabled,
    isLoading,
    error,
    onRetry,
}) => {
    const { updateRecord } = useOnboarding();

    // Initialize form data from existing record or defaults (memoized to prevent infinite loops)
    const initialData: BusinessRegistrationForm = useMemo(() => ({
        full_business_name: businessRegistrationData?.full_business_name || '',
        trading_name: businessRegistrationData?.trading_name || '',
        abn: businessRegistrationData?.abn || '',
        acn: businessRegistrationData?.acn || '',
        business_structure: businessRegistrationData?.business_structure || 'Sole Trader',
        is_gst_registered: businessRegistrationData?.is_gst_registered || false,
        primary_business_phone: businessRegistrationData?.primary_business_phone || '',
    }), [businessRegistrationData]);

    // Business registration management hook
    const {
        validateBusinessField,
        createBusinessRegistration,
        isCreatingRecord,
    } = useBusinessRegistrationManagement(stepId);

    // Form management hook for the entire form
    const {
        formData,
        fieldErrors,
        isFieldSaving,
        handleFieldChange,
        handleFieldBlur,
        isDirty,
        isSaving,
    } = useFormManagement<BusinessRegistrationFormData>({
        stepId: null, // Disable auto-save to prevent conflicts with manual creation logic
        tableName: 'business_registration',
        initialData,
        validateField: validateBusinessField,
        disabled: disabled || isCreatingRecord,
        displayName: 'BusinessRegistration',
    });

    // Note: Auto-save is disabled for this form to prevent conflicts with creation logic

    // Only create record if it doesn't exist and we have minimum required data
    const shouldCreateRecord = useMemo(() => {
        // Don't create if record already exists, creation in progress, or no step ID
        if (businessRegistrationData?.id || isCreatingRecord || !stepId) {
            return false;
        }

        // Only create if we have the minimum required fields
        const hasValidName = formData.full_business_name?.trim();
        const abnValidationResult = validators.abn(formData.abn || '');
        const hasValidAbn = !abnValidationResult && formData.abn?.trim();

        // Must have both required fields to trigger creation
        return !!(hasValidName && hasValidAbn);
    }, [
        businessRegistrationData?.id,
        isCreatingRecord,
        stepId,
        formData.full_business_name,
        formData.abn
    ]);

    // Form data is now captured directly in the effect to prevent infinite dependency loops

    // Create a ref to track if creation is in progress to prevent race conditions
    const creationAttemptRef = useRef(false);

    // Check if we should attempt to create a record
    useEffect(() => {
        if (!shouldCreateRecord || creationAttemptRef.current) return;

        const attemptRecordCreation = async () => {
            if (creationAttemptRef.current) return; // Additional guard
            
            creationAttemptRef.current = true;
            
            // Capture form data at the time of creation to avoid stale closure
            const dataToCreate = {
                full_business_name: formData.full_business_name,
                abn: formData.abn,
                business_structure: formData.business_structure,
                is_gst_registered: formData.is_gst_registered,
                trading_name: formData.trading_name,
                acn: formData.acn,
                primary_business_phone: formData.primary_business_phone,
            };
            
            try {
                logger.info('Attempting to create business registration record', {
                    stepId,
                    businessName: dataToCreate.full_business_name,
                    abn: dataToCreate.abn
                });

                await createBusinessRegistration(dataToCreate);
            } catch (error) {
                logger.error('Failed to create business registration record', error);
            } finally {
                creationAttemptRef.current = false;
            }
        };

        // Debounce the creation attempt to avoid rapid calls
        const timeoutId = setTimeout(attemptRecordCreation, 1500);
        return () => {
            clearTimeout(timeoutId);
            creationAttemptRef.current = false;
        };
    }, [shouldCreateRecord, stepId, createBusinessRegistration]);

    // Show creation progress message when creating record
    if (isCreatingRecord) {
        return (
            <FormSection
                title="Business Information"
                description="Creating your business registration record..."
                disabled={true}
                isLoading={true}
            >
                <div className="text-center py-8">
                    <p className="text-sm text-muted-foreground">
                        Setting up your business profile with the provided information...
                    </p>
                </div>
            </FormSection>
        );
    }

    // Show form for data collection or editing
    return (
        <FormSection
            title="Business Information"
            description={businessRegistrationData?.id
                ? "Edit your business registration details. Changes are saved automatically."
                : "Enter your business details. Your registration will be created automatically when you provide valid information."
            }
            disabled={disabled}
            isLoading={isLoading}
            error={error}
            onRetry={onRetry}
        >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Full Business Name */}
                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        Full Business Name (Legal Name)
                        <span className="text-destructive ml-1">*</span>
                    </label>
                    <input
                        type="text"
                        value={formData.full_business_name}
                        onChange={(e) => handleFieldChange('full_business_name', e.target.value)}
                        onBlur={() => handleFieldBlur('full_business_name')}
                        placeholder="e.g., John Smith Pty Ltd"
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                        maxLength={200}
                    />
                    {fieldErrors.full_business_name && (
                        <p className="text-sm text-destructive">{fieldErrors.full_business_name}</p>
                    )}
                </div>

                {/* Trading Name */}
                <div className="space-y-2">
                    <label className="text-sm font-medium">Trading Name (if different)</label>
                    <input
                        type="text"
                        value={formData.trading_name}
                        onChange={(e) => handleFieldChange('trading_name', e.target.value)}
                        onBlur={() => handleFieldBlur('trading_name')}
                        placeholder="e.g., JS Farms"
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                        maxLength={200}
                    />
                    {fieldErrors.trading_name && (
                        <p className="text-sm text-destructive">{fieldErrors.trading_name}</p>
                    )}
                </div>

                {/* ABN */}
                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        ABN (Australian Business Number)
                        <span className="text-destructive ml-1">*</span>
                    </label>
                    <input
                        type="text"
                        value={formData.abn}
                        onChange={(e) => {
                            const formatted = formatters.abn(e.target.value);
                            handleFieldChange('abn', formatted);
                        }}
                        onBlur={() => handleFieldBlur('abn')}
                        placeholder="e.g., **************"
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                        maxLength={14}
                    />
                    {fieldErrors.abn && (
                        <p className="text-sm text-destructive">{fieldErrors.abn}</p>
                    )}
                </div>

                {/* ACN */}
                <div className="space-y-2">
                    <label className="text-sm font-medium">ACN (Australian Company Number)</label>
                    <input
                        type="text"
                        value={formData.acn}
                        onChange={(e) => {
                            const formatted = formatters.acn ? formatters.acn(e.target.value) : e.target.value;
                            handleFieldChange('acn', formatted);
                        }}
                        onBlur={() => handleFieldBlur('acn')}
                        placeholder="e.g., ***********"
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                        maxLength={11}
                    />
                    {fieldErrors.acn && (
                        <p className="text-sm text-destructive">{fieldErrors.acn}</p>
                    )}
                </div>

                {/* Business Structure */}
                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        Business Structure
                        <span className="text-destructive ml-1">*</span>
                    </label>
                    <select
                        value={formData.business_structure}
                        onChange={(e) => handleFieldChange('business_structure', e.target.value)}
                        onBlur={() => handleFieldBlur('business_structure')}
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                    >
                        {BUSINESS_STRUCTURES.map(structure => (
                            <option key={structure.value} value={structure.value}>
                                {structure.label}
                            </option>
                        ))}
                    </select>
                    {fieldErrors.business_structure && (
                        <p className="text-sm text-destructive">{fieldErrors.business_structure}</p>
                    )}
                </div>

                {/* Primary Business Phone */}
                <div className="space-y-2">
                    <label className="text-sm font-medium">Phone Number</label>
                    <input
                        type="tel"
                        value={formData.primary_business_phone}
                        onChange={(e) => {
                            const formatted = formatters.phone(e.target.value);
                            handleFieldChange('primary_business_phone', formatted);
                        }}
                        onBlur={() => handleFieldBlur('primary_business_phone')}
                        placeholder="e.g., +61 2 1234 5678"
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                    />
                    {fieldErrors.primary_business_phone && (
                        <p className="text-sm text-destructive">{fieldErrors.primary_business_phone}</p>
                    )}
                </div>
            </div>

            {/* GST Registration */}
            <div className="pt-4">
                <div className="space-y-2">
                    <label className="text-sm font-medium">GST Registration</label>
                    <select
                        value={formData.is_gst_registered ? 'true' : 'false'}
                        onChange={(e) => handleFieldChange('is_gst_registered', e.target.value === 'true')}
                        onBlur={() => handleFieldBlur('is_gst_registered')}
                        className="w-full px-3 py-2 border rounded-md"
                        disabled={disabled}
                    >
                        <option value="false">No, Not GST Registered</option>
                        <option value="true">Yes, GST Registered</option>
                    </select>
                    <p className="text-xs text-muted-foreground">
                        Select if your business is registered for GST
                    </p>
                    {fieldErrors.is_gst_registered && (
                        <p className="text-sm text-destructive">{fieldErrors.is_gst_registered}</p>
                    )}
                </div>
            </div>

            {/* Status indicators */}
            <div className="pt-4 border-t">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div>
                        {isDirty && (
                            <span className="text-amber-600">
                                {isSaving ? 'Saving changes...' : 'Unsaved changes'}
                            </span>
                        )}
                        {!isDirty && !businessRegistrationData?.id && (
                            <span>
                                Enter valid business name and ABN to create your registration
                            </span>
                        )}
                        {!isDirty && businessRegistrationData?.id && (
                            <span className="text-green-600">All changes saved</span>
                        )}
                    </div>

                    {businessRegistrationData?.id && (
                        <span className="text-xs">
                            Registration ID: {businessRegistrationData.id.slice(0, 8)}...
                        </span>
                    )}
                </div>
            </div>
        </FormSection>
    );
}; 