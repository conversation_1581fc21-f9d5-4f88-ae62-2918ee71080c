
import { Star, Users, Shield, TrendingUp } from "lucide-react";

const SocialProof = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      location: "Wheat Farmer, NSW",
      content: "NewTerra cut our accounting costs by $3,400 annually and their compliance tracking means I never miss a deadline. The digital migration was seamless.",
      rating: 5,
      savings: "$3,400"
    },
    {
      name: "<PERSON>",
      location: "Cattle Operation, QLD",
      content: "Finally, professionals who understand seasonal cash flows and cattle depreciation. The integrated platform saves me 10 hours per month.",
      rating: 5,
      savings: "$2,800"
    },
    {
      name: "<PERSON>",
      location: "Mixed Farming, VIC",
      content: "The data insights from NewTerra helped us identify $15,000 in tax savings we didn't know existed. Best investment we've made.",
      rating: 5,
      savings: "$4,200"
    }
  ];

  const stats = [
    {
      icon: Users,
      value: "500+",
      label: "Australian Farms Served",
      color: "text-green-600"
    },
    {
      icon: TrendingUp,
      value: "$3,200",
      label: "Average Annual Savings",
      color: "text-blue-600"
    },
    {
      icon: Shield,
      value: "100%",
      label: "Compliance Success Rate",
      color: "text-purple-600"
    },
    {
      icon: Star,
      value: "4.9/5",
      label: "Client Satisfaction",
      color: "text-orange-600"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-green-50 to-emerald-50">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Join Hundreds of{" "}
            <span className="bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
              Australian Farms
            </span>{" "}
            Already Saving with NewTerra
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Real farmers, real savings, real results. See what our clients are saying about their transformation.
          </p>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div 
              key={index}
              className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className={`flex items-center justify-center w-12 h-12 bg-gray-100 rounded-xl mx-auto mb-4`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
              <div className={`text-3xl font-bold ${stat.color} mb-2`}>
                {stat.value}
              </div>
              <div className="text-gray-600 text-sm">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-600 leading-relaxed mb-6 italic">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.location}</p>
                </div>
                <div className="bg-green-100 px-3 py-1 rounded-full">
                  <p className="text-green-700 font-semibold text-sm">
                    Saved {testimonial.savings}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Badges */}
        <div className="bg-white rounded-2xl p-8 shadow-lg animate-fade-in">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Trusted by Australian Agriculture
            </h3>
            <p className="text-gray-600">
              Licensed, insured, and compliant with all Australian business standards
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div className="flex items-center justify-center space-x-2">
              <Shield className="w-6 h-6 text-green-600" />
              <span className="font-semibold">Australian Business License</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <Shield className="w-6 h-6 text-blue-600" />
              <span className="font-semibold">Professional Indemnity Insured</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <Shield className="w-6 h-6 text-purple-600" />
              <span className="font-semibold">ACCC Compliant</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialProof;
