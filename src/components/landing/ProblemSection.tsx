import { AlertTriangle, DollarSign, FileText, BarChart3 } from "lucide-react";

const ProblemSection = () => {
  const problems = [
    {
      icon: DollarSign,
      title: "Overpaying for Generic Services",
      description: "You're spending $5,000+ annually on accountants who treat your farm like a retail shop. They don't understand seasonal cash flows, livestock depreciation, or agricultural tax strategies—yet charge premium rates for basic services."
    },
    {
      icon: FileText,
      title: "Compliance Stress and Risk Exposure",
      description: "Juggling ATO deadlines, biosecurity requirements, WorkCover obligations, and industry regulations across multiple providers. One missed filing could cost thousands in penalties while keeping you awake at night."
    },
    {
      icon: BarChart3,
      title: "No Time for Strategic Farm Management",
      description: "Drowning in receipt scanning, invoice processing, and regulatory paperwork when you should be optimizing operations, managing livestock, or planning next season's strategy."
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-red-50 to-orange-50">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16 animate-fade-in">
          <div className="flex items-center justify-center mb-6">
            <AlertTriangle className="w-12 h-12 text-red-500" />
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Farm Business Administration Shouldn't Drain Your Profits
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {problems.map((problem, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-100 to-orange-100 rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                <problem.icon className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {problem.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {problem.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProblemSection;
