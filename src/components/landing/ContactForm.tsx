
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { MapPin, Phone, Mail, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    location: "",
    farmType: "",
    revenue: "",
    currentCosts: "",
    challenge: "",
    source: "",
    comments: ""
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    toast({
      title: "Assessment Request Submitted!",
      description: "We'll contact you within 24 hours to schedule your free farm assessment.",
    });
    // Reset form
    setFormData({
      fullName: "",
      email: "",
      phone: "",
      location: "",
      farmType: "",
      revenue: "",
      currentCosts: "",
      challenge: "",
      source: "",
      comments: ""
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <section id="contact" className="py-20 px-4 bg-gradient-to-br from-green-50 to-emerald-50">
      <div className="container mx-auto max-w-6xl">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left Column - Form Header & Contact Info */}
          <div className="animate-fade-in">
            <div className="mb-8">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Ready to Transform Your{" "}
                <span className="bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
                  Farm Business?
                </span>
              </h2>
              <p className="text-xl text-gray-600 mb-6">
                Get your free assessment and savings calculation. We'll show you exactly how much you can save and improve your farm's operations.
              </p>
              
              {/* Trust Signals */}
              <div className="space-y-3 mb-8">
                <div className="flex items-center space-x-3 text-green-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>No spam, ever</span>
                </div>
                <div className="flex items-center space-x-3 text-green-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Free consultation included</span>
                </div>
                <div className="flex items-center space-x-3 text-green-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Response within 24 hours</span>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Phone</p>
                    <p className="text-gray-600">1800-NEW-FARM</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Email</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Location</p>
                    <p className="text-gray-600">Serving all of Australia</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Business Hours</p>
                    <p className="text-gray-600">Mon-Fri: 8AM-6PM AEST</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="animate-fade-in" style={{ animationDelay: "200ms" }}>
            <form onSubmit={handleSubmit} className="bg-white rounded-2xl p-8 shadow-lg space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fullName">Full Name *</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => handleInputChange("fullName", e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="location">Farm Location (State/Region) *</Label>
                  <Input
                    id="location"
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange("location", e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="farmType">Primary Farm Type *</Label>
                <Select onValueChange={(value) => handleInputChange("farmType", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your farm type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wheat-grain">Wheat/Grain</SelectItem>
                    <SelectItem value="cattle">Cattle</SelectItem>
                    <SelectItem value="sheep">Sheep</SelectItem>
                    <SelectItem value="mixed-cropping">Mixed Cropping</SelectItem>
                    <SelectItem value="dairy">Dairy</SelectItem>
                    <SelectItem value="horticulture">Horticulture</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="revenue">Current Annual Revenue Range *</Label>
                <Select onValueChange={(value) => handleInputChange("revenue", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select revenue range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="under-100k">Under $100k</SelectItem>
                    <SelectItem value="100k-500k">$100k - $500k</SelectItem>
                    <SelectItem value="500k-1m">$500k - $1M</SelectItem>
                    <SelectItem value="1m-5m">$1M - $5M</SelectItem>
                    <SelectItem value="5m-plus">$5M+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="currentCosts">Current Accounting/Legal Annual Costs (Optional)</Label>
                <Input
                  id="currentCosts"
                  type="text"
                  value={formData.currentCosts}
                  onChange={(e) => handleInputChange("currentCosts", e.target.value)}
                  placeholder="Help us calculate your potential savings"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="challenge">Biggest Business Challenge *</Label>
                <Select onValueChange={(value) => handleInputChange("challenge", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your biggest challenge" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high-costs">High professional service costs</SelectItem>
                    <SelectItem value="compliance">Compliance management</SelectItem>
                    <SelectItem value="digital-organization">Poor digital organization</SelectItem>
                    <SelectItem value="time-management">Time management</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="source">How did you hear about us? (Optional)</Label>
                <Input
                  id="source"
                  type="text"
                  value={formData.source}
                  onChange={(e) => handleInputChange("source", e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="comments">Additional Comments (Optional)</Label>
                <Textarea
                  id="comments"
                  value={formData.comments}
                  onChange={(e) => handleInputChange("comments", e.target.value)}
                  className="mt-1"
                  rows={4}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full bg-gradient-to-r from-green-600 to-emerald-500 hover:from-green-700 hover:to-emerald-600 text-white py-6 text-lg rounded-xl transition-all duration-300 hover:scale-105"
              >
                Get My Free Assessment
              </Button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
