import { DollarSign, Layers, Zap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const SolutionSection = () => {
  const solutions = [
    {
      icon: DollarSign,
      title: "Specialized Agricultural Bookkeeping",
      description: "Complete Xero integration and management. Monthly financial statements with agricultural insights. Accounts payable/receivable management. Expense categorization for optimal tax positioning.",
      highlight: "Typical savings: $1,800-$2,400 annually",
      bgColor: "from-green-100 to-emerald-100",
      iconColor: "text-green-600"
    },
    {
      icon: Layers,
      title: "Automated Compliance Management",
      description: "All regulatory deadline tracking and filing. ATO, biosecurity, WorkCover, and council requirements. Document management and storage systems. Penalty-free guarantee with proactive notifications.",
      highlight: "Risk elimination value: Priceless",
      bgColor: "from-blue-100 to-cyan-100",
      iconColor: "text-blue-600"
    },
    {
      icon: Zap,
      title: "Administrative Outsourcing",
      description: "Receipt scanning and processing automation. Invoice management and payment scheduling. Contractor documentation and compliance. Insurance and registration renewals.",
      highlight: "Time savings: 8-12 hours monthly",
      bgColor: "from-purple-100 to-indigo-100",
      iconColor: "text-purple-600"
    }
  ];

  return (
    <section id="services" className="py-20 px-4 bg-gradient-to-br from-white to-green-50">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Professional Farm Business Services at{" "}
            <span className="bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
              Half the Cost
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Currently offering comprehensive bookkeeping, compliance, and administrative outsourcing with future expansion into complete farm optimization solutions.
          </p>
        </div>

        <div className="space-y-12">
          {solutions.map((solution, index) => (
            <div
              key={index}
              className={`animate-fade-in bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300`}
              style={{ animationDelay: `${index * 300}ms` }}
            >
              <div className={`space-y-6`}>
                <div className={`flex items-center w-16 h-16 bg-gradient-to-br ${solution.bgColor} rounded-xl mb-4`}>
                  <solution.icon className={`w-8 h-8 ${solution.iconColor} mx-auto`} />
                </div>
                <h3 className="text-3xl font-bold text-gray-900">
                  {solution.title}
                </h3>
                <p className="text-lg text-gray-600 leading-relaxed">
                  {solution.description}
                </p>
                <div className={`bg-gradient-to-r ${solution.bgColor} p-4 rounded-xl border-l-4 ${solution.iconColor.replace('text-', 'border-')}`}>
                  <p className="font-semibold text-gray-700">
                    ✨ {solution.highlight}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16 animate-fade-in">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">Future Platform Development:</h3>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            NewTerra is expanding beyond administration to include yield optimization analytics, resource allocation insights, and supply chain management tools—creating Australia's most comprehensive agricultural business platform.
          </p>
        </div>
      </div>
    </section>
  );
};

export default SolutionSection;
