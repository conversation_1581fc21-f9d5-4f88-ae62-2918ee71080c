import React from 'react';
import { Button } from '@/components/ui/button';
import { Leaf } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import UserMenu from '@/components/auth/UserMenu';

const Header = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  return (
    <header className="bg-white/90 backdrop-blur-md border-b border-green-100 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Leaf className="h-8 w-8 text-green-600" />
          <span className="text-2xl font-bold text-gray-900">NewTerra</span>
        </div>

        <nav className="hidden md:flex items-center space-x-8">
          <a href="#features" className="text-gray-600 hover:text-green-600 transition-colors">
            Features
          </a>
          <a href="#how-it-works" className="text-gray-600 hover:text-green-600 transition-colors">
            How It Works
          </a>
          <a href="#pricing" className="text-gray-600 hover:text-green-600 transition-colors">
            Pricing
          </a>
          <a href="#contact" className="text-gray-600 hover:text-green-600 transition-colors">
            Contact
          </a>
        </nav>

        <div className="flex items-center space-x-4">
          {!loading && (
            user ? (
              <UserMenu />
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => navigate('/auth')}
                  className="text-gray-600 hover:text-green-600"
                >
                  Sign In
                </Button>
                <Button
                  onClick={() => navigate('/auth?redirectTo=/onboarding')}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Get Started
                </Button>
              </>
            )
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
