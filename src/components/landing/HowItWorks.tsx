import { FileSearch, <PERSON>R<PERSON>, Setting<PERSON>, BarChart3 } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      icon: FileSearch,
      title: "Farm Business Onboarding (Free)",
      description: "Complete analysis of your current costs and administrative burden.",
      detail: "Delivered within 72 business hours with specific savings projections for your operation"
    },
    {
      icon: Settings,
      title: "Seamless Service Migration",
      description: "Our team handles complete transition from your existing providers.",
      detail: "All historical data preserved, systems integrated, zero operational disruption guaranteed"
    },
    {
      icon: BarChart3,
      title: "Ongoing Professional Management",
      description: "Monthly financial reports, proactive compliance management, and direct access to agricultural business specialists.",
      detail: "Focus on farming while we handle the office"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Three Steps to Administrative{" "}
            <span className="bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
              Freedom
            </span>
          </h2>
        </div>

        <div className="relative">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-green-200 via-green-300 to-green-200 transform -translate-y-1/2 z-0"></div>

          <div className="grid lg:grid-cols-3 gap-8 relative z-10">
            {steps.map((step, index) => (
              <div
                key={index}
                className="group relative animate-fade-in"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {/* Step Number */}
                <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-r from-green-600 to-emerald-500 text-white rounded-full flex items-center justify-center font-bold text-sm z-20">
                  {index + 1}
                </div>

                {/* Card */}
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    <step.icon className="w-8 h-8 text-green-600" />
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {step.title}
                  </h3>

                  <p className="text-gray-600 leading-relaxed mb-4">
                    {step.description}
                  </p>

                  <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-500">
                    <p className="text-green-700 font-semibold text-sm">
                      ✓ {step.detail}
                    </p>
                  </div>
                </div>

                {/* Arrow for desktop */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-20">
                    <ArrowRight className="w-8 h-8 text-green-500" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16 animate-fade-in">
          <div className="bg-gradient-to-r from-green-600 to-emerald-500 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Get Your Free Farm Business Assessment</h3>
            <p className="text-green-100 mb-6 max-w-2xl mx-auto">
              Receive a complete cost analysis with specific savings projections for your farm within 24 hours. Simply complete a 2-minute form to get started.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-300 hover:scale-105 transform">
              Start Your Free Assessment Today
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
