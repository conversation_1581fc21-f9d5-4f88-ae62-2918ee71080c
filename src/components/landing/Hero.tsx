import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, Leaf, Users, Globe } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Hero = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  return (
    <section className="py-20 bg-gradient-to-br from-green-50 via-white to-emerald-50">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Outsource Farm Business Administration,{' '}
            <span className="text-green-600 relative">
              Focus on Farming
              <div className="absolute -bottom-2 left-0 right-0 h-3 bg-green-200 -z-10 rounded"></div>
            </span>
          </h1>

          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            Professional bookkeeping, compliance, and administration services designed specifically for Australian agriculture.
            Save $2,000-$4,000 annually while eliminating administrative headaches. Our agricultural specialists handle your books, compliance, and paperwork—so you can concentrate on profitable farming operations.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            {user ? (
              <Button
                size="lg"
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg"
                onClick={() => navigate('/onboarding')}
              >
                Continue Onboarding
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            ) : (
              <>
                <Button
                  size="lg"
                  className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg"
                  onClick={() => navigate('/login')}
                >
                  Get Your Free Assessment
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-green-600 text-green-600 hover:bg-green-50 px-8 py-4 text-lg"
                >
                  Watch Demo
                </Button>
              </>
            )}
          </div>

          <div className="flex flex-col md:flex-row justify-center items-center gap-8 text-gray-600">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              <span>0+ :D Farms Served</span>
            </div>
            <div className="flex items-center gap-2">
              <Leaf className="h-5 w-5 text-green-600" />
              <span>$3,200? Average Annual Savings</span>
            </div>
            <div className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-green-600" />
              <span>100%!! Compliance Success Rate</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
