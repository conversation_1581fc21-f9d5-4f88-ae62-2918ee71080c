
import { Check, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const PricingSection = () => {
  const plans = [
    {
      name: "Starter Farms",
      price: "299",
      period: "month",
      description: "Perfect for small family farms just getting started",
      features: [
        "Basic bookkeeping with Xero integration",
        "Quarterly tax preparation",
        "Essential compliance tracking",
        "Email support",
        "Farm document storage",
        "Basic financial reporting"
      ],
      popular: false,
      savings: "Save $1,500+ annually"
    },
    {
      name: "Growing Operations",
      price: "599",
      period: "month",
      description: "Comprehensive solution for expanding farm businesses",
      features: [
        "Full bookkeeping and accounting",
        "Monthly tax planning",
        "Complete compliance management",
        "Priority phone & email support",
        "Advanced analytics dashboard",
        "Strategic business planning",
        "Digital migration service",
        "Custom reporting"
      ],
      popular: true,
      savings: "Save $3,200+ annually"
    },
    {
      name: "Large Enterprises",
      price: "Custom",
      period: "pricing",
      description: "Tailored solutions for complex agricultural operations",
      features: [
        "Enterprise-level accounting",
        "Dedicated account manager",
        "Custom compliance workflows",
        "24/7 priority support",
        "Advanced data analytics",
        "Multi-location management",
        "Integration with farm management systems",
        "Quarterly strategy sessions"
      ],
      popular: false,
      savings: "Save $5,000+ annually"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Honest, Transparent{" "}
            <span className="bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
              Pricing
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            No hidden fees, no surprises - just affordable professional services that save you money
          </p>
          <div className="bg-green-100 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto">
            <p className="text-green-800 font-semibold">
              🛡️ 30-day money-back guarantee - if you don't save money in your first month, we'll refund your fee
            </p>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <div 
              key={index}
              className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in ${
                plan.popular ? 'ring-2 ring-green-500 scale-105' : ''
              }`}
              style={{ animationDelay: `${index * 200}ms` }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-green-600 to-emerald-500 text-white px-6 py-2 rounded-full flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-current" />
                    <span className="font-semibold text-sm">Most Popular</span>
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-4">{plan.description}</p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900">
                      {plan.price === "Custom" ? "Custom" : `$${plan.price}`}
                    </span>
                    {plan.price !== "Custom" && (
                      <span className="text-gray-600 ml-2">/{plan.period}</span>
                    )}
                  </div>
                  <div className="mt-2">
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                      {plan.savings}
                    </span>
                  </div>
                </div>

                {/* Features */}
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-3">
                      <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <Button 
                  className={`w-full py-6 text-lg rounded-xl transition-all duration-300 ${
                    plan.popular 
                      ? 'bg-gradient-to-r from-green-600 to-emerald-500 hover:from-green-700 hover:to-emerald-600 text-white'
                      : 'border-2 border-green-200 text-green-700 hover:bg-green-50'
                  }`}
                  variant={plan.popular ? "default" : "outline"}
                >
                  {plan.price === "Custom" ? "Contact Sales" : "Start Free Trial"}
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Information */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 animate-fade-in">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              What's Included in Every Plan
            </h3>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl mx-auto mb-3 flex items-center justify-center">
                  <Check className="w-6 h-6 text-blue-600" />
                </div>
                <p className="font-semibold text-gray-900">No Setup Fees</p>
                <p className="text-gray-600 text-sm">Get started immediately</p>
              </div>
              <div>
                <div className="w-12 h-12 bg-green-100 rounded-xl mx-auto mb-3 flex items-center justify-center">
                  <Check className="w-6 h-6 text-green-600" />
                </div>
                <p className="font-semibold text-gray-900">Free Migration</p>
                <p className="text-gray-600 text-sm">We handle the transition</p>
              </div>
              <div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl mx-auto mb-3 flex items-center justify-center">
                  <Check className="w-6 h-6 text-purple-600" />
                </div>
                <p className="font-semibold text-gray-900">Cancel Anytime</p>
                <p className="text-gray-600 text-sm">No long-term contracts</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
