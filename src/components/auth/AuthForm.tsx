import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Eye, EyeOff, Mail, Lock, User, Phone } from 'lucide-react';

interface AuthFormProps {
  mode: 'login' | 'register' | 'forgot-password';
  onModeChange: (mode: 'login' | 'register' | 'forgot-password') => void;
  onAuthSuccess?: () => void;
}

const AuthForm: React.FC<AuthFormProps> = ({ mode, onModeChange, onAuthSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [username, setUsername] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [receiveTextMessages, setReceiveTextMessages] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const { signIn, signUp, resetPassword } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (mode === 'login') {
        const { error } = await signIn(email, password);
        if (error) {
          toast.error(error.message);
        } else {
          toast.success('Welcome back!');
          if (onAuthSuccess) onAuthSuccess();
        }
      } else if (mode === 'register') {
        if (!fullName.trim()) {
          toast.error('Please enter your full name');
          return;
        }
        if (!username.trim()) {
          toast.error('Please enter a username');
          return;
        }

        const { error } = await signUp(email, password, {
          full_name: fullName,
          username: username,
          phone_number: phoneNumber,
          receive_text_messages: receiveTextMessages
        });

        if (error) {
          toast.error(error.message);
        } else {
          toast.success('Account created! Please check your email to verify your account.');
          if (onAuthSuccess) onAuthSuccess();
        }
      } else if (mode === 'forgot-password') {
        const { error } = await resetPassword(email);
        if (error) {
          toast.error(error.message);
        } else {
          toast.success('Password reset email sent!');
        }
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'login': return 'Welcome Back';
      case 'register': return 'Create Account';
      case 'forgot-password': return 'Reset Password';
    }
  };

  const getButtonText = () => {
    switch (mode) {
      case 'login': return 'Sign In';
      case 'register': return 'Create Account';
      case 'forgot-password': return 'Send Reset Email';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900">{getTitle()}</h1>
        <p className="text-gray-600 mt-2">
          {mode === 'login' && 'Sign in to your NewTerra account'}
          {mode === 'register' && 'Join NewTerra and start your journey'}
          {mode === 'forgot-password' && 'Enter your email to reset your password'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {mode === 'register' && (
          <>
            <div className="space-y-2">
              <Label htmlFor="fullName" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Full Name *
              </Label>
              <Input
                id="fullName"
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Enter your full name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="username" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Username *
              </Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Choose a username"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phoneNumber" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Phone Number
              </Label>
              <Input
                id="phoneNumber"
                type="tel"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="Enter your phone number"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="receiveTextMessages"
                checked={receiveTextMessages}
                onCheckedChange={(checked) => setReceiveTextMessages(checked === true)}
              />
              <Label htmlFor="receiveTextMessages" className="text-sm">
                I want to receive text message updates
              </Label>
            </div>
          </>
        )}

        <div className="space-y-2">
          <Label htmlFor="email" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            required
          />
        </div>

        {mode !== 'forgot-password' && (
          <div className="space-y-2">
            <Label htmlFor="password" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        )}

        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? 'Loading...' : getButtonText()}
        </Button>
      </form>

      <div className="text-center space-y-2">
        {mode === 'login' && (
          <>
            <button
              type="button"
              onClick={() => onModeChange('forgot-password')}
              className="text-sm text-green-600 hover:text-green-500"
            >
              Forgot your password?
            </button>
            <div className="text-sm text-gray-600">
              Don't have an account?{' '}
              <button
                type="button"
                onClick={() => onModeChange('register')}
                className="text-green-600 hover:text-green-500 font-medium"
              >
                Sign up
              </button>
            </div>
          </>
        )}

        {mode === 'register' && (
          <div className="text-sm text-gray-600">
            Already have an account?{' '}
            <button
              type="button"
              onClick={() => onModeChange('login')}
              className="text-green-600 hover:text-green-500 font-medium"
            >
              Sign in
            </button>
          </div>
        )}

        {mode === 'forgot-password' && (
          <button
            type="button"
            onClick={() => onModeChange('login')}
            className="text-sm text-green-600 hover:text-green-500"
          >
            Back to sign in
          </button>
        )}
      </div>
    </div>
  );
};

export default AuthForm;
