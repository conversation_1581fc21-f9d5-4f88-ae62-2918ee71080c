import React from 'react';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import AuthForm from './AuthForm';

interface AuthModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAuthSuccess: () => void;
}

export const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, onAuthSuccess }) => {
    const [mode, setMode] = React.useState<'login' | 'register' | 'forgot-password'>('login');

    const handleAuthSuccess = () => {
        onAuthSuccess();
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>{mode === 'login' ? 'Sign In' : 'Sign Up'}</DialogTitle>
                </DialogHeader>
                <AuthForm mode={mode} onModeChange={setMode} onAuthSuccess={handleAuthSuccess} />
            </DialogContent>
        </Dialog>
    );
}; 