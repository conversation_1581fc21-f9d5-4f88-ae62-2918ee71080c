import React from 'react';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface StepLoadingStateProps {
  stepName: string;
  message?: string;
}

export const StepLoadingState: React.FC<StepLoadingStateProps> = ({ 
  stepName, 
  message = "Loading step essentials..." 
}) => (
  <div className="flex items-center justify-center space-x-2 py-8" role="status" aria-live="polite">
    <Loader2 className="h-6 w-6 animate-spin" aria-hidden="true" />
    <p>
      <span className="sr-only">Loading {stepName}: </span>
      {message}
    </p>
  </div>
);

interface StepErrorStateProps {
  stepName: string;
  error: string;
  onRetry: () => void;
  isRetrying?: boolean;
  showRefresh?: boolean;
}

export const StepErrorState: React.FC<StepErrorStateProps> = ({
  stepName,
  error,
  onRetry,
  isRetrying = false,
  showRefresh = true,
}) => (
  <div className="text-destructive py-8 space-y-4" role="alert" aria-live="assertive">
    <div className="flex items-center space-x-2">
      <AlertCircle className="h-5 w-5" aria-hidden="true" />
      <p className="font-medium">Error initializing {stepName}:</p>
    </div>
    <p className="text-sm">{error}</p>
    <div className="flex gap-2">
      <Button
        variant="outline"
        onClick={onRetry}
        disabled={isRetrying}
        aria-label={isRetrying ? `Retrying ${stepName} initialization...` : `Retry ${stepName} initialization`}
      >
        {isRetrying && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
        Retry
      </Button>
      {showRefresh && (
        <Button
          variant="ghost"
          onClick={() => window.location.reload()}
          aria-label="Refresh the entire page"
        >
          Refresh Page
        </Button>
      )}
    </div>
  </div>
);

interface StepInitializationWarningProps {
  stepName: string;
  message?: string;
}

export const StepInitializationWarning: React.FC<StepInitializationWarningProps> = ({
  stepName,
  message = "is waiting for initialization. If this persists, please refresh.",
}) => (
  <p className="text-amber-600 p-4 border border-amber-300 rounded-md" role="status" aria-live="polite">
    {stepName} {message}
  </p>
);

interface DataSectionPlaceholderProps {
  title: string;
  description?: string;
  isLoading?: boolean;
}

export const DataSectionPlaceholder: React.FC<DataSectionPlaceholderProps> = ({
  title,
  description = "Loading data for review...",
  isLoading = true,
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center space-x-2">
        {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
        <span>{title}</span>
      </CardTitle>
    </CardHeader>
    <CardContent>
      <p className="text-sm text-muted-foreground">{description}</p>
    </CardContent>
  </Card>
);

interface EntityLoadingStateProps {
  entityName: string;
  action: 'adding' | 'deleting' | 'updating';
}

export const EntityLoadingState: React.FC<EntityLoadingStateProps> = ({
  entityName,
  action,
}) => (
  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
    <Loader2 className="h-4 w-4 animate-spin" />
    <span>
      {action === 'adding' && `Adding ${entityName}...`}
      {action === 'deleting' && `Deleting ${entityName}...`}
      {action === 'updating' && `Updating ${entityName}...`}
    </span>
  </div>
);

interface SuccessIndicatorProps {
  message: string;
  showIcon?: boolean;
}

export const SuccessIndicator: React.FC<SuccessIndicatorProps> = ({
  message,
  showIcon = true,
}) => (
  <div className="flex items-center space-x-2 text-green-600">
    {showIcon && <CheckCircle className="h-4 w-4" />}
    <span className="text-sm">{message}</span>
  </div>
);

interface FieldErrorDisplayProps {
  error: string;
  className?: string;
}

export const FieldErrorDisplay: React.FC<FieldErrorDisplayProps> = ({
  error,
  className = "",
}) => (
  <p className={`text-sm text-destructive ${className}`}>
    {error}
  </p>
);

interface SavingIndicatorProps {
  fieldName?: string;
  size?: 'sm' | 'md';
}

export const SavingIndicator: React.FC<SavingIndicatorProps> = ({
  fieldName,
  size = 'sm'
}) => {
  const iconSize = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4';
  const textSize = size === 'sm' ? 'text-xs' : 'text-sm';
  
  return (
    <div className={`flex items-center space-x-1 text-muted-foreground ${textSize}`}>
      <Loader2 className={`${iconSize} animate-spin`} />
      <span>
        {fieldName ? `Saving ${fieldName}...` : 'Saving...'}
      </span>
    </div>
  );
};