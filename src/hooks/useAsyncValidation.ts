import { useState, useEffect, useCallback, useRef } from 'react';
import { useDebounce } from './useDebounce';

// Define the possible states for async validation
type ValidationStatus = 'idle' | 'validating' | 'valid' | 'invalid' | 'error';

interface ValidationState {
    status: ValidationStatus;
    message: string | null;
}

interface ValidationError {
    context?: {
        status?: number;
    };
    message?: string;
}

interface ValidationOptions {
    debounceDelay?: number;
    invalidMessage?: string;
    notFoundMessage?: string;
    serviceUnavailableMessage?: string;
    generalErrorMessage?: string;
}

// Type guard for validation errors
function isValidationError(error: unknown): error is ValidationError {
    return typeof error === 'object' && error !== null && 'context' in error;
}

/**
 * A custom hook to manage the state and execution of an asynchronous validation process.
 * It uses a debounced input value to trigger a validation function.
 */
export function useAsyncValidation(
    value: string,
    validationFn: (value: string) => Promise<{ isValid: boolean; businessName?: string }>,
    options: ValidationOptions = {}
): ValidationState {
    const {
        debounceDelay = 500,
        invalidMessage = 'This value is not valid.',
        notFoundMessage = 'This value is not registered.',
        serviceUnavailableMessage = 'Cannot validate now. Will retry.',
        generalErrorMessage = 'An unexpected error occurred.',
    } = options;

    const [validationState, setValidationState] = useState<ValidationState>({
        status: 'idle',
        message: null,
    });
    
    const debouncedValue = useDebounce(value, debounceDelay);
    const isMountedRef = useRef(true);
    
    // Stabilize validation function reference
    const stableValidationFn = useCallback(validationFn, []);

    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    useEffect(() => {
        if (!debouncedValue) {
            if (isMountedRef.current) {
                setValidationState({ status: 'idle', message: null });
            }
            return;
        }

        const handleValidation = async () => {
            if (!isMountedRef.current) return;
            
            setValidationState({ status: 'validating', message: 'Validating...' });
            
            try {
                const result = await stableValidationFn(debouncedValue);
                
                if (!isMountedRef.current) return;
                
                if (result.isValid) {
                    setValidationState({ 
                        status: 'valid', 
                        message: result.businessName || 'Valid' 
                    });
                } else {
                    setValidationState({ 
                        status: 'invalid', 
                        message: invalidMessage 
                    });
                }
            } catch (error: unknown) {
                if (!isMountedRef.current) return;
                
                if (isValidationError(error)) {
                    const status = error.context?.status;
                    
                    if (status === 404) {
                        setValidationState({ 
                            status: 'invalid', 
                            message: notFoundMessage 
                        });
                    } else if (status === 503) {
                        setValidationState({ 
                            status: 'error', 
                            message: serviceUnavailableMessage 
                        });
                    } else {
                        setValidationState({ 
                            status: 'error', 
                            message: error.message || generalErrorMessage 
                        });
                    }
                } else {
                    setValidationState({ 
                        status: 'error', 
                        message: generalErrorMessage 
                    });
                }
            }
        };

        handleValidation();

    }, [debouncedValue, stableValidationFn, invalidMessage, notFoundMessage, serviceUnavailableMessage, generalErrorMessage]);

    return validationState;
} 