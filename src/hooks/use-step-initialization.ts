import { useState, useEffect, useRef, useCallback } from 'react';
import { logger } from '@/utils/logger';

/**
 * Configuration options for step initialization
 */
export interface UseStepInitializationOptions<T> {
    /** Session ID for the onboarding session */
    sessionId: string | null;
    /** Whether the context is currently loading */
    contextLoading: boolean;
    /** Existing data from session (if any) */
    existingData: T | null;
    /** Function to initialize/ensure the step record exists */
    initializeFn: (sessionId: string) => Promise<T>;
    /** Maximum number of retry attempts (default: 3) */
    maxRetries?: number;
    /** Timeout in milliseconds (default: 30000) */
    timeoutMs?: number;
    /** Step name for logging purposes */
    stepName?: string;
}

/**
 * Return type for the useStepInitialization hook
 */
export interface UseStepInitializationReturn<T> {
    /** Whether the step is currently initializing */
    isInitializing: boolean;
    /** Any error that occurred during initialization */
    error: string | null;
    /** Number of retry attempts made */
    retryCount: number;
    /** Whether the step is ready for use */
    isReady: boolean;
    /** Function to manually retry initialization */
    retry: () => void;
    /** The initialized data (if successful) */
    data: T | null;
}

/**
 * Custom hook for managing step initialization patterns
 * Handles loading states, error handling, retry logic, and memory management
 */
export function useStepInitialization<T>({
    sessionId,
    contextLoading,
    existingData,
    initializeFn,
    maxRetries = 3,
    timeoutMs = 30000,
    stepName = 'Unknown Step'
}: UseStepInitializationOptions<T>): UseStepInitializationReturn<T> {
    const [isInitializing, setIsInitializing] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [retryCount, setRetryCount] = useState(0);
    const [data, setData] = useState<T | null>(existingData);

    // Refs for memory management and cleanup
    const isMountedRef = useRef(true);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const initializationPromiseRef = useRef<Promise<void> | null>(null);

    // Cleanup function
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    // Update data when existingData changes
    useEffect(() => {
        if (existingData) {
            setData(existingData);
            setIsInitializing(false);
            setError(null);
        }
    }, [existingData]);

    const initialize = useCallback(async () => {
        // Prevent multiple simultaneous initialization attempts
        if (initializationPromiseRef.current) {
            return initializationPromiseRef.current;
        }

        // Early return conditions
        if (!isMountedRef.current) return;

        if (!sessionId) {
            if (!contextLoading) {
                setError("Session not available. Please refresh the page.");
                setIsInitializing(false);
            }
            return;
        }

        // If we already have data, mark as ready
        if (existingData) {
            setIsInitializing(false);
            setError(null);
            setData(existingData);
            return;
        }

        // Start initialization
        setIsInitializing(true);
        setError(null);

        const initPromise = (async () => {
            try {
                logger.info(`${stepName}: Starting initialization`, {
                    sessionId,
                    retryCount,
                    hasExistingData: !!existingData,
                });

                // Create timeout promise
                const timeoutPromise = new Promise<never>((_, reject) => {
                    timeoutRef.current = setTimeout(() => {
                        reject(new Error(`Initialization timeout after ${timeoutMs}ms`));
                    }, timeoutMs);
                });

                // Race between initialization and timeout
                const result = await Promise.race([
                    initializeFn(sessionId),
                    timeoutPromise
                ]);

                // Clear timeout on success
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                    timeoutRef.current = null;
                }

                if (!isMountedRef.current) return;

                if (!result) {
                    throw new Error(`Failed to initialize ${stepName} - no data returned`);
                }

                setData(result);
                setIsInitializing(false);
                setError(null);
                setRetryCount(0); // Reset retry count on success

                logger.info(`${stepName}: Initialization completed successfully`, {
                    sessionId,
                    dataReceived: !!result,
                });

            } catch (error: unknown) {
                if (!isMountedRef.current) return;

                const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';

                logger.error(`${stepName}: Initialization failed`, {
                    error: errorMessage,
                    sessionId,
                    retryCount,
                    operation: 'step_initialization',
                });

                // Determine if we should retry
                const shouldRetry = retryCount < maxRetries && (
                    errorMessage.includes('timeout') ||
                    errorMessage.includes('network') ||
                    errorMessage.includes('fetch') ||
                    errorMessage.includes('502') ||
                    errorMessage.includes('503') ||
                    errorMessage.includes('504')
                );

                if (shouldRetry) {
                    const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
                    logger.info(`${stepName}: Retrying initialization in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);

                    setTimeout(() => {
                        if (!isMountedRef.current) return;
                        setRetryCount(prev => prev + 1);
                        initializationPromiseRef.current = null; // Allow new attempt
                        initialize();
                    }, delay);
                } else {
                    setError(`Initialization failed: ${errorMessage}`);
                    setIsInitializing(false);
                }
            } finally {
                initializationPromiseRef.current = null;

                // Clear timeout if still active
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                    timeoutRef.current = null;
                }
            }
        })();

        initializationPromiseRef.current = initPromise;
        return initPromise;
    }, [
        sessionId,
        contextLoading,
        existingData,
        initializeFn,
        maxRetries,
        timeoutMs,
        stepName,
        retryCount
    ]);

    // Initialize when dependencies change
    useEffect(() => {
        if (!contextLoading && !existingData && !error) {
            initialize();
        }
    }, [initialize, contextLoading, existingData, error]);

    const retry = useCallback(() => {
        setError(null);
        setRetryCount(0);
        initializationPromiseRef.current = null;
        initialize();
    }, [initialize]);

    const isReady = !isInitializing && !error && !!data;

    return {
        isInitializing,
        error,
        retryCount,
        isReady,
        retry,
        data,
    };
} 