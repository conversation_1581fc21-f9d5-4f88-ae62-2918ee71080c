/**
 * Hooks Index
 * Centralized exports for all custom hooks
 */

// Consolidated hooks - New unified management hooks (Recommended)
export {
    useFormManagement,
    useFieldManagement,
    type UseFormManagementOptions,
    type UseFormManagementReturn,
    type UseFieldManagementOptions,
    type UseFieldManagementReturn,
} from './use-form-management';

export {
    useEntityListManagement,
    type UseEntityListManagementOptions,
    type UseEntityListManagementReturn,
} from './use-entity-list-management';

// Utility hooks (Still needed)
export { useAsyncValidation } from './useAsyncValidation';
export { useDebounce } from './useDebounce';

// Validation hooks
export {
    useFormValidation,
    useFormValidationWithChange,
    type UseFormValidationOptions,
    type UseFormValidationReturn,
    type ValidationSchema,
} from './use-form-validation';

// Step initialization hooks
export {
    useStepInitialization,
    type UseStepInitializationOptions,
    type UseStepInitializationReturn,
} from './use-step-initialization';

export {
    useEnhancedStepInit,
    type UseEnhancedStepInitProps,
    type UseEnhancedStepInitReturn,
} from './use-enhanced-step-init';

// UI/Mobile hooks
export { useToast, toast } from './use-toast';
export { useIsMobile } from './use-mobile';

// Legacy hooks - Deprecated (Use consolidated versions above)
// IMPORTANT: These hooks will be removed in a future update
// Please migrate to the consolidated hooks listed above

// Still in use but deprecated - migrate to useEntityListManagement
export {
    useDynamicEntityList,
    type UseDynamicEntityListOptions,
    type UseDynamicEntityListReturn,
} from './use-dynamic-entity-list';

// Still in use but deprecated - migrate to useFieldManagement  
export {
    useAutoSaveFormField,
    type UseAutoSaveFormFieldOptions,
    type UseAutoSaveFormFieldReturn,
} from './use-auto-save-form-field'; 