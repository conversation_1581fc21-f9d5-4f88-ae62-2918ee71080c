/**
 * Hook for selective data refresh in the onboarding system
 * Optimizes performance by only refreshing affected data instead of entire session
 */

import { useCallback, useRef } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { TableName } from '@/contexts/OnboardingContext';
import { logger } from '@/utils/logger';

interface RefreshOptions {
  /**
   * Tables that were modified and need refreshing
   */
  tables: TableName[];
  /**
   * Whether to force a full refresh regardless
   */
  forceFullRefresh?: boolean;
  /**
   * Minimum time between refreshes (in ms) to prevent rapid calls
   */
  debounceMs?: number;
}

export function useSelectiveRefresh() {
  const { refreshSessionData } = useOnboarding();
  const lastRefreshTime = useRef<number>(0);
  const pendingRefresh = useRef<NodeJS.Timeout | null>(null);

  /**
   * Performs a selective refresh of session data
   * In the current implementation, this still calls the full refresh
   * but provides a foundation for future optimization
   */
  const selectiveRefresh = useCallback(
    async (options: RefreshOptions) => {
      const { tables, forceFullRefresh = false, debounceMs = 500 } = options;

      // Clear any pending refresh
      if (pendingRefresh.current) {
        clearTimeout(pendingRefresh.current);
        pendingRefresh.current = null;
      }

      // Check if we should debounce
      const now = Date.now();
      const timeSinceLastRefresh = now - lastRefreshTime.current;

      if (!forceFullRefresh && timeSinceLastRefresh < debounceMs) {
        // Schedule a refresh after the debounce period
        return new Promise<void>((resolve) => {
          pendingRefresh.current = setTimeout(async () => {
            lastRefreshTime.current = Date.now();
            logger.info('Performing debounced selective refresh', { tables });
            await refreshSessionData(false); // Don't show toast for automatic refreshes
            resolve();
          }, debounceMs - timeSinceLastRefresh);
        });
      }

      // Perform immediate refresh
      lastRefreshTime.current = now;
      logger.info('Performing selective refresh', { 
        tables, 
        forceFullRefresh,
        timeSinceLastRefresh 
      });

      // TODO: In the future, this could be optimized to only fetch specific tables
      // For now, we still do a full refresh but with better debouncing
      await refreshSessionData(false);
    },
    [refreshSessionData]
  );

  /**
   * Batches multiple table refreshes into a single operation
   */
  const batchRefresh = useCallback(
    (tables: TableName[]) => {
      return selectiveRefresh({ tables, debounceMs: 1000 });
    },
    [selectiveRefresh]
  );

  /**
   * Forces an immediate full refresh
   */
  const forceRefresh = useCallback(
    () => {
      return selectiveRefresh({ tables: [], forceFullRefresh: true, debounceMs: 0 });
    },
    [selectiveRefresh]
  );

  return {
    selectiveRefresh,
    batchRefresh,
    forceRefresh,
  };
}