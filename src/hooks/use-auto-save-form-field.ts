import { useFieldManagement } from './use-form-management';
import type { UseFieldManagementOptions, UseFieldManagementReturn } from './use-form-management';

/**
 * @deprecated Use useFieldManagement from ./use-form-management instead
 * This is a compatibility wrapper that will be removed in a future version
 */
export type UseAutoSaveFormFieldOptions = UseFieldManagementOptions;

/**
 * @deprecated Use useFieldManagement from ./use-form-management instead  
 * This is a compatibility wrapper that will be removed in a future version
 */
export type UseAutoSaveFormFieldReturn = UseFieldManagementReturn;

/**
 * @deprecated Use useFieldManagement from ./use-form-management instead
 * This is a compatibility wrapper that will be removed in a future version
 */
export function useAutoSaveFormField(options: UseAutoSaveFormFieldOptions): UseAutoSaveFormFieldReturn {
    console.warn('useAutoSaveFormField is deprecated. Please migrate to useFieldManagement from @/hooks/use-form-management');
    return useFieldManagement(options);
}