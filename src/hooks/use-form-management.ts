import { useState, useCallback, useRef, useEffect } from 'react';
import { useDebounce } from './useDebounce';
import { useOnboarding, type TableName } from '@/contexts/OnboardingContext';
import { useSelectiveRefresh } from '@/hooks/use-selective-refresh';
import { validateFormField, ValidationConfig } from '@/utils/form-validation';
import { logger } from '@/utils/logger';
import { Database } from '@/types/database.types';

/**
 * Configuration options for form field management
 */
export interface UseFormManagementOptions<T> {
    /** Step ID for database operations */
    stepId: string | null;
    /** Database table name */
    tableName: TableName;
    /** Initial form data */
    initialData?: T | null;
    /** Field validation function */
    validateField?: (field: keyof T, value: unknown) => string | null;
    /** Debounce delay in milliseconds (default: 1000) */
    debounceMs?: number;
    /** Whether the form is disabled */
    disabled?: boolean;
    /** Display name for logging */
    displayName?: string;
}

/**
 * Configuration for individual field management
 */
export interface UseFieldManagementOptions {
    /** Unique identifier for the entity being updated */
    entityId: string;
    /** Database table name */
    tableName: string;
    /** Field name in the database */
    fieldName: string;
    /** Initial value for the field */
    initialValue: unknown;
    /** Function to update the record in the database */
    updateFn: (tableName: string, id: string, data: Record<string, unknown>) => Promise<void>;
    /** Debounce delay in milliseconds (default: 1000) */
    debounceMs?: number;
    /** Validation rules for the field */
    validationRules?: ValidationConfig[];
    /** Custom formatter function */
    formatter?: (value: unknown) => unknown;
    /** Whether the field is disabled */
    disabled?: boolean;
    /** Field display name for logging */
    fieldDisplayName?: string;
}

/**
 * Return type for form management
 */
export interface UseFormManagementReturn<T> {
    /** Current form data */
    formData: T;
    /** Field-specific errors */
    fieldErrors: Partial<Record<keyof T, string>>;
    /** Field-specific saving states */
    isFieldSaving: Partial<Record<keyof T, boolean>>;
    /** Handle field value changes */
    handleFieldChange: (field: keyof T, value: unknown) => void;
    /** Handle field blur events */
    handleFieldBlur: (field: keyof T) => void;
    /** Validate all fields */
    validateAllFields: () => boolean;
    /** Reset form to initial or provided data */
    resetForm: (newData?: T) => void;
    /** Whether the form has unsaved changes */
    isDirty: boolean;
    /** Whether any field is currently saving */
    isSaving: boolean;
}

/**
 * Return type for individual field management
 */
export interface UseFieldManagementReturn {
    /** Current field value */
    value: unknown;
    /** Function to handle input changes */
    onChange: (value: unknown) => void;
    /** Function to handle blur events (immediate save) */
    onBlur: () => void;
    /** Current validation error (if any) */
    error: string | null;
    /** Whether the field is currently being saved */
    isSaving: boolean;
    /** Whether the field has unsaved changes */
    hasUnsavedChanges: boolean;
    /** Function to manually trigger save */
    save: () => Promise<void>;
    /** Function to reset the field to its initial value */
    reset: () => void;
}

/**
 * Comprehensive form management hook that handles multiple fields with auto-save, validation, and state management.
 * Consolidates functionality from multiple form management patterns.
 */
export function useFormManagement<T extends Record<string, unknown>>({
    stepId,
    tableName,
    initialData,
    validateField,
    debounceMs = 800,
    disabled = false,
    displayName = 'form'
}: UseFormManagementOptions<T>): UseFormManagementReturn<T> {
    const { updateRecord } = useOnboarding();
    const { selectiveRefresh } = useSelectiveRefresh();

    const [formData, setFormData] = useState<T>(() => initialData || ({} as T));
    const [fieldErrors, setFieldErrors] = useState<Partial<Record<keyof T, string>>>({});
    const [isFieldSaving, setIsFieldSaving] = useState<Partial<Record<keyof T, boolean>>>({});
    const [isDirty, setIsDirty] = useState(false);

    // Track original data for dirty state comparison
    const originalDataRef = useRef<T>(initialData || ({} as T));
    const isMountedRef = useRef(true);

    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    // Update form data when initialData changes
    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
            originalDataRef.current = initialData;
            setIsDirty(false);
        }
    }, [initialData]);

    // Debounced save function
    const debouncedSave = useDebounce(
        useCallback(async (field: keyof T, value: unknown) => {
            if (!stepId || disabled || !isMountedRef.current) return;

            setIsFieldSaving(prev => ({ ...prev, [field]: true }));

            try {
                await updateRecord(tableName, stepId, { [field]: value });

                // Selective refresh for the affected table
                await selectiveRefresh({
                    tables: [tableName],
                    debounceMs: 1000
                });

                if (isMountedRef.current) {
                    // Clear field error on successful save
                    setFieldErrors(prev => {
                        const newErrors = { ...prev };
                        delete newErrors[field];
                        return newErrors;
                    });

                    logger.info(`Field ${String(field)} saved successfully`, {
                        tableName,
                        stepId,
                        field: String(field),
                        displayName
                    });
                }
            } catch (error) {
                if (isMountedRef.current) {
                    const errorMessage = error instanceof Error ? error.message : 'Save failed';
                    setFieldErrors(prev => ({ ...prev, [field]: errorMessage }));

                    logger.error(`Failed to save field ${String(field)}`, {
                        error: errorMessage,
                        tableName,
                        stepId,
                        field: String(field),
                        displayName
                    });
                }
            } finally {
                if (isMountedRef.current) {
                    setIsFieldSaving(prev => ({ ...prev, [field]: false }));
                }
            }
        }, [stepId, tableName, updateRecord, selectiveRefresh, disabled, displayName]),
        debounceMs
    );

    // Handle field changes with validation and auto-save
    const handleFieldChange = useCallback((field: keyof T, value: unknown) => {
        if (disabled) return;

        // Update form data immediately for responsive UI
        setFormData(prev => {
            const newData = { ...prev, [field]: value };

            // Check if form is dirty
            const isFormDirty = JSON.stringify(newData) !== JSON.stringify(originalDataRef.current);
            setIsDirty(isFormDirty);

            return newData;
        });

        // Validate field if validator provided
        if (validateField) {
            const error = validateField(field, value);
            setFieldErrors(prev => ({
                ...prev,
                [field]: error || undefined
            }));
        }

        // Trigger debounced save
        debouncedSave(field, value);
    }, [disabled, validateField, debouncedSave]);

    // Handle field blur for additional validation
    const handleFieldBlur = useCallback((field: keyof T) => {
        if (disabled || !validateField) return;

        const value = formData[field];
        const error = validateField(field, value);

        setFieldErrors(prev => ({
            ...prev,
            [field]: error || undefined
        }));
    }, [disabled, validateField, formData]);

    // Validate all fields
    const validateAllFields = useCallback((): boolean => {
        if (!validateField) return true;

        const errors: Partial<Record<keyof T, string>> = {};
        let isValid = true;

        Object.keys(formData).forEach(key => {
            const field = key as keyof T;
            const error = validateField(field, formData[field]);
            if (error) {
                errors[field] = error;
                isValid = false;
            }
        });

        setFieldErrors(errors);
        return isValid;
    }, [formData, validateField]);

    // Reset form to initial or provided data
    const resetForm = useCallback((newData?: T) => {
        const resetData = newData || originalDataRef.current;
        setFormData(resetData);
        setFieldErrors({});
        setIsFieldSaving({});
        setIsDirty(false);

        if (newData) {
            originalDataRef.current = newData;
        }
    }, []);

    // Check if any field is currently saving
    const isSaving = Object.values(isFieldSaving).some(Boolean);

    return {
        formData,
        fieldErrors,
        isFieldSaving,
        handleFieldChange,
        handleFieldBlur,
        validateAllFields,
        resetForm,
        isDirty,
        isSaving,
    };
}

/**
 * Hook for managing individual form fields with auto-save, debouncing, and validation.
 * Useful for standalone field management or when you need fine-grained control.
 */
export function useFieldManagement({
    entityId,
    tableName,
    fieldName,
    initialValue,
    updateFn,
    debounceMs = 800,
    validationRules = [],
    formatter,
    disabled = false,
    fieldDisplayName
}: UseFieldManagementOptions): UseFieldManagementReturn {
    const [value, setValue] = useState(initialValue);
    const [error, setError] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState(false);
    const [lastSavedValue, setLastSavedValue] = useState(initialValue);

    // Refs for cleanup and memory management
    const isMountedRef = useRef(true);
    const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const interactionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Track user interaction to prevent input override
    const [isUserInteracting, setIsUserInteracting] = useState(false);

    // Debounced value for auto-save
    const debouncedValue = useDebounce(value, debounceMs);

    // Display name for logging
    const displayName = fieldDisplayName || fieldName;

    // Cleanup function
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
            if (interactionTimeoutRef.current) {
                clearTimeout(interactionTimeoutRef.current);
            }
        };
    }, []);

    // Update value when initialValue changes (e.g., from server)
    // BUT only if user is not actively typing
    useEffect(() => {
        // Don't override user input if they're actively typing or saving
        if (isUserInteracting || isSaving) {
            return;
        }

        // Only update if the value actually changed and it's not undefined
        // Also prevent recursive updates by checking if it's really different
        if (initialValue !== value && 
            initialValue !== undefined && 
            JSON.stringify(initialValue) !== JSON.stringify(value)) {
            
            setValue(initialValue);
            setLastSavedValue(initialValue);
            setError(null);
        }
    }, [initialValue, value, isUserInteracting, isSaving]);

    // Validate the current value
    const validateValue = useCallback((valueToValidate: unknown): string | null => {
        if (validationRules.length === 0) return null;
        return validateFormField(valueToValidate, validationRules);
    }, [validationRules]);

    // Save function
    const saveValue = useCallback(async (valueToSave: unknown): Promise<void> => {
        if (!isMountedRef.current || disabled || !entityId) return;

        // Skip save if value hasn't changed
        if (valueToSave === lastSavedValue) {
            return;
        }

        // Validate before saving
        const validationError = validateValue(valueToSave);
        if (validationError) {
            setError(validationError);
            return;
        }

        // Optimistic update - immediately update lastSavedValue for better UX
        const previousSavedValue = lastSavedValue;
        setLastSavedValue(valueToSave);
        setIsSaving(true);
        setError(null);

        try {
            // Apply formatter if provided
            const processedValue = formatter ? formatter(valueToSave) : valueToSave;

            // Convert empty strings to null for database storage
            const dbValue = processedValue === '' ? null : processedValue;

            logger.info(`Auto-saving field: ${displayName}`, {
                tableName,
                entityId,
                fieldName,
                oldValue: lastSavedValue,
                newValue: dbValue,
            });

            await updateFn(tableName, entityId, { [fieldName]: dbValue });

            if (isMountedRef.current) {
                setLastSavedValue(valueToSave);
                setError(null);

                logger.info(`Successfully saved field: ${displayName}`, {
                    tableName,
                    entityId,
                    fieldName,
                    savedValue: dbValue,
                });
            }
        } catch (saveError: unknown) {
            if (!isMountedRef.current) return;

            const errorMessage = saveError instanceof Error
                ? saveError.message
                : 'Failed to save changes';

            logger.error(`Failed to save field: ${displayName}`, {
                error: errorMessage,
                tableName,
                entityId,
                fieldName,
                attemptedValue: valueToSave,
            });

            // Revert optimistic update on error
            setLastSavedValue(previousSavedValue);
            setError(`Failed to save. Please try again.`);
        } finally {
            if (isMountedRef.current) {
                setIsSaving(false);
            }
        }
    }, [
        entityId,
        tableName,
        fieldName,
        updateFn,
        disabled,
        validateValue,
        formatter,
        displayName
    ]);

    // Auto-save when debounced value changes
    useEffect(() => {
        if (debouncedValue !== lastSavedValue && !disabled) {
            saveValue(debouncedValue);
        }
    }, [debouncedValue, lastSavedValue, saveValue, disabled]);

    // Handle input changes
    const onChange = useCallback((newValue: unknown) => {
        if (disabled) return;

        setValue(newValue);

        // Track user interaction to prevent server override
        setIsUserInteracting(true);
        
        // Clear previous timeout and set new one
        if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
        }
        
        // Mark user as not interacting after 2 seconds of inactivity
        interactionTimeoutRef.current = setTimeout(() => {
            setIsUserInteracting(false);
        }, 2000);

        // Clear error on change (will be re-validated on save)
        if (error) {
            setError(null);
        }

        // Immediate validation for better UX
        const validationError = validateValue(newValue);
        if (validationError) {
            setError(validationError);
        }
    }, [disabled, error, validateValue]);

    // Handle blur events (immediate save)
    const onBlur = useCallback(() => {
        if (disabled || !isMountedRef.current) return;

        // Cancel any pending debounced save
        if (saveTimeoutRef.current) {
            clearTimeout(saveTimeoutRef.current);
        }

        // Immediate save
        if (value !== lastSavedValue) {
            saveValue(value);
        }
    }, [disabled, value, lastSavedValue, saveValue]);

    // Manual save function
    const save = useCallback(async () => {
        if (disabled) return;
        await saveValue(value);
    }, [disabled, value, saveValue]);

    // Reset function
    const reset = useCallback(() => {
        setValue(initialValue);
        setLastSavedValue(initialValue);
        setError(null);
    }, [initialValue]);

    const hasUnsavedChanges = value !== lastSavedValue;

    return {
        value,
        onChange,
        onBlur,
        error,
        isSaving,
        hasUnsavedChanges,
        save,
        reset,
    };
}

/**
 * Hook for managing business registration with auto-creation when valid data is provided.
 * Avoids database constraint violations by only creating records with valid ABN and business name.
 */
export function useBusinessRegistrationManagement(stepId: string | null) {
    const { createRecord, updateRecord, refreshSessionData } = useOnboarding();
    const [isCreatingRecord, setIsCreatingRecord] = useState(false);
    const [recordId, setRecordId] = useState<string | null>(null);

    // Validation function for business registration fields (memoized to prevent infinite loops)
    const validateBusinessField = useCallback((field: string, value: unknown): string | null => {
        switch (field) {
            case 'full_business_name':
                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    return 'Business name is required';
                }
                return null;
            case 'abn':
                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    return 'ABN is required';
                }
                // ABN format validation
                if (typeof value === 'string') {
                    const abnPattern = /^[0-9]{2}\s[0-9]{3}\s[0-9]{3}\s[0-9]{3}$/;
                    if (!abnPattern.test(value.trim())) {
                        return 'Please enter a valid ABN (XX XXX XXX XXX format)';
                    }
                }
                return null;
            case 'business_structure':
                if (!value) {
                    return 'Business structure is required';
                }
                return null;
            default:
                return null;
        }
    }, []);

    // Create business registration record when valid data is provided
    const createBusinessRegistration = useCallback(async (formData: Record<string, unknown>) => {
        if (!stepId || isCreatingRecord) return null;

        // Only create if we have minimum valid data
        const hasValidName = typeof formData.full_business_name === 'string' && formData.full_business_name.trim();
        const hasValidAbn = typeof formData.abn === 'string' && formData.abn.trim() &&
            /^[0-9]{2}\s[0-9]{3}\s[0-9]{3}\s[0-9]{3}$/.test(formData.abn.trim());

        if (!hasValidName || !hasValidAbn) {
            return null; // Don't create record yet
        }

        setIsCreatingRecord(true);
        try {
            logger.info('Creating business registration record with valid data', {
                stepId,
                hasValidName: !!hasValidName,
                hasValidAbn: !!hasValidAbn
            });

            const result = await createRecord('business_registration', {
                step_1_id: stepId,
                full_business_name: (formData.full_business_name as string).trim(),
                abn: (formData.abn as string).trim(),
                business_structure: (formData.business_structure as string) || 'Sole Trader',
                is_gst_registered: Boolean(formData.is_gst_registered),
                trading_name: typeof formData.trading_name === 'string' ? formData.trading_name.trim() || null : null,
                acn: typeof formData.acn === 'string' ? formData.acn.trim() || null : null,
                primary_business_phone: typeof formData.primary_business_phone === 'string' ? formData.primary_business_phone.trim() || null : null,
            });

            const createdRecord = Array.isArray(result) ? result[0] : result;
            if (createdRecord?.id) {
                setRecordId(createdRecord.id);
                await refreshSessionData(false); // Don't show toast for automatic refresh
                return createdRecord.id;
            }
            return null;
        } catch (error) {
            logger.error('Failed to create business registration record', { error });
            return null;
        } finally {
            setIsCreatingRecord(false);
        }
    }, [stepId, isCreatingRecord, createRecord, refreshSessionData]);

    return {
        validateBusinessField,
        createBusinessRegistration,
        isCreatingRecord,
        recordId,
    };
} 