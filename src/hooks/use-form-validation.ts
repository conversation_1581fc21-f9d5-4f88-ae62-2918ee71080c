import { useState, useCallback, useMemo } from 'react';
import { validateForm, validateFormField, ValidationConfig } from '@/utils/form-validation';

/**
 * Validation schema defining rules for each field
 */
export type ValidationSchema<T> = Record<keyof T, ValidationConfig[]>;

/**
 * Configuration options for form validation
 */
export interface UseFormValidationOptions<T> {
    /** Validation schema mapping field names to validation rules */
    validationSchema: ValidationSchema<T>;
    /** Whether to validate on change (default: false) */
    validateOnChange?: boolean;
    /** Whether to validate immediately on mount (default: false) */
    validateOnMount?: boolean;
}

/**
 * Return type for the useFormValidation hook
 */
export interface UseFormValidationReturn<T> {
    /** Current validation errors for each field */
    errors: Partial<Record<keyof T, string>>;
    /** Whether the entire form is valid */
    isValid: boolean;
    /** Whether any validation has been attempted */
    hasValidated: boolean;
    /** Validate a specific field */
    validateField: (fieldName: keyof T, value: unknown) => string | null;
    /** Validate the entire form */
    validateAll: (formData: T) => Partial<Record<keyof T, string>>;
    /** Clear errors for a specific field */
    clearFieldError: (fieldName: keyof T) => void;
    /** Clear all errors */
    clearAllErrors: () => void;
    /** Set a custom error for a field */
    setFieldError: (fieldName: keyof T, error: string) => void;
    /** Get error for a specific field */
    getFieldError: (fieldName: keyof T) => string | undefined;
    /** Check if a specific field is valid */
    isFieldValid: (fieldName: keyof T) => boolean;
    /** Get all fields with errors */
    getInvalidFields: () => (keyof T)[];
}

/**
 * Custom hook for comprehensive form validation
 * Manages validation state, errors, and provides validation functions
 */
export function useFormValidation<T extends Record<string, unknown>>({
    validationSchema,
    validateOnChange = false,
    validateOnMount = false,
}: UseFormValidationOptions<T>): UseFormValidationReturn<T> {
    const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
    const [hasValidated, setHasValidated] = useState(validateOnMount);

    /**
     * Validate a single field
     */
    const validateField = useCallback((fieldName: keyof T, value: unknown): string | null => {
        const fieldRules = validationSchema[fieldName];
        if (!fieldRules || fieldRules.length === 0) {
            return null;
        }

        return validateFormField(value, fieldRules);
    }, [validationSchema]);

    /**
     * Validate the entire form
     */
    const validateAll = useCallback((formData: T): Partial<Record<keyof T, string>> => {
        setHasValidated(true);

        const validationErrors = validateForm(formData, validationSchema);
        setErrors(validationErrors);

        return validationErrors;
    }, [validationSchema]);

    /**
     * Clear error for a specific field
     */
    const clearFieldError = useCallback((fieldName: keyof T) => {
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[fieldName];
            return newErrors;
        });
    }, []);

    /**
     * Clear all errors
     */
    const clearAllErrors = useCallback(() => {
        setErrors({});
        setHasValidated(false);
    }, []);

    /**
     * Set a custom error for a field
     */
    const setFieldError = useCallback((fieldName: keyof T, error: string) => {
        setErrors(prev => ({
            ...prev,
            [fieldName]: error,
        }));
        setHasValidated(true);
    }, []);

    /**
     * Get error for a specific field
     */
    const getFieldError = useCallback((fieldName: keyof T): string | undefined => {
        return errors[fieldName];
    }, [errors]);

    /**
     * Check if a specific field is valid
     */
    const isFieldValid = useCallback((fieldName: keyof T): boolean => {
        return !errors[fieldName];
    }, [errors]);

    /**
     * Get all fields with errors
     */
    const getInvalidFields = useCallback((): (keyof T)[] => {
        return Object.keys(errors).filter(key => errors[key as keyof T]) as (keyof T)[];
    }, [errors]);

    /**
     * Whether the entire form is valid (no errors)
     */
    const isValid = useMemo(() => {
        return Object.values(errors).every(error => !error);
    }, [errors]);

    return {
        errors,
        isValid,
        hasValidated,
        validateField,
        validateAll,
        clearFieldError,
        clearAllErrors,
        setFieldError,
        getFieldError,
        isFieldValid,
        getInvalidFields,
    };
}

/**
 * Higher-order hook that combines form validation with onChange validation
 * Provides automatic validation on field changes
 */
export function useFormValidationWithChange<T extends Record<string, unknown>>(
    formData: T,
    options: UseFormValidationOptions<T>
) {
    const validation = useFormValidation<T>(options);

    /**
     * Enhanced field validation that updates errors automatically
     */
    const validateFieldWithUpdate = useCallback((fieldName: keyof T, value: unknown): string | null => {
        const error = validation.validateField(fieldName, value);

        if (error) {
            validation.setFieldError(fieldName, error);
        } else {
            validation.clearFieldError(fieldName);
        }

        return error;
    }, [validation]);

    /**
     * Validate field on change if enabled
     */
    const handleFieldChange = useCallback((fieldName: keyof T, value: unknown) => {
        if (options.validateOnChange) {
            validateFieldWithUpdate(fieldName, value);
        }
    }, [options.validateOnChange, validateFieldWithUpdate]);

    return {
        ...validation,
        validateFieldWithUpdate,
        handleFieldChange,
    };
} 