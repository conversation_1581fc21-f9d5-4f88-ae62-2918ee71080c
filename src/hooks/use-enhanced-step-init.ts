import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { logger } from '@/utils/logger';

export interface UseEnhancedStepInitProps {
  stepName: string;
  sessionId: string | null;
  contextLoading: boolean;
  existingStepId: string | null;
  ensureStepFunction: (sessionId: string) => Promise<{ id: string } | null>;
  timeoutMs?: number;
}

export interface UseEnhancedStepInitReturn {
  isStepInitializing: boolean;
  initializationError: string | null;
  localStepId: string | null;
  handleRetryInitialization: () => void;
  isLoading: boolean;
  effectiveStepId: string | null;
}

/**
 * Enhanced step initialization hook that consolidates common patterns
 * across all onboarding steps for initialization logic, error handling,
 * and memory management.
 */
export const useEnhancedStepInit = ({
  stepName,
  sessionId,
  contextLoading,
  existingStepId,
  ensureStepFunction,
  timeoutMs = 30000
}: UseEnhancedStepInitProps): UseEnhancedStepInitReturn => {
  const [isStepInitializing, setIsStepInitializing] = useState(true);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [localStepId, setLocalStepId] = useState<string | null>(null);
  
  // Memory management - track component mount state
  const isMountedRef = useRef(true);
  const initializationAttemptRef = useRef<Promise<void> | null>(null);

  // Cleanup function to prevent memory leaks
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Memoize the effective step ID
  const effectiveStepId = useMemo(() => {
    return existingStepId || localStepId;
  }, [existingStepId, localStepId]);

  // Enhanced initialization with race condition prevention
  const initializeStep = useCallback(async () => {
    // Prevent multiple simultaneous initialization attempts
    if (initializationAttemptRef.current) {
      return initializationAttemptRef.current;
    }

    // Early return if we already have step data
    if (effectiveStepId) {
      setIsStepInitializing(false);
      setInitializationError(null);
      // Sync local tracking with existing data if needed
      if (existingStepId && !localStepId) {
        setLocalStepId(existingStepId);
      }
      return;
    }

    // Early return validation
    if (!sessionId) {
      if (!contextLoading && isMountedRef.current) {
        setInitializationError("Session not available. Please refresh the page.");
        setIsStepInitializing(false);
      }
      return;
    }

    // Create and track the initialization promise
    const initPromise = (async () => {
      if (!isMountedRef.current) return;

      logger.info(`${stepName}: Starting step initialization`, { sessionId });
      setIsStepInitializing(true);
      setInitializationError(null);

      try {
        const result = await ensureStepFunction(sessionId);
        
        if (!isMountedRef.current) return;

        if (!result || !result.id) {
          throw new Error(`Failed to create or retrieve ${stepName} record`);
        }

        setLocalStepId(result.id);
        setIsStepInitializing(false);
        setInitializationError(null);
        
        logger.info(`${stepName}: Initialization completed successfully`, {
          stepId: result.id,
          sessionId
        });

      } catch (error: unknown) {
        if (!isMountedRef.current) return;
        
        const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
        logger.error(`${stepName}: Initialization failed`, {
          error: errorMessage,
          stack: error instanceof Error ? error.stack : undefined,
          sessionId,
          operation: 'step_initialization'
        });
        
        setInitializationError(`Initialization failed: ${errorMessage}`);
        setIsStepInitializing(false);
      } finally {
        initializationAttemptRef.current = null;
      }
    })();

    initializationAttemptRef.current = initPromise;
    return initPromise;
  }, [stepName, sessionId, effectiveStepId, existingStepId, localStepId, contextLoading, ensureStepFunction]);

  // Declarative initialization effect
  useEffect(() => {
    if (!isMountedRef.current) {
      logger.info(`${stepName}: Effect skipped, component unmounted.`);
      return;
    }

    if (!sessionId) {
      if (!contextLoading) {
        setInitializationError("Session not available. Please refresh the page.");
        setIsStepInitializing(false);
        logger.warn(`${stepName}: No sessionId and context not loading. Initialization cannot proceed.`);
      } else {
        logger.info(`${stepName}: Waiting for sessionId; context is loading.`, { contextLoading });
      }
      return;
    }

    // If we have step data, initialization is complete
    if (effectiveStepId) {
      setIsStepInitializing(false);
      setInitializationError(null);
      logger.info(`${stepName}: Using existing step data. Initialization complete.`, {
        stepId: effectiveStepId,
        sessionId
      });
      return;
    }

    // Start initialization if context is not loading
    if (!contextLoading) {
      initializeStep();
    } else {
      logger.info(`${stepName}: Waiting for context to finish loading.`, { sessionId, contextLoading });
    }
  }, [stepName, sessionId, effectiveStepId, contextLoading, initializeStep]);

  // Timeout safeguard to prevent eternal loading
  useEffect(() => {
    if (isStepInitializing) {
      const timeout = setTimeout(() => {
        if (isMountedRef.current && isStepInitializing) {
          logger.warn(`${stepName}: Initialization timeout - forcing reset`);
          setIsStepInitializing(false);
          setInitializationError("Initialization timed out. Please try again.");
        }
      }, timeoutMs);

      return () => clearTimeout(timeout);
    }
  }, [isStepInitializing, timeoutMs, stepName]);

  // Enhanced retry functionality
  const handleRetryInitialization = useCallback(() => {
    setInitializationError(null);
    setIsStepInitializing(true);
    initializationAttemptRef.current = null;
    // The useEffect will handle re-initialization automatically
  }, []);

  // Simplified loading state calculation
  const isLoading = useMemo(() => {
    return contextLoading || isStepInitializing || (!effectiveStepId && !initializationError);
  }, [contextLoading, isStepInitializing, effectiveStepId, initializationError]);

  return {
    isStepInitializing,
    initializationError,
    localStepId,
    handleRetryInitialization,
    isLoading,
    effectiveStepId,
  };
};