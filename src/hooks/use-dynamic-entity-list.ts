import { useState, useCallback, useRef, useEffect } from 'react';
import { logger } from '@/utils/logger';

/**
 * Configuration options for dynamic entity list management
 */
export interface UseDynamicEntityListOptions<T> {
    /** Parent step ID to link entities to */
    stepId: string;
    /** Database table name */
    tableName: string;
    /** Default values for new entities */
    defaultValues: Partial<T>;
    /** Whether operations are disabled */
    disabled: boolean;
    /** Function to create new records */
    createFn: (tableName: string, data: Record<string, unknown>) => Promise<T>;
    /** Function to update existing records */
    updateFn: (tableName: string, id: string, data: Record<string, unknown>) => Promise<void>;
    /** Function to delete records */
    deleteFn: (tableName: string, id: string) => Promise<boolean>;
    /** Display name for logging */
    entityDisplayName?: string;
}

/**
 * Return type for the useDynamicEntityList hook
 */
export interface UseDynamicEntityListReturn<T> {
    /** Function to add a new entity */
    addEntity: () => Promise<void>;
    /** Function to delete an entity by ID */
    deleteEntity: (entityId: string) => Promise<void>;
    /** Whether any operation is currently in progress */
    isOperating: boolean;
    /** Errors from the last operation */
    operationError: string | null;
    /** Clear the operation error */
    clearError: () => void;
}

/**
 * Custom hook for managing dynamic lists of entities
 * Provides add, delete functionality with error handling and loading states
 */
export function useDynamicEntityList<T extends { id: string }>({
    stepId,
    tableName,
    defaultValues,
    disabled,
    createFn,
    updateFn,
    deleteFn,
    entityDisplayName = 'entity'
}: UseDynamicEntityListOptions<T>): UseDynamicEntityListReturn<T> {
    const [isOperating, setIsOperating] = useState(false);
    const [operationError, setOperationError] = useState<string | null>(null);

    // Ref for memory management
    const isMountedRef = useRef(true);

    // Cleanup function
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    /**
     * Add a new entity to the list
     */
    const addEntity = useCallback(async (): Promise<void> => {
        // Validation checks
        if (!stepId) {
            const errorMsg = `Cannot add ${entityDisplayName} - step ID is undefined`;
            logger.error(`DynamicEntityList: ${errorMsg}`, {
                stepId,
                tableName,
                disabled,
                entityDisplayName,
            });
            setOperationError(`Unable to add ${entityDisplayName}: Step not fully initialized. Please wait a moment and try again.`);
            return;
        }

        if (disabled) {
            logger.info(`DynamicEntityList: Cannot add ${entityDisplayName} - operations disabled`, {
                stepId,
                tableName,
                disabled,
            });
            return;
        }

        if (isOperating) {
            logger.info(`DynamicEntityList: Cannot add ${entityDisplayName} - operation already in progress`);
            return;
        }

        setIsOperating(true);
        setOperationError(null);

        try {
            logger.info(`DynamicEntityList: Adding new ${entityDisplayName}`, {
                stepId,
                tableName,
                defaultValues,
            });

            // Map table names to their correct step foreign key columns
            const getStepColumnName = (tableName: string): string => {
                const stepColumnMap: Record<string, string> = {
                    // Step 1 tables
                    'addresses': 'step_1_id',
                    'contacts': 'step_1_id',
                    'business_registration': 'step_1_id',

                    // Step 2 tables
                    'activities': 'step_2_id',
                    'licenses': 'step_2_id',
                    'suppliers': 'step_2_id',
                    'contracts': 'step_2_id',
                    'chemical_usage': 'step_2_id',

                    // Step 3 tables
                    'assets': 'step_3_id',
                    'bookkeeping': 'step_3_id',
                    'payroll': 'step_3_id',

                    // Step 4 tables
                    'agreements': 'step_4_id',
                    'permissions': 'step_4_id',
                    'payments': 'step_4_id',
                    'data_migration': 'step_4_id',
                    'communication_preferences': 'step_4_id',
                };

                const columnName = stepColumnMap[tableName];
                if (!columnName) {
                    throw new Error(`Unknown table name: ${tableName}. No step column mapping found.`);
                }
                return columnName;
            };

            // Prepare the payload with step ID and default values
            const stepColumnName = getStepColumnName(tableName);
            const newEntityPayload = {
                ...defaultValues,
                [stepColumnName]: stepId,
            };

            // Double-check step ID before creating
            if (!stepId) {
                throw new Error(`Step ID became undefined during ${entityDisplayName} creation`);
            }

            const result = await createFn(tableName, newEntityPayload);

            if (isMountedRef.current) {
                logger.info(`DynamicEntityList: Successfully added ${entityDisplayName}`, {
                    stepId,
                    tableName,
                    newEntityId: result.id,
                });
            }
        } catch (error: unknown) {
            if (!isMountedRef.current) return;

            const errorMessage = error instanceof Error ? error.message : `Failed to add ${entityDisplayName}`;

            logger.error(`DynamicEntityList: Failed to add ${entityDisplayName}`, {
                error: errorMessage,
                stepId,
                tableName,
                operation: 'addEntity',
            });

            setOperationError(`Failed to add ${entityDisplayName}. Please try again.`);
        } finally {
            if (isMountedRef.current) {
                setIsOperating(false);
            }
        }
    }, [
        stepId,
        tableName,
        defaultValues,
        disabled,
        createFn,
        entityDisplayName,
        isOperating
    ]);

    /**
     * Delete an entity from the list
     */
    const deleteEntity = useCallback(async (entityId: string): Promise<void> => {
        if (!entityId) {
            setOperationError(`Cannot delete ${entityDisplayName} - invalid ID`);
            return;
        }

        if (disabled) {
            logger.info(`DynamicEntityList: Cannot delete ${entityDisplayName} - operations disabled`, {
                entityId,
                tableName,
                disabled,
            });
            return;
        }

        if (isOperating) {
            logger.info(`DynamicEntityList: Cannot delete ${entityDisplayName} - operation already in progress`);
            return;
        }

        setIsOperating(true);
        setOperationError(null);

        try {
            logger.info(`DynamicEntityList: Deleting ${entityDisplayName}`, {
                entityId,
                tableName,
            });

            const success = await deleteFn(tableName, entityId);

            if (isMountedRef.current) {
                if (success) {
                    logger.info(`DynamicEntityList: Successfully deleted ${entityDisplayName}`, {
                        entityId,
                        tableName,
                    });
                } else {
                    throw new Error(`Delete operation returned false for ${entityDisplayName}`);
                }
            }
        } catch (error: unknown) {
            if (!isMountedRef.current) return;

            const errorMessage = error instanceof Error ? error.message : `Failed to delete ${entityDisplayName}`;

            logger.error(`DynamicEntityList: Failed to delete ${entityDisplayName}`, {
                error: errorMessage,
                entityId,
                tableName,
                operation: 'deleteEntity',
            });

            setOperationError(`Failed to delete ${entityDisplayName}. Please try again.`);
        } finally {
            if (isMountedRef.current) {
                setIsOperating(false);
            }
        }
    }, [
        tableName,
        disabled,
        deleteFn,
        entityDisplayName,
        isOperating
    ]);

    /**
     * Clear the current operation error
     */
    const clearError = useCallback(() => {
        setOperationError(null);
    }, []);

    return {
        addEntity,
        deleteEntity,
        isOperating,
        operationError,
        clearError,
    };
} 