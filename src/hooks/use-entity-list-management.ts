import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { useOnboarding, type TableName } from '@/contexts/OnboardingContext';
import { useSelectiveRefresh } from '@/hooks/use-selective-refresh';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/utils/logger';
import { Database } from '@/types/database.types';
import { validateUUID, isValidUUID } from '@/utils/uuid-validation';

/**
 * Configuration options for entity list management
 */
export interface UseEntityListManagementOptions<T, TInsert, TUpdate> {
    /** Parent step ID to link entities to */
    stepId: string | null;
    /** Database table name */
    tableName: TableName;
    /** Initial entities array */
    initialEntities?: T[] | null;
    /** Function to create default entity data */
    createDefaultEntity: (stepId: string) => TInsert;
    /** Entity validation function */
    validateEntity?: (entity: Partial<TUpdate>) => string | null;
    /** Display name for the entity type (singular) */
    entityDisplayName: string;
    /** Whether operations are disabled */
    disabled?: boolean;
    /** Maximum number of entities allowed */
    maxEntities?: number;
    /** Minimum number of entities required */
    minEntities?: number;
    /** Whether to show toast notifications */
    showNotifications?: boolean;
}

/**
 * Return type for entity list management
 */
export interface UseEntityListManagementReturn<T, TInsert, TUpdate> {
    /** Current entities array */
    entities: T[];
    /** Whether any operation is in progress */
    isLoading: boolean;
    /** Whether adding entity is in progress */
    isAddingEntity: boolean;
    /** Whether deleting entity is in progress */
    isDeletingEntity: boolean;
    /** Entity-specific errors */
    entityErrors: Record<string, string>;
    /** Function to add a new entity */
    addEntity: () => Promise<void>;
    /** Function to delete an entity by ID */
    deleteEntity: (entityId: string) => Promise<void>;
    /** Function to update an entity */
    updateEntity: (entityId: string, updates: Partial<TUpdate>) => Promise<void>;
    /** Function to validate an entity */
    validateEntity: (entity: Partial<TUpdate>) => string | null;
    /** Function to clear entity error */
    clearEntityError: (entityId: string) => void;
    /** Function to refresh entities from server */
    refreshEntities: () => Promise<void>;
    /** Any operation error */
    operationError: string | null;
    /** Function to clear operation error */
    clearOperationError: () => void;
    /** Whether constraints are met */
    canAddEntity: boolean;
    /** Whether entity can be deleted */
    canDeleteEntity: boolean;
}

/**
 * Comprehensive entity list management hook that handles CRUD operations, validation, and state management.
 * Consolidates functionality from multiple entity management patterns.
 */
export function useEntityListManagement<T extends { id: string }, TInsert, TUpdate>({
    stepId,
    tableName,
    initialEntities,
    createDefaultEntity,
    validateEntity,
    entityDisplayName,
    disabled = false,
    maxEntities,
    minEntities,
    showNotifications = true,
}: UseEntityListManagementOptions<T, TInsert, TUpdate>): UseEntityListManagementReturn<T, TInsert, TUpdate> {
    const { createRecord, updateRecord, deleteRecord, refreshSessionData } = useOnboarding();
    const { selectiveRefresh } = useSelectiveRefresh();
    const { toast } = useToast();

    const [entities, setEntities] = useState<T[]>(() => initialEntities || []);
    const [isAddingEntity, setIsAddingEntity] = useState(false);
    const [isDeletingEntity, setIsDeletingEntity] = useState(false);
    const [entityErrors, setEntityErrors] = useState<Record<string, string>>({});
    const [operationError, setOperationError] = useState<string | null>(null);

    // Refs for memory management and cleanup
    const isMountedRef = useRef(true);
    const isLoadingRef = useRef(false);

    // Cleanup function
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    // Update entities when initialEntities changes
    useEffect(() => {
        if (initialEntities) {
            setEntities(initialEntities);
        }
    }, [initialEntities]);

    // Computed loading state
    const isLoading = useMemo(() => {
        return isLoadingRef.current || isAddingEntity || isDeletingEntity;
    }, [isAddingEntity, isDeletingEntity]);

    // Computed constraint checks
    const canAddEntity = useMemo(() => {
        if (disabled || isLoading || !stepId) return false;
        if (maxEntities && entities.length >= maxEntities) return false;
        return true;
    }, [disabled, isLoading, stepId, maxEntities, entities.length]);

    const canDeleteEntity = useMemo(() => {
        if (disabled || isLoading) return false;
        if (minEntities && entities.length <= minEntities) return false;
        return true;
    }, [disabled, isLoading, minEntities, entities.length]);

    /**
     * Map table names to their correct step foreign key columns
     */
    const getStepColumnName = useCallback((tableName: string): string => {
        const stepColumnMap: Record<string, string> = {
            // Step 1 tables
            'addresses': 'step_1_id',
            'contacts': 'step_1_id',
            'business_registration': 'step_1_id',
            'key_staff': 'step_1_id',

            // Step 2 tables
            'activities': 'step_2_id',
            'licenses': 'step_2_id',
            'suppliers': 'step_2_id',
            'contracts': 'step_2_id',
            'chemical_usage': 'step_2_id',

            // Step 3 tables
            'assets': 'step_3_id',
            'bookkeeping': 'step_3_id',
            'payroll': 'step_3_id',

            // Step 4 tables
            'agreements': 'step_4_id',
            'permissions': 'step_4_id',
            'payments': 'step_4_id',
            'data_migration': 'step_4_id',
            'communication_preferences': 'step_4_id',
        };

        const columnName = stepColumnMap[tableName];
        if (!columnName) {
            throw new Error(`Unknown table name: ${tableName}. No step column mapping found.`);
        }
        return columnName;
    }, []);

    /**
     * Add a new entity to the list
     */
    const addEntity = useCallback(async (): Promise<void> => {
        // Enhanced stepId validation with UUID format check
        if (!stepId) {
            const errorMsg = `Cannot add ${entityDisplayName} - step ID is undefined`;
            logger.error(`EntityListManagement: ${errorMsg}`, {
                stepId,
                tableName,
                disabled,
                entityDisplayName,
            });
            setOperationError(`Unable to add ${entityDisplayName}: Step not fully initialized. Please wait a moment and try again.`);
            return;
        }

        try {
            validateUUID(stepId, `${entityDisplayName} step ID`);
        } catch (error) {
            const errorMsg = `Cannot add ${entityDisplayName} - invalid step ID format`;
            logger.error(`EntityListManagement: ${errorMsg}`, {
                stepId,
                tableName,
                disabled,
                entityDisplayName,
                validationError: error instanceof Error ? error.message : 'Unknown validation error',
            });
            setOperationError(`Unable to add ${entityDisplayName}: Invalid step configuration. Please refresh the page and try again.`);
            return;
        }

        if (!canAddEntity) {
            if (maxEntities && entities.length >= maxEntities) {
                const message = `You can only have up to ${maxEntities} ${entityDisplayName}${maxEntities > 1 ? 's' : ''}.`;
                setOperationError(message);
                if (showNotifications) {
                    toast({
                        title: "Limit Reached",
                        description: message,
                        variant: "destructive",
                    });
                }
            }
            return;
        }

        setIsAddingEntity(true);
        setOperationError(null);
        setEntityErrors(prev => ({ ...prev, [`new_${Date.now()}`]: '' }));

        try {
            logger.info(`EntityListManagement: Adding new ${entityDisplayName}`, {
                stepId,
                tableName,
                entityDisplayName,
            });

            // Prepare the payload with step ID and default values
            const stepColumnName = getStepColumnName(tableName);
            const newEntityData = {
                ...createDefaultEntity(stepId),
                [stepColumnName]: stepId,
            };

            // Double-check step ID before creating
            if (!stepId) {
                throw new Error(`Step ID became undefined during ${entityDisplayName} creation`);
            }

            const result = await createRecord(tableName, newEntityData);

            // Handle case where result might be an array (take first item) or single object
            const createdEntity = Array.isArray(result) ? result[0] : result;

            if (!createdEntity?.id) {
                throw new Error(`Failed to create ${entityDisplayName}`);
            }

            // Use a moderate debounce to prevent refresh conflicts with user input
            await selectiveRefresh({
                tables: [tableName],
                debounceMs: 1000 // Balanced timing for responsiveness and conflict prevention
            });

            if (isMountedRef.current) {
                if (showNotifications) {
                    toast({
                        title: "Success",
                        description: `${entityDisplayName} added successfully`,
                        variant: "default",
                    });
                }

                logger.info(`EntityListManagement: Successfully added ${entityDisplayName}`, {
                    stepId,
                    tableName,
                    entityId: createdEntity.id,
                    entityDisplayName,
                });
            }
        } catch (error: unknown) {
            if (!isMountedRef.current) return;

            const errorMessage = error instanceof Error ? error.message : `Failed to add ${entityDisplayName}`;

            logger.error(`EntityListManagement: Failed to add ${entityDisplayName}`, {
                error: errorMessage,
                stepId,
                tableName,
                operation: 'addEntity',
                entityDisplayName,
            });

            setOperationError(`Failed to add ${entityDisplayName}. Please try again.`);

            if (showNotifications) {
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
            }
        } finally {
            if (isMountedRef.current) {
                setIsAddingEntity(false);
            }
        }
    }, [
        stepId,
        canAddEntity,
        maxEntities,
        entities.length,
        entityDisplayName,
        showNotifications,
        toast,
        tableName,
        getStepColumnName,
        createDefaultEntity,
        createRecord,
        selectiveRefresh
    ]);

    /**
     * Delete an entity from the list
     */
    const deleteEntity = useCallback(async (entityId: string): Promise<void> => {
        if (!entityId) {
            setOperationError(`Cannot delete ${entityDisplayName} - invalid ID`);
            return;
        }

        // Validate entityId format
        try {
            validateUUID(entityId, `${entityDisplayName} entity ID`);
        } catch (error) {
            const errorMsg = `Cannot delete ${entityDisplayName} - invalid entity ID format`;
            logger.error(`EntityListManagement: ${errorMsg}`, {
                entityId,
                tableName,
                entityDisplayName,
                validationError: error instanceof Error ? error.message : 'Unknown validation error',
            });
            setOperationError(`Invalid entity ID format. Please refresh the page and try again.`);
            return;
        }

        if (!canDeleteEntity) {
            if (minEntities && entities.length <= minEntities) {
                const message = `You must have at least ${minEntities} ${entityDisplayName}${minEntities > 1 ? 's' : ''}.`;
                setOperationError(message);
                if (showNotifications) {
                    toast({
                        title: "Minimum Required",
                        description: message,
                        variant: "destructive",
                    });
                }
            }
            return;
        }

        setIsDeletingEntity(true);
        setOperationError(null);

        try {
            logger.info(`EntityListManagement: Deleting ${entityDisplayName}`, {
                entityId,
                tableName,
                entityDisplayName,
            });

            await deleteRecord(tableName as keyof Database['farms']['Tables'], entityId);

            // Use a moderate debounce to prevent refresh conflicts with user input
            await selectiveRefresh({
                tables: [tableName],
                debounceMs: 1000 // Balanced timing for responsiveness and conflict prevention
            });

            if (isMountedRef.current) {
                // Clear entity error if it exists
                setEntityErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors[entityId];
                    return newErrors;
                });

                if (showNotifications) {
                    toast({
                        title: "Success",
                        description: `${entityDisplayName} deleted successfully`,
                        variant: "default",
                    });
                }

                logger.info(`EntityListManagement: Successfully deleted ${entityDisplayName}`, {
                    entityId,
                    tableName,
                    entityDisplayName,
                });
            }
        } catch (error: unknown) {
            if (!isMountedRef.current) return;

            const errorMessage = error instanceof Error ? error.message : `Failed to delete ${entityDisplayName}`;

            logger.error(`EntityListManagement: Failed to delete ${entityDisplayName}`, {
                error: errorMessage,
                entityId,
                tableName,
                operation: 'deleteEntity',
                entityDisplayName,
            });

            setOperationError(`Failed to delete ${entityDisplayName}. Please try again.`);

            if (showNotifications) {
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
            }
        } finally {
            if (isMountedRef.current) {
                setIsDeletingEntity(false);
            }
        }
    }, [
        canDeleteEntity,
        minEntities,
        entities.length,
        entityDisplayName,
        showNotifications,
        toast,
        tableName,
        deleteRecord,
        selectiveRefresh
    ]);

    /**
     * Update an entity
     */
    const updateEntity = useCallback(async (entityId: string, updates: Partial<TUpdate>): Promise<void> => {
        if (!entityId || disabled) return;

        // Validate entityId format
        try {
            validateUUID(entityId, `${entityDisplayName} entity ID`);
        } catch (error) {
            const errorMsg = `Cannot update ${entityDisplayName} - invalid entity ID format`;
            logger.error(`EntityListManagement: ${errorMsg}`, {
                entityId,
                tableName,
                entityDisplayName,
                validationError: error instanceof Error ? error.message : 'Unknown validation error',
            });
            setEntityErrors(prev => ({ 
                ...prev, 
                [entityId]: `Invalid entity ID format. Please refresh the page and try again.` 
            }));
            return;
        }

        // Validate entity if validator provided
        if (validateEntity) {
            const error = validateEntity(updates);
            if (error) {
                setEntityErrors(prev => ({ ...prev, [entityId]: error }));
                return;
            }
        }

        try {
            await updateRecord(tableName as keyof Database['farms']['Tables'], entityId, updates);

            if (isMountedRef.current) {
                // Clear entity error on successful update
                setEntityErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors[entityId];
                    return newErrors;
                });

                logger.info(`EntityListManagement: ${entityDisplayName} updated successfully`, {
                    tableName,
                    entityId,
                    updates: Object.keys(updates),
                    entityDisplayName,
                });
            }
        } catch (error: unknown) {
            if (!isMountedRef.current) return;

            const errorMessage = error instanceof Error ? error.message : `Failed to update ${entityDisplayName}`;

            setEntityErrors(prev => ({ ...prev, [entityId]: errorMessage }));

            logger.error(`EntityListManagement: Failed to update ${entityDisplayName}`, {
                error: errorMessage,
                tableName,
                entityId,
                entityDisplayName,
            });
        }
    }, [disabled, validateEntity, updateRecord, tableName, entityDisplayName]);

    /**
     * Clear entity error
     */
    const clearEntityError = useCallback((entityId: string) => {
        setEntityErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[entityId];
            return newErrors;
        });
    }, []);

    /**
     * Refresh entities from server
     */
    const refreshEntities = useCallback(async () => {
        try {
            isLoadingRef.current = true;
            await refreshSessionData();
        } catch (error) {
            logger.error(`EntityListManagement: Failed to refresh entities`, {
                error: error instanceof Error ? error.message : 'Unknown error',
                tableName,
                entityDisplayName,
            });
        } finally {
            isLoadingRef.current = false;
        }
    }, [refreshSessionData, tableName, entityDisplayName]);

    /**
     * Clear operation error
     */
    const clearOperationError = useCallback(() => {
        setOperationError(null);
    }, []);

    /**
     * Default entity validator if none provided
     */
    const defaultValidateEntity = useCallback((entity: Partial<TUpdate>): string | null => {
        if (validateEntity) {
            return validateEntity(entity);
        }
        return null;
    }, [validateEntity]);

    return {
        entities,
        isLoading,
        isAddingEntity,
        isDeletingEntity,
        entityErrors,
        addEntity,
        deleteEntity,
        updateEntity,
        validateEntity: defaultValidateEntity,
        clearEntityError,
        refreshEntities,
        operationError,
        clearOperationError,
        canAddEntity,
        canDeleteEntity,
    };
} 