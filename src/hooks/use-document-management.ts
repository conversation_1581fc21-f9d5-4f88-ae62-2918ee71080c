/**
 * Hook for managing documents related to entities in the onboarding system
 * Provides upload, view, and delete capabilities for entity-related documents
 */

import { useState, useCallback, useMemo } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/utils/logger';
import { Document } from '@/types/onboarding';
import { validateEntityForUpload, isValidUUID } from '@/utils/uuid-validation';

export interface UseDocumentManagementOptions {
  entityType: string;
  entityId: string;
  entityDisplayName?: string;
}

export interface UseDocumentManagementReturn {
  documents: Document[];
  documentCount: number;
  hasDocuments: boolean;
  isUploading: boolean;
  uploadError: string | null;
  isLoadingDocument: string | null;
  
  // Actions
  handleFileUpload: (file: File) => Promise<void>;
  handleFileRemove: (documentId: string) => Promise<void>;
  handleDocumentView: (document: Document) => Promise<void>;
  handleDocumentDownload: (document: Document) => Promise<void>;
  clearUploadError: () => void;
}

/**
 * Hook for managing documents associated with a specific entity
 */
export function useDocumentManagement({
  entityType,
  entityId,
  entityDisplayName = 'item'
}: UseDocumentManagementOptions): UseDocumentManagementReturn {
  const { uploadAndFinalizeDocument, getSignedDocumentUrl, sessionData, deleteDocument } = useOnboarding();
  const { toast } = useToast();

  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isLoadingDocument, setIsLoadingDocument] = useState<string | null>(null);

  // Filter documents from session data for this entity with UUID validation
  const documents = useMemo(() => {
    if (!sessionData?.documents || !entityId) return [];
    
    // Only include documents with valid UUID format to prevent issues
    return sessionData.documents.filter(doc => 
      doc.related_to_entity === entityType && 
      doc.related_to_id === entityId &&
      isValidUUID(doc.related_to_id) && // Ensure the document's related_to_id is valid
      isValidUUID(doc.id) // Ensure the document ID itself is valid
    );
  }, [sessionData?.documents, entityType, entityId]);

  const documentCount = documents.length;
  const hasDocuments = documentCount > 0;

  // Handle file upload
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entityId) {
      logger.warn('Attempted file upload without entity ID', { entityType, file: file.name });
      setUploadError('Entity ID is missing. Please save the entity first before uploading documents.');
      return;
    }

    // Validate entity ID format before attempting upload
    try {
      validateEntityForUpload(entityId, entityType);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `Invalid ${entityType} ID format`;
      logger.error('Entity ID validation failed for upload', {
        entityType,
        entityId,
        file: file.name,
        validationError: errorMessage
      });
      
      setUploadError(errorMessage);
      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      logger.info('Starting document upload', {
        entityType,
        entityId,
        fileName: file.name,
        fileSize: file.size,
        entityDisplayName
      });

      const result = await uploadAndFinalizeDocument(
        file,
        entityType,
        entityId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: `${entityDisplayName} document uploaded successfully`,
          variant: "default",
        });

        logger.info('Document upload successful', {
          entityType,
          entityId,
          documentId: result.documentId,
          fileName: file.name
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });

      logger.error('Document upload failed', {
        error: errorMessage,
        entityType,
        entityId,
        fileName: file.name
      });
    } finally {
      setIsUploading(false);
    }
  }, [entityId, entityType, entityDisplayName, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async (documentId: string) => {
    if (!documentId) {
      logger.warn('Attempted file removal without document ID', { entityType, entityId });
      toast({
        title: "Remove Error",
        description: 'Document ID is missing. Cannot remove document.',
        variant: "destructive",
      });
      return;
    }

    try {
      logger.info('Starting document removal', {
        documentId,
        entityType,
        entityId,
        entityDisplayName
      });

      const result = await deleteDocument(documentId);

      if (result.success) {
        logger.info('Document removal successful', {
          documentId,
          entityType,
          entityId
        });
        
        // Note: OnboardingContext.deleteDocument already shows success toast and refreshes data
        // No additional toast needed here to avoid duplication
      } else {
        throw new Error(result.error || 'Document removal failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove document';
      
      logger.error('Document removal failed', {
        error: errorMessage,
        documentId,
        entityType,
        entityId
      });

      // Only show error toast here since OnboardingContext already handles success cases
      toast({
        title: "Remove Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }, [entityType, entityId, entityDisplayName, deleteDocument, toast]);

  // Handle document access (view/download)
  const handleDocumentAccess = useCallback(async (document: Document, action: 'view' | 'download') => {
    if (!document.storage_bucket_path) {
      toast({
        title: "Error",
        description: "Document path not found",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingDocument(document.id);
    
    try {
      logger.info('Accessing document', {
        documentId: document.id,
        documentName: document.document_name,
        action,
        entityType,
        entityId
      });

      const signedUrl = await getSignedDocumentUrl(document.storage_bucket_path);
      
      if (signedUrl) {
        if (action === 'view') {
          window.open(signedUrl, '_blank', 'noopener,noreferrer');
        } else {
          // Create download link
          const link = document.createElement('a');
          link.href = signedUrl;
          link.download = document.document_name;
          link.click();
        }
        
        toast({
          title: "Success",
          description: `Document ${action === 'view' ? 'opened' : 'downloaded'} successfully`,
          variant: "default",
        });

        logger.info('Document access successful', {
          documentId: document.id,
          action
        });
      } else {
        throw new Error('Failed to get document URL');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to access document';
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      logger.error('Document access failed', {
        error: errorMessage,
        documentId: document.id,
        action,
        entityType,
        entityId
      });
    } finally {
      setIsLoadingDocument(null);
    }
  }, [getSignedDocumentUrl, toast, entityType, entityId]);

  // Specific handlers for view and download
  const handleDocumentView = useCallback((document: Document) => 
    handleDocumentAccess(document, 'view'), [handleDocumentAccess]);

  const handleDocumentDownload = useCallback((document: Document) => 
    handleDocumentAccess(document, 'download'), [handleDocumentAccess]);

  // Clear upload error
  const clearUploadError = useCallback(() => {
    setUploadError(null);
  }, []);

  return {
    documents,
    documentCount,
    hasDocuments,
    isUploading,
    uploadError,
    isLoadingDocument,
    
    // Actions
    handleFileUpload,
    handleFileRemove,
    handleDocumentView,
    handleDocumentDownload,
    clearUploadError,
  };
}