/**
 * Unified form update wrapper hook
 * Provides a consistent interface for form field updates across all components
 */

import { useCallback } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { TABLE_NAMES, isValidTableName } from '@/constants/database-tables';
import { Database } from '@/types/database.types';

type TableName = keyof Database['farms']['Tables'];

/**
 * Creates a type-safe update wrapper function that bridges between
 * AutoSaveFormField's expected signature and entity form's onUpdate prop
 */
export function useFormUpdateWrapper<T extends Record<string, unknown>>(
  onUpdate: (entityId: string, updates: Partial<T>) => Promise<void>
) {
  const { updateRecord } = useOnboarding();

  /**
   * Wrapper function that handles the AutoSaveForm<PERSON>ield signature
   * and converts it to the entity form's expected signature
   */
  const updateWrapper = useCallback(
    async (tableName: string, id: string, data: Record<string, unknown>) => {
      // Validate table name
      if (!isValidTableName(tableName)) {
        console.error(`Invalid table name: ${tableName}`);
        throw new Error(`Invalid table name: ${tableName}`);
      }

      // Use the onUpdate prop for consistency
      await onUpdate(id, data as Partial<T>);
    },
    [onUpdate]
  );

  /**
   * Direct update function that uses the OnboardingContext's updateRecord
   * This provides an alternative when you need direct database access
   */
  const directUpdate = useCallback(
    async (tableName: TableName, id: string, data: Record<string, unknown>) => {
      return updateRecord(tableName, id, data);
    },
    [updateRecord]
  );

  return {
    updateWrapper,
    directUpdate,
    TABLE_NAMES, // Export for convenience
  };
}