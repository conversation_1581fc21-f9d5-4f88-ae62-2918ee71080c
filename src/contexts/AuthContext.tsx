/* eslint-disable react-refresh/only-export-components */
import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface UserMetadata {
  full_name?: string;
  username?: string;
  phone_number?: string;
  receive_text_messages?: boolean;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, metadata?: UserMetadata) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Convenience hook to access the authentication context.
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Provides user authentication state and helper methods to its children.
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('AuthContext: useEffect triggered.');
    setLoading(true); // Explicitly set loading true at the start of effect

    // Set up auth state listener first
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('AuthContext: onAuthStateChange event:', event, 'Session from event:', session ? 'exists' : null, 'User from event:', session?.user?.id ?? null);
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
        console.log('AuthContext: onAuthStateChange finished. Loading set to false. User:', session?.user?.id ?? null);
      }
    );

    // Then check for existing session
    console.log('AuthContext: Calling supabase.auth.getSession()');
    supabase.auth.getSession().then(({ data: { session: currentSession } }) => {
      console.log('AuthContext: getSession() resolved. Session from getSession:', currentSession ? 'exists' : null, 'User from getSession:', currentSession?.user?.id ?? null);
      // Only update if onAuthStateChange hasn't already handled a more recent session
      // This basic check might not be fully sufficient for complex race conditions
      // but helps avoid unnecessary overwrites if onAuthStateChange already ran.
      if (!user && !session) { // A simple check, might need more robust logic if races are an issue
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        console.log('AuthContext: getSession() updated state. User:', currentSession?.user?.id ?? null);
      } else {
        console.log('AuthContext: getSession() did not update state as user/session already existed. Current User:', user?.id, 'Current Session:', session ? 'exists' : null);
      }
      setLoading(false); // Ensure loading is false after getSession
      console.log('AuthContext: getSession() finished. Loading set to false. User after potential update:', (currentSession?.user ?? user)?.id ?? null);
    }).catch(error => {
      console.error('AuthContext: Error in getSession() promise:', error);
      setLoading(false); // Ensure loading is false on error too
    });

    return () => {
      console.log('AuthContext: Unsubscribing from onAuthStateChange.');
      subscription.unsubscribe();
    }
  }, []); // Empty dependency array means this runs once on mount and cleans up on unmount

  /**
   * Creates a new user account and sends a confirmation email.
   */
  const signUp = async (email: string, password: string, metadata?: UserMetadata) => {
    const redirectUrl = `${window.location.origin}/`;

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: metadata || {}
      }
    });
    return { error };
  };

  /**
   * Signs in a user with email and password.
   */
  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  /**
   * Clears the current Supabase auth session.
   */
  const signOut = async () => {
    await supabase.auth.signOut();
  };

  /**
   * Sends a password reset email.
   */
  const resetPassword = async (email: string) => {
    const redirectUrl = `${window.location.origin}/auth`;

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    });
    return { error };
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
