// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Use the "farms" schema by default. This avoids issues where the production
// Supabase instance does not include it in the search path, which previously
// caused failures like "cannot create new session" when inserting into tables.
export const supabase = createClient<Database, 'farms'>(supabaseUrl, supabaseAnonKey, {
  db: { schema: 'farms' },
});