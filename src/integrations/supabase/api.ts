import { supabase } from './client';
import { FullOnboardingSessionData, AgreementTypeEnum } from '@/types/onboarding';

// === TYPE DEFINITIONS ===
// Keep only API-specific result types that don't exist in database schema
export interface AbnValidationResult {
  isValid: boolean;
  businessName?: string;
  entityType?: string;
  businessState?: string;
  error?: string;
}

export interface FinalizeUploadResult {
  success: boolean;
  documentId?: string;
  message?: string;
  error?: string;
}

export interface EncryptionResult {
  success: boolean;
  message?: string;
  error?: string;
}

export interface DeleteDocumentResult {
  success: boolean;
  documentId?: string;
  message?: string;
  error?: string;
  details?: string;
}

export interface ImportAssetsCSVResult {
  success: boolean;
  importedCount?: number;
  errorCount?: number;
  errors?: string[];
  message?: string;
  error?: string;
}

export interface ProcessDigitalSignatureResult {
  success: boolean;
  message?: string;
  signatureStoragePath?: string;
  agreementId?: string;
  error?: string;
}

export interface SubmitOnboardingResult {
  success: boolean;
  message?: string;
  error?: string;
  errors?: string[];
  validationSummary?: {
    totalErrors: number;
    stepErrors: Record<string, number>;
  };
}

export interface MirrorToSharePointResult {
  success: boolean;
  message?: string;
  sharepointUrl?: string;
  error?: string;
}

// === SESSION MANAGEMENT ===
export const getOnboardingSession = async (): Promise<FullOnboardingSessionData | null> => {
  const { data, error } = await supabase.schema('public').rpc('get_onboarding_session_data');
  if (error) {
    console.error('Error fetching onboarding session:', error);
    throw error;
  }
  return data as unknown as FullOnboardingSessionData | null;
};

export const loadOrCreateOnboardingSession = async (): Promise<FullOnboardingSessionData> => {
  const { data, error } = await supabase.functions.invoke('load-or-create-onboarding-session', {
    body: {},
  });
  if (error) {
    console.error('Error loading or creating onboarding session:', error);
    throw error;
  }
  return data as FullOnboardingSessionData;
};

// === VALIDATION SERVICES ===
export const validateAbn = async (abn: string): Promise<AbnValidationResult> => {
  const { data, error } = await supabase.functions.invoke('validate-australian-business-number', {
    body: { abn },
  });
  if (error) throw error;
  return data as AbnValidationResult;
};

// === FILE UPLOAD SERVICES ===
export const initiateSecureUpload = async (payload: {
  sessionId: string;
  fileName: string;
  relatedToEntity: string;
  relatedToId: string;
}) => {
  const { data, error } = await supabase.functions.invoke('initiate-secure-file-upload', {
    body: payload,
  });
  if (error) throw error;
  return data as { signedUrl: string, filePath: string };
};

export const finalizeSecureUpload = async (payload: {
  sessionId: string;
  documentName: string;
  relatedToEntity: string;
  relatedToId: string;
  filePath: string;
}): Promise<FinalizeUploadResult> => {
  const { data, error } = await supabase.functions.invoke('finalize-secure-file-upload', {
    body: payload,
  });
  if (error) throw error;
  return data as FinalizeUploadResult;
};

// === ENCRYPTION SERVICES ===
export const invokeEncryptAndStoreSensitiveField = async (payload: {
  tableName: 'bookkeeping' | 'payments' | 'payroll';
  sessionId: string;
  fieldName: 'access_credentials' | 'bank_account_details' | 'encrypted_access_credentials';
  plainTextValue: string;
}): Promise<EncryptionResult> => {
  const { data, error } = await supabase.functions.invoke('encrypt-and-store-sensitive-field', {
    body: payload,
  });
  if (error) throw error;
  return data as EncryptionResult;
};

// === IMPORT SERVICES ===
export const importAssetsCSVApi = async (file: File): Promise<ImportAssetsCSVResult> => {
  const formData = new FormData();
  formData.append('file', file);

  const { data, error } = await supabase.functions.invoke('import-asset-registry-from-csv', {
    body: formData,
  });
  if (error) {
    console.error('Error importing assets CSV:', error);
    throw error;
  }
  return data as ImportAssetsCSVResult;
};

// === SIGNATURE SERVICES ===
export const processDigitalSignature = async (
  signatureDataUrl: string,
  sessionId: string,
  agreementType: AgreementTypeEnum
): Promise<ProcessDigitalSignatureResult> => {
  const { data, error } = await supabase.functions.invoke('process-digital-signature-and-consent', {
    body: {
      signatureData: signatureDataUrl,
      sessionId: sessionId,
      agreementType: agreementType,
    },
  });
  if (error) {
    console.error('Error processing digital signature:', error);
    throw error;
  }
  return data as ProcessDigitalSignatureResult;
};

// === SUBMISSION SERVICES ===
export const submitOnboardingSession = async (sessionId: string): Promise<SubmitOnboardingResult> => {
  if (!sessionId) {
    return { success: false, error: "Session ID is required for submission." };
  }

  try {
    const { data, error } = await supabase.functions.invoke('finalize-onboarding-submission', {
      body: { sessionId },
    });

    if (error) {
      console.error('Error submitting onboarding session:', error);
      return {
        success: false,
        error: error.message || 'Unknown submission error.',
        errors: Array.isArray(error.details) ? error.details : []
      };
    }

    return data as SubmitOnboardingResult;
  } catch (e: unknown) {
    console.error('Client-side error during submission:', e);
    const errorMessage = e instanceof Error ? e.message : 'A client-side error occurred.';
    return { success: false, error: errorMessage };
  }
};

// === SHAREPOINT MIRRORING SERVICES ===
export const mirrorToSharePoint = async (payload: {
  supabaseFilePath: string;
  sessionId: string;
  relatedToEntity: string;
  relatedToId: string;
  originalFileName: string;
}): Promise<MirrorToSharePointResult> => {
  try {
    const { data, error } = await supabase.functions.invoke('mirror-to-sharepoint', {
      body: payload,
    });

    if (error) {
      console.error('Error mirroring to SharePoint:', error);
      return {
        success: false,
        error: error.message || 'Failed to mirror file to SharePoint'
      };
    }

    return data as MirrorToSharePointResult;
  } catch (e: unknown) {
    console.error('Client-side error during SharePoint mirroring:', e);
    const errorMessage = e instanceof Error ? e.message : 'A client-side error occurred during SharePoint mirroring.';
    return { success: false, error: errorMessage };
  }
};

// === DOCUMENT DELETION SERVICES ===
export const deleteSecureDocument = async (payload: {
  documentId: string;
  sessionId: string;
}): Promise<DeleteDocumentResult> => {
  try {
    const { data, error } = await supabase.functions.invoke('delete-secure-document', {
      body: payload,
    });

    if (error) {
      console.error('Error deleting document:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete document'
      };
    }

    return data as DeleteDocumentResult;
  } catch (e: unknown) {
    console.error('Client-side error during document deletion:', e);
    const errorMessage = e instanceof Error ? e.message : 'A client-side error occurred during document deletion.';
    return { success: false, error: errorMessage };
  }
};

// Backwards compatibility
export { invokeEncryptAndStoreSensitiveField as encryptAndStoreField }; 