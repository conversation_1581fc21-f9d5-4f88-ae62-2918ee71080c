
  1. Verify Edge Function integration: Check that src/integrations/supabase/api.ts includes a function to call the encrypt-and-store-sensitive-field Edge Function. If it doesn't exist, create it:
  export const encryptAndStoreField = async (
    tableName: string,
    fieldName: string,
    value: string,
    recordId: string
  ): Promise<EncryptionResult> => {
    const { data, error } = await supabase.functions.invoke('encrypt-and-store-sensitive-field', {
      body: { tableName, fieldName, value, recordId }
    });

    if (error) throw new Error(error.message);
    return data;
  };
  2. Update OnboardingContext: Replace the placeholder encryptAndStoreCredentials implementation with actual API calls:
    - Remove console.log statements
    - Call the encryptAndStoreField function
    - Handle success and error responses properly
    - Update sessionData after successful encryption
  3. Update FinancialSystemsStep component:
    - Remove the "DEV NOTE" placeholder logic
    - Implement proper loading states during encryption
    - Add user feedback with toasts for success/failure
    - Ensure the UI shows when credentials are encrypted vs. not encrypted
  4. Add proper error handling:
    - Handle network errors during API calls
    - Show specific error messages for different failure scenarios
    - Allow users to retry encryption if it fails
    - Never expose the actual credential values in error messages
  5. Update UI indicators:
    - Show a secure icon or indicator when credentials are encrypted
    - Display appropriate loading states during encryption
    - Provide clear feedback about the security of stored credentials

  The implementation must follow the security-first principle where plaintext credentials are never sent directly to the database and all sensitive data goes through the Edge Function encryption process.



  1. Create submission API function: In src/integrations/supabase/api.ts, create a function to handle final submission:
  export const submitOnboardingSession = async (sessionId: string): Promise<{success: boolean, message?: string}> => {
    // Call Edge Function or direct database update to mark session as complete
    // Trigger any post-submission workflows
    // Return success/failure status
  };
  2. Update OnboardingContext: Add the submitOnboardingSession function to the context:
    - Implement proper API integration
    - Add submission state management
    - Handle success and error scenarios
    - Update session status after successful submission
  3. Implement data validation: Before submission, validate that all required data is present:
    - Check business registration completeness (ABN, business name, etc.)
    - Verify at least one farming activity is specified
    - Ensure required agreements are signed
    - Validate that critical fields are not empty
  4. Update AgreementsStep submission logic:
    - Replace alert() with actual submitOnboardingSession call
    - Add comprehensive error handling with specific error messages
    - Implement loading states during submission
    - Show success feedback with next steps information
  5. Add post-submission workflow:
    - Create a success page or redirect to appropriate dashboard
    - Send confirmation email if required
    - Update user session status
    - Clear local onboarding state
  6. Handle edge cases:
    - Network failures during submission
    - Partial submission scenarios
    - Session timeout during submission
    - Duplicate submission prevention

  The implementation should ensure data integrity and provide clear feedback to users about the submission status and any required next steps.



  1. Install signature pad dependency: Add a signature pad library to the project:
  npm install react-signature-canvas @types/react-signature-canvas
  2. Create DigitalSignatureSection component: Create src/components/onboarding/steps/review/DigitalSignatureSection.tsx:
    - Import SignatureCanvas from react-signature-canvas
    - Implement signature capture with clear and undo functionality
    - Add validation to ensure signature is not empty
    - Include proper labeling and instructions
    - Show loading state during signature processing
  3. Update API integration: Ensure src/integrations/supabase/api.ts includes the signature processing function:
  export const processDigitalSignature = async (
    signatureDataUrl: string,
    sessionId: string,
    agreementTypes: string[]
  ): Promise<ProcessDigitalSignatureResult> => {
    const { data, error } = await supabase.functions.invoke('process-digital-signature-and-consent', {
      body: { signatureDataUrl, sessionId, agreementTypes }
    });

    if (error) throw new Error(error.message);
    return data;
  };
  4. Update OnboardingContext: Add signature processing to the context:
    - Include processDigitalSignature function
    - Manage signature state and loading
    - Handle signature upload and storage path updates
    - Refresh sessionData after successful signature processing
  5. Integrate with AgreementsStep:
    - Add the DigitalSignatureSection component after terms checkboxes
    - Implement signature state management (pristine/dirty states)
    - Update submission validation to require valid signature
    - Process signature before final submission
    - Handle signature processing errors with user feedback
  6. Add proper validation logic:
    - Ensure signature pad is not empty before submission
    - Validate that all required agreements are checked
    - Show appropriate error messages for missing signatures
    - Disable submit button when signature requirements not met
  7. Handle signature processing workflow:
    - Convert signature to data URL format
    - Call the Edge Function to process and store signature
    - Update agreement records with signature storage path
    - Provide user feedback during processing

  The signature functionality must integrate seamlessly with the existing agreement workflow and follow the security requirements for storing signature data in Supabase Storage.



  1. Update PaymentInfoSection component: Replace the placeholder content in src/components/onboarding/steps/review/PaymentInfoSection.tsx:
    - Add form fields for bank account details (BSB, account number, account name)
    - Implement proper form validation for Australian banking details
    - Add secure input styling to indicate sensitive data
    - Include loading states during encryption
  2. Create payment management functions: Update OnboardingContext to include payment operations:
  const ensurePaymentRecordExists = async (step4Id: string) => {
    // Check if payment record exists for this step
    // Create if doesn't exist
    // Return payment record
  };

  const encryptAndStorePaymentDetails = async (
    bankDetails: string,
    paymentId: string
  ) => {
    // Call encrypt-and-store-sensitive-field Edge Function
    // Update payment record with encrypted details
    // Refresh sessionData
  };
  3. Add bank account validation:
    - Validate BSB format (6 digits, XXX-XXX)
    - Validate account number format
    - Ensure account name is provided
    - Add real-time validation feedback
  4. Implement secure data handling:
    - Never store plaintext bank details in component state
    - Use the encryption Edge Function for all sensitive data
    - Clear form data after successful encryption
    - Show encrypted status indicators
  5. Update AgreementsStep integration:
    - Ensure payment record is created when Step 4 initializes
    - Add payment validation to submission requirements
    - Handle payment processing errors gracefully
    - Show payment status in review section
  6. Add payment status display:
    - Show when payment details are configured vs. not configured
    - Display masked account details if already stored
    - Allow users to update payment information
    - Handle cases where encryption fails
  7. Implement proper error handling:
    - Handle encryption API failures
    - Validate bank details format before encryption
    - Provide clear error messages for payment issues
    - Allow retry for failed payment operations

  The payment implementation must follow strict security guidelines, ensuring bank details are never exposed in plaintext and all operations use the secure encryption workflow.



  1. Create DataMigrationSection component: Create src/components/onboarding/steps/review/DataMigrationSection.tsx:
    - Primary cloud storage dropdown (Google Drive, Dropbox, OneDrive, Other)
    - Filing system description text area
    - Access permissions checkboxes (Bookkeeping, Cloud Storage, Bank Feeds)
    - Integrate with OnboardingContext for data_migration and permissions table management
  2. Create CommunicationPreferencesSection component: Create src/components/onboarding/steps/review/CommunicationPreferencesSection.tsx:
    - Preferred communication methods checkboxes (Email, Phone, SMS)
    - Preferred contact times text input
    - Reporting frequency dropdown (Weekly, Fortnightly, Monthly)
    - Integrate with OnboardingContext for communication_preferences table management
  3. Update OnboardingContext with new data management:
  const ensureDataMigrationRecordExists = async (step4Id: string) => {
    // Create or retrieve data_migration record
  };

  const updateDataMigration = async (data: DataMigrationUpdate) => {
    // Update data_migration record
  };

  const upsertPermissions = async (step4Id: string, permissions: string[]) => {
    // Upsert permissions records based on selected permissions
  };

  const ensureCommunicationPreferencesExists = async (step4Id: string) => {
    // Create or retrieve communication_preferences record
  };
  4. Add validation and required fields handling:
    - Primary cloud storage is required
    - At least one communication method must be selected
    - Reporting frequency is required
    - Access permissions validation based on business needs
  5. Update AgreementsStep to include new sections:
    - Add DataMigrationSection before the terms and agreements
    - Add CommunicationPreferencesSection after data migration
    - Ensure proper loading states during record initialization
    - Add these sections to submission validation
  6. Implement proper data flow:
    - Auto-create records when Step 4 initializes
    - Real-time updates as users make selections
    - Proper error handling for data operations
    - Integration with existing sessionData structure
  7. Add UI consistency:
    - Use same Card layout as other sections
    - Consistent form styling and spacing
    - Proper loading states and disabled states
    - Clear labels and descriptions for each field
  8. Update submission workflow:
    - Validate that data migration preferences are set
    - Ensure communication preferences are configured
    - Include these sections in the final submission process
    - Handle validation errors appropriately

  The implementation should follow the same patterns as existing sections, with proper TypeScript interfaces, error handling, and integration with the OnboardingContext state management system.
