# Frontend Architecture - NewTerra Onboarding System

## Overview

The NewTerra onboarding frontend is built with React 18, TypeScript, and a modern component architecture designed for maintainability, performance, and user experience. The system uses a consolidated hook architecture with generic CRUD operations and comprehensive state management.

## Technology Stack

### Core Technologies
- **React 18**: Latest React with concurrent features
- **TypeScript**: Strict type checking with explicit database types
- **Vite**: Fast build tool with HMR and optimizations
- **React Router DOM**: Client-side routing with protected routes

### UI & Styling
- **shadcn/ui**: Component library built on Radix UI primitives
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library with consistent design

### State Management
- **React Context**: OnboardingContext for global session state
- **React Query (@tanstack/react-query)**: Server state management
- **React Hook Form**: Form state with validation
- **Zod**: Runtime type validation for forms

## Component Architecture

### Page Components (`src/pages/`)

#### `App.tsx` - Application Root
```typescript
// Provider hierarchy - CRITICAL ORDER
<QueryClientProvider> // React Query for server state
  <AuthProvider> // User authentication state
    <ProtectedRoute> // Route protection wrapper
      <ErrorBoundary> // Error catching for onboarding
        <OnboardingProvider> // Onboarding session management
```

#### `Onboarding.tsx` - Main Wizard Controller
- Orchestrates 4-step onboarding process
- Manages step navigation and progress tracking
- Handles global loading and error states
- Integrates with OnboardingContext for session management

**Step Configuration:**
```typescript
const STEPS = [
  { id: 1, title: "Business Profile", component: BusinessProfileStep },
  { id: 2, title: "Farm Operations", component: FarmOperationsStep },
  { id: 3, title: "Financial Systems", component: FinancialSystemsStep },
  { id: 4, title: "Agreements & Review", component: AgreementsStep }
];
```

#### `Auth.tsx` - Authentication Page
- Login, registration, and password reset
- Redirect handling with query parameters
- Responsive design with loading states

### Step Components (`src/components/onboarding/steps/`)

Each step component follows the consolidated architecture pattern:

#### `BusinessProfileStep.tsx` - Step 1
- Business registration with ABN/ACN validation
- Contact management with Australian phone formatting
- Address collection with postal standards
- Key staff information

**Architecture Pattern:**
```typescript
const BusinessProfileStep = () => {
  // 1. Step initialization
  const { isLoading, effectiveStepId } = useEnhancedStepInit({
    stepName: 'BusinessProfileStep',
    sessionId,
    contextLoading,
    existingStepId: getStepId(sessionData, 'step1'),
    ensureStepFunction: ensureStep1BusinessProfileRecordExists,
  });

  // 2. Entity list management
  const contactManagement = useEntityListManagement({
    stepId: effectiveStepId,
    tableName: 'contacts',
    entities: sessionData?.step1_businessProfile?.contacts || [],
    createDefaultEntity: (stepId) => ({ ... }),
    entityDisplayName: 'Contact'
  });

  // 3. Form management for single entities
  const businessRegistrationForm = useFormManagement({
    stepId: effectiveStepId,
    tableName: 'business_registration',
    initialData: sessionData?.step1_businessProfile?.businessRegistration,
    validateField: validateBusinessRegistrationField
  });
};
```

#### `FarmOperationsStep.tsx` - Step 2
- Farm activity tracking with categorization
- License and certification management
- Supplier relationship management
- Contract tracking linked to suppliers
- Chemical usage monitoring

#### `FinancialSystemsStep.tsx` - Step 3
- Bookkeeping software integration (encrypted credentials)
- Payroll system configuration (encrypted access)
- Asset registry with CSV import capability
- Financial data validation and formatting

#### `AgreementsStep.tsx` - Step 4
- Data migration planning
- Access permissions configuration
- Payment setup with encrypted banking details
- Communication preferences
- Digital signature capture
- Final submission workflow

### Form Components (`src/components/form_components/`)

#### Core Form Infrastructure

**`FormSection.tsx`** - Section wrapper
```typescript
interface FormSectionProps {
  title: string;
  description: string;
  children: React.ReactNode;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
  disabled?: boolean;
  variant?: 'default' | 'outlined' | 'elevated';
}
```

**`FormField.tsx`** - Individual field component
```typescript
interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'tel' | 'number' | 'date' | 'textarea' | 'select';
  value: string | number | boolean | string[] | null | undefined;
  onChange: (value: any) => void;
  onBlur?: () => void;
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  options?: SelectOption[];
  error?: string;
  isSaving?: boolean;
  validationRules?: ValidationConfig[];
}
```

**`AutoSaveFormField.tsx`** - Field with auto-save
- Built-in debounced saving (1000ms default)
- Real-time validation feedback
- Loading and error states
- Unsaved changes indicator

#### Entity-Specific Forms

Each form component handles a specific business entity:

- **`BusinessRegistrationForm.tsx`** - Business details, ABN/ACN validation
- **`ContactForm.tsx`** - Contact information with phone formatting
- **`AddressForm.tsx`** - Australian address standards
- **`ActivityForm.tsx`** - Farm activity categorization
- **`LicenseForm.tsx`** - License management with expiry tracking
- **`AssetForm.tsx`** - Asset registry with categories

## Consolidated Hook System

### Primary Hooks (`src/hooks/`)

#### `use-enhanced-step-init.ts`
**Purpose**: Step initialization and parent record management
**Features**:
- Race condition prevention
- Memory leak protection
- Timeout safeguards
- Comprehensive error handling

```typescript
export interface UseEnhancedStepInitProps {
  stepName: string;
  sessionId: string | null;
  contextLoading: boolean;
  existingStepId: string | null;
  ensureStepFunction: (sessionId: string) => Promise<{ id: string } | null>;
  timeoutMs?: number;
}
```

#### `use-entity-list-management.ts`
**Purpose**: Comprehensive CRUD operations for entity lists
**Features**:
- Add/update/delete operations
- Constraint checking (min/max entities)
- UUID validation
- Error handling per entity
- Loading states and notifications

```typescript
export interface UseEntityListManagementOptions<T, TInsert, TUpdate> {
  stepId: string | null;
  tableName: TableName;
  initialEntities?: T[] | null;
  createDefaultEntity: (stepId: string) => TInsert;
  validateEntity?: (entity: Partial<TUpdate>) => string | null;
  entityDisplayName: string;
  maxEntities?: number;
  minEntities?: number;
}
```

#### `use-form-management.ts`
**Purpose**: Form state management with auto-save and validation
**Features**:
- Multi-field form management
- Debounced auto-save
- Field-level validation
- Dirty state tracking
- Form reset functionality

### Specialized Hooks

#### `use-document-management.ts`
- Secure file upload workflow
- Multi-step process: initiate → upload → finalize → mirror
- Progress tracking and error handling

#### `use-selective-refresh.ts`
- Targeted session data refresh
- Performance optimization
- Prevents unnecessary re-renders

## State Management Architecture

### OnboardingContext (`src/contexts/OnboardingContext.tsx`)

**Core Responsibilities**:
1. Session data management
2. Generic CRUD operations
3. Step initialization functions
4. Helper utilities

**Generic CRUD Functions**:
```typescript
// Type-safe database operations
createRecord<T extends TableName>(tableName: T, data: TableInsert<T>): Promise<TableRow<T>[]>
updateRecord<T extends TableName>(tableName: T, recordId: string, data: TableUpdate<T>): Promise<TableRow<T>[]>
deleteRecord<T extends TableName>(tableName: T, recordId: string): Promise<boolean>
upsertRecord<T extends TableName>(tableName: T, data: TableInsert<T>, options?: { onConflict?: string }): Promise<TableRow<T>[]>
```

**Step Initialization Functions**:
```typescript
ensureStep1BusinessProfileRecordExists(sessionId: string): Promise<TableRow<'step_1_business_profile'>>
ensureStep2FarmOperationsRecordExists(sessionId: string): Promise<TableRow<'step_2_farm_operations'>>
ensureStep3FinancialSystemsRecordExists(sessionId: string): Promise<TableRow<'step_3_financial_systems'>>
ensureStep4AgreementsRecordExists(sessionId: string): Promise<TableRow<'step_4_agreements'>>
```

### AuthContext (`src/contexts/AuthContext.tsx`)

**Features**:
- Supabase Auth integration
- Session persistence
- Route protection
- Password reset functionality

**Auth Flow**:
1. `AuthProvider` manages global auth state
2. `ProtectedRoute` wraps protected pages
3. Unauthenticated users redirect to `/auth?redirectTo=/onboarding`
4. Post-login redirect to intended destination

## Type System

### Database Types (`src/types/database.types.ts`)
- Auto-generated from Supabase schema
- Source of truth for all database operations
- Explicit type casting required

### Business Types (`src/types/onboarding.ts`)
- Business-friendly interfaces
- Mapped from database types
- Component prop definitions

### Form Types (`src/types/form-components.ts`)
- Form-specific data structures
- Validation rule interfaces
- Component prop types

## Routing & Navigation

### Route Structure
```typescript
<Routes>
  <Route path="/" element={<Index />} />
  <Route path="/auth" element={<Auth />} />
  <Route path="/onboarding" element={
    <ProtectedRoute>
      <ErrorBoundary>
        <OnboardingProvider>
          <Onboarding />
        </OnboardingProvider>
      </ErrorBoundary>
    </ProtectedRoute>
  } />
  <Route path="*" element={<NotFound />} />
</Routes>
```

### Navigation Logic
- `currentStep` stored in database (`onboarding_sessions.current_step`)
- `updateCurrentStep()` persists progress
- Step validation controls navigation
- Back/forward navigation with validation

## Performance Optimization

### React Optimization
- `React.memo` for entity list items
- Proper dependency arrays in hooks
- Debounced form updates (1000ms default)
- Selective refresh patterns

### Bundle Optimization
- Code splitting with dynamic imports
- Tree shaking with ES modules
- Asset optimization with Vite
- Component lazy loading

### State Optimization
- Selective context updates
- Minimal re-renders
- Efficient state updates
- Memory leak prevention

## Error Handling

### Error Boundary Strategy
- `ErrorBoundary` wraps critical components
- Graceful fallback UI for crashes
- Error reporting with context
- User-friendly error messages

### Validation Strategy
- Client-side validation for UX
- Server-side validation for security
- Real-time feedback
- Australian business format validation

### Toast Notification System
- Global toast provider via Sonner
- Consistent success/error messaging
- Auto-dismiss with appropriate durations
- Accessibility compliance

## Testing Strategy

### Component Testing
- React Testing Library for component behavior
- User interaction testing
- Accessibility testing
- Visual regression testing

### Hook Testing
- Custom hook testing with React Hooks Testing Library
- State management validation
- Error handling verification
- Performance testing

### Integration Testing
- End-to-end user flows
- API integration testing
- Database operation testing
- File upload testing

## Security Considerations

### Client-Side Security
- Input sanitization and validation
- XSS prevention with proper escaping
- CSRF protection via Supabase Auth
- Secure token handling

### Data Protection
- Sensitive data encryption via Edge Functions
- Secure file upload with signed URLs
- User data isolation with RLS
- Audit trail for data changes

This frontend architecture provides a scalable, maintainable, and secure foundation for the NewTerra onboarding system, with comprehensive state management, type safety, and user experience optimization.