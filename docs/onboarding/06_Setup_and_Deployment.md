# NewTerra Setup and Deployment Guide

**Version:** 2.1
**Audience:** All Developers

---

This guide provides the steps required to set up a local development environment, manage secrets, and deploy changes to the NewTerra Onboarding Wizard.

## 1. Local Development Setup

### Step 1: Clone the Repository
Clone the project repository (`newterra-lovable`) to your local machine.

### Step 2: Set Up the Frontend
1.  Navigate to the project root directory (`newterra-lovable`). The frontend is a Vite React app within this root.
2.  Create a `.env` file (or `.env.local`) by copying the `.env.example` file if one exists, or create it from scratch.
3.  Populate `.env` with the Supabase API URL and anonymous key.
    -   Navigate to your project in the [Supabase Dashboard](https://app.supabase.com/).
    -   Go to **Project Settings -> API**.
    -   Find the **Project URL** and the `anon` `public` key under **Project API Keys**.
    ```env
    VITE_SUPABASE_URL=YOUR_PROJECT_URL
    VITE_SUPABASE_ANON_KEY=YOUR_PROJECT_ANON_KEY
    ```
    (Ensure your Vite config `vite.config.ts` and Supabase client `src/integrations/supabase/client.ts` correctly use these environment variable names.)
4.  Install dependencies and start the Vite development server:
    ```bash
    npm install # or bun install, if using Bun
    npm run dev # or bun dev
    ```

### Step 3: Set Up the Backend (Supabase)

For a complete local setup, you should have the [Supabase CLI](https://supabase.com/docs/guides/cli) installed and run the project locally using `supabase start`. This is highly recommended for backend development to test migrations and Edge Functions.

If using a remote Supabase project (development, staging, or production):
1.  **Link Supabase Project:** Ensure your local CLI is linked to the correct Supabase project:
    ```bash
    supabase login # If not already logged in
    supabase link --project-ref <your-project-ref-from-dashboard-url>
    ```
2.  **Apply Migrations:** Apply all schema and data migrations from the `supabase/migrations` directory:
    ```bash
    supabase db push # For development/staging. For production, consider manual review of SQL.
    # OR, to reset and apply all migrations (destructive, for local dev only!):
    # supabase db reset 
    ```
    See Section 3.3 for key migration files.
3.  **Deploy Edge Functions:** Deploy all functions from `supabase/functions/`:
    ```bash
    supabase functions deploy
    ```
4.  **Create Storage Bucket (if not in migrations):** If not already handled by a migration script (e.g., `supabase/migrations/0003_storage_setup.sql`), manually create a **private** storage bucket named `farms` via the Supabase Dashboard.
5.  **Configure CORS:** For local frontend development (e.g., `http://localhost:5173` for Vite default), add your local URL to the CORS allow-list in `Supabase Dashboard -> Project Settings -> API -> CORS settings`.

---
## 2. Secrets and Environment Management

All sensitive keys used by the backend **MUST** be stored in Supabase Vault. They should never be hardcoded in files like `supabase/config.toml` or function code.

**Instructions:**
1.  Navigate to your project in the [Supabase Dashboard](https://app.supabase.com/).
2.  Go to **Project Settings -> Edge Functions -> Add New Secret**.
3.  Add the following secrets:

| Secret Name        | Description                                                                                                    |
| ------------------ | -------------------------------------------------------------------------------------------------------------- |
| `ASIC_API_KEY`     | The API key for the Australian Business Number lookup service. Used by `supabase/functions/validate-australian-business-number/index.ts`.  |
| `PGCRYPTO_KEY`     | A strong, randomly generated 32-character string for encrypting and decrypting sensitive data in the database. Used by `supabase/functions/encrypt-and-store-sensitive-field/index.ts` via the `public.update_encrypted_field` SQL function. |

**How to Generate a Secure Encryption Key:**
You can use any password generator or run the following command in your terminal:
```bash
openssl rand -base64 32
```
*Note: The secret was previously named `ENCRYPTION_KEY`. It has been renamed to `PGCRYPTO_KEY` to be more specific.*

---
## 3. Deployment & Migrations

### 3.1. Deploying Edge Functions
(As described in Step 1.3)
1.  Ensure you are logged into the Supabase CLI: `supabase login`
2.  Link your local project: `supabase link --project-ref <your-project-id>` (if not already done)
3.  Deploy all functions from `supabase/functions/`:
    ```bash
    supabase functions deploy
    ```
    Or deploy a single function:
    ```bash
    supabase functions deploy <function-name> --no-verify-jwt
    ```
    *Note: `--no-verify-jwt` might be used if a function is designed for public access without user auth, though most in this project require auth.*

### 3.2. Deploying Database Migrations
When you make changes to your local Supabase database schema (e.g., by running `supabase start` and using Supabase Studio or `psql`), you generate migration files. These files, located in `supabase/migrations/`, capture your DDL changes.

1.  **Generate a new migration file (after local changes):**
    ```bash
    # Ensure supabase/config.toml has db.port correctly set if using a non-default local DB port
    supabase db diff -f <migration-name_describing_changes>
    ```
2.  **Apply migrations to your linked remote Supabase project:**
    ```bash
    supabase db push
    ```
    **WARNING:** For production environments, `supabase db push` should be used with extreme caution. It is safer to generate the SQL from the migration files using a tool or manually inspect the migration files, then run the SQL scripts in the Supabase SQL Editor after thorough review and backups.

3.  **Resetting local database (for development):**
    To apply all migrations from scratch to your local Supabase instance (destructive):
    ```bash
    supabase db reset
    ```
    After running `supabase db reset` or `supabase db push`, it is crucial to verify that the schema and functions have been correctly applied in your target Supabase environment (local or remote dashboard).

### 3.3. Key Migration Files
Located in `supabase/migrations/`:
-   `0000_initial_schema.sql`: Defines core tables, columns, relationships, and **ENUM types** (`farms.activity_type_enum`, `farms.agreement_type_enum`).
-   `0001_rls_policies.sql`: Contains all Row-Level Security policies for the tables.
-   `0002_edge_functions.sql`: Creates and configures critical PostgreSQL helper functions used by Edge Functions (e.g., `public.get_onboarding_session_data`, `public.update_encrypted_field`).
-   `0003_storage_setup.sql`: Sets up the `farms` storage bucket and its RLS policies.

### 3.4. ⚠️ Lead Developer Action: Post-Migration Verification

After applying migrations to any environment (especially staging or production, but also useful for local dev after a `reset`), the Lead Developer or a designated senior developer **MUST** manually verify the following in the Supabase Dashboard SQL Editor and Table Editor:

1.  **Schema Changes (from `0000_initial_schema.sql` and subsequent migrations):**
    *   `farms.business_registration`: Includes `phone_number TEXT NULL` with `chk_br_phone_format` constraint.
    *   `farms.activity_type_enum`: Exists with values: `('Cropping', 'Livestock', 'Mixed Farming', 'Horticulture', 'Dairy', 'Aquaculture', 'Forestry', 'Other')`.
    *   `farms.agreement_type_enum`: Exists with values: `('Service Agreement', 'Privacy Policy', 'Direct Debit', 'Terms and Conditions')`.
    *   `farms.activities`: `activity_type` column uses `farms.activity_type_enum`.
    *   `farms.agreements`: `agreement_type` column uses `farms.agreement_type_enum`.
    *   `farms.contracts`: Includes `contract_type TEXT NULL`.
    *   `farms.payroll`: Has `is_access_to_software_granted BOOLEAN NOT NULL DEFAULT false` and `encrypted_access_credentials BYTEA`.
    *   `farms.payments`: Does NOT have the `has_signed_direct_debit` column.
2.  **SQL Functions (from `0002_edge_functions.sql`):**
    *   `public.update_encrypted_field`: Verify its definition. Specifically, check that the `CASE` statement for `p_table_name` correctly routes `'payroll'` to use `step_3_financial_systems` and `step_3_id` for ownership checks. Also, verify that its internal `v_allowed_tables` array includes `'payroll'` and its `v_allowed_fields` array includes `'encrypted_access_credentials'`. Confirm that if the Edge Function sends `tableName: 'payroll'` and `fieldName: 'encrypted_access_credentials'`, the `public.update_encrypted_field` SQL function correctly updates the `farms.payroll.encrypted_access_credentials` column.
3.  **RLS Policies (from `0001_rls_policies.sql`):**
    *   Spot-check RLS policies on a few key tables (e.g., `onboarding_sessions`, a step parent table like `step_1_business_profile`, and a child data table like `contacts`) to ensure they are active and correctly defined.

This manual check is crucial to confirm that the automated migration process has resulted in the intended database state. Update this document (`docs/06_Setup_and_Deployment.md` or `.cursor/docs/onboarding06_Setup_and_Deployment.md`) if any discrepancies or additional verification steps are identified. 