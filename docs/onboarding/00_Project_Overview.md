# NewTerra Onboarding System - Project Overview

## Project Vision & Mission

NewTerra is an Ag-tech B2B startup helping agriculture businesses digitize and optimize their operations. The **Onboarding Wizard** is the primary tool for achieving this mission, designed to collect comprehensive business, operational, and financial data from new farming clients.

**ABN**: ***********

## System Architecture Overview

### Technology Stack
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui with Radix UI primitives + Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Edge Functions + Storage)
- **State Management**: React Context (OnboardingContext) + React Query
- **Forms**: React Hook Form + Zod validation with auto-save
- **Testing**: Vitest + Testing Library

### Core Features
- **Multi-Step Wizard**: 4-step onboarding process with progress tracking
- **Auto-Save Functionality**: Real-time form saving with debounced updates
- **Australian Business Validation**: ABN/ACN validation and phone formatting
- **Secure File Management**: Multi-step upload with <PERSON>hare<PERSON>oint mirroring
- **Digital Signatures**: Secure signature capture and storage
- **Encrypted Credentials**: Secure storage of sensitive business data

## Onboarding Process Flow

### Step 1: Business Profile
- Business registration details (ABN, ACN, structure)
- Primary contacts and key staff
- Business addresses (property and postal)
- Australian business number validation

### Step 2: Farm Operations
- Farm activities and operations
- Licenses and certifications
- Supplier relationships
- Contracts and agreements
- Chemical usage tracking

### Step 3: Financial Systems
- Bookkeeping software integration
- Payroll system details
- Asset registry management
- Financial credential encryption

### Step 4: Agreements & Review
- Data migration planning
- Access permissions
- Payment setup
- Communication preferences
- Digital signature capture
- Final submission

## Database Architecture

### Hierarchical Design
The database follows a strict hierarchical, session-centric design:

1. **Root**: `onboarding_sessions` (linked to authenticated user)
2. **Step Parents**: 
   - `step_1_business_profile`
   - `step_2_farm_operations` 
   - `step_3_financial_systems`
   - `step_4_agreements`
3. **Child Entities**: All data tables link to their respective step parent

### Security Model
- **Row-Level Security (RLS)**: Default-deny policies on all tables
- **User Isolation**: All data scoped to authenticated user via `auth.uid()`
- **Encryption**: Sensitive data encrypted via Edge Functions before storage
- **Audit Trail**: Comprehensive logging and change tracking

## Technical Specifications

### Australian Business Requirements
- **ABN Format**: XX XXX XXX XXX (11 digits with validation)
- **ACN Format**: XXX XXX XXX (9 digits)
- **Phone Format**: +61X XXXX XXXX (Australian mobile/landline)
- **Address Standards**: Australian postal address formatting

### Performance Targets
- **Page Load**: < 2 seconds initial load
- **Auto-Save**: 1 second debounce on form fields
- **File Upload**: Multi-part upload with progress tracking
- **Data Sync**: Real-time session state management

### Compliance & Security
- **Data Privacy**: GDPR/Privacy Act compliant data handling
- **Business Security**: Encrypted credential storage
- **File Security**: Private storage with signed URLs
- **Access Control**: Granular permission management

## Development Approach

### Code Organization
- **Consolidated Hooks**: Unified hook system for form and entity management
- **Type Safety**: Explicit TypeScript types from database schema
- **Component Reusability**: Generic components with configuration
- **Error Handling**: Comprehensive error boundaries and user feedback

### Quality Standards
- **ESLint Compliance**: Strict TypeScript and React rules
- **Test Coverage**: Unit tests for business logic and validation
- **Code Reviews**: Mandatory review process for all changes
- **Documentation**: Comprehensive inline and external documentation

## Integration Points

### External Systems
- **Australian Business Register**: ABN validation and lookup
- **SharePoint**: Document mirroring and backup
- **Accounting Software**: Bookkeeping system integration
- **Payroll Systems**: Payroll software connectivity

### API Endpoints
- **Edge Functions**: Serverless business logic and integrations
- **Database RPCs**: Optimized data retrieval functions
- **Storage APIs**: Secure file upload and management
- **Auth Services**: User authentication and session management

## Success Metrics

### User Experience
- **Completion Rate**: > 85% onboarding completion
- **Time to Complete**: < 30 minutes average
- **Error Rate**: < 5% validation errors
- **User Satisfaction**: > 4.5/5 rating

### Technical Performance
- **Uptime**: 99.9% availability
- **Response Time**: < 500ms API responses
- **Data Accuracy**: 100% validation compliance
- **Security**: Zero data breaches

This overview provides the foundation for understanding the NewTerra onboarding system architecture and implementation approach.