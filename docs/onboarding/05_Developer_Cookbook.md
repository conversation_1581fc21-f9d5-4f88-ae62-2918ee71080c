# NewTerra Developer Cookbook

**Version:** 1.3
**Audience:** Full-Stack Developers, Frontend Developers, Backend Developers

---

This document provides practical, **prescriptive** recipes for common development tasks. Following these checklists is **mandatory** to ensure consistency, security, and alignment with the project's architecture.

## 1. How to Add a New Field to a Form

**Goal:** Add a new non-sensitive field to an existing wizard step (e.g., adding "Business Website" to the "Business Profile" step, which is part of the `business_registration` table).

This is a **six-step, mandatory checklist**. Do not change the order.

1.  **Step 1: [Backend] Update the Database Schema & Regenerate Types.**
    -   In `supabase/migrations/0000_initial_schema.sql` (or a new migration file if post-initial setup), add the new column to the relevant table (e.g., `website_url TEXT NULL` to `farms.business_registration`).
    -   **Decision:** Does this field require a `CHECK` constraint? If so, add it.
    -   Apply the schema change to your local database (e.g., via `supabase db push` or `supabase migration up`).
    -   **CRITICAL: Regenerate `src/types/database.types.ts`**:
        ```bash
        npx supabase gen types typescript --project-id <your-project-id> --schema farms > src/types/database.types.ts
        ```
        This updates `TableRow<T>`, `TableInsert<T>`, and `TableUpdate<T>` for all tables, which are used by the generic context functions in `OnboardingContext.tsx`.

2.  **Step 2: [Backend] Update the Data Access Function (If Necessary).**
    -   The primary data retrieval function `public.get_onboarding_session_data()` (defined in `supabase/migrations/0002_edge_functions.sql`) uses `to_jsonb()` which automatically includes all columns from the tables it queries. For simple field additions to existing tables already part of this function's SELECT statements, **no changes to this SQL function are typically needed.** The new field will automatically be part of the `sessionData` fetched by `OnboardingContext`.

3.  **Step 3: [Documentation] Update the Frontend Data Contract.**
    -   **Critical step.** Open `03_Frontend_Architecture.md`. Find the correct table in "Component Implementation Contracts."
    -   Add a new row for your field, detailing `Component & DB`, `Client-Side Validation & Transformation`, `State Management Contract` (noting if it's part of an auto-created record and how its UI is controlled by the step's `isStepInitializing` state and parent ID availability), and `Backend Error Handling`.

4.  **Step 4: [Frontend] Update Onboarding Context & Types.**
    -   **`src/types/database.types.ts`**: This was regenerated in Step 1. The generic context functions (`createRecord`, `updateRecord`, `ensureLinkedRecord`, etc.) will now recognize the new field due to these updated base types.
    -   **`src/types/onboarding.ts`**: If you have custom view model interfaces that *extend* or *compose* the base `TableRow<T>` types (e.g., `FullOnboardingSessionData`), update them here to include the new field if it's directly part of the session data structure. However, for individual table operations, components will rely on types from `database.types.ts`.
    -   **`src/contexts/OnboardingContext.tsx`**:
        *   **No changes needed for generic functions**: `createRecord`, `updateRecord`, `upsertRecord`, `deleteRecord`, `ensureLinkedRecord` are already generic and will work with the updated types from `database.types.ts`.
        *   **Component Responsibility for `ensureLinkedRecord`**: If the new field is part of a primary data table that is auto-created by a component calling `ensureLinkedRecord` (e.g., `business_registration`), that component is responsible for ensuring its `defaultValues` payload (of type `TableInsert<T>`) for the `ensureLinkedRecord` call includes the new field (e.g., `website_url: null`).
        *   **Component Responsibility for Updates**: The component responsible for updating this field will call, for example, `updateRecord('business_registration', recordId, { website_url: '...' })`, where the payload conforms to `TableUpdate<'business_registration'>`.

5.  **Step 5: [Frontend] Implement the UI Component.**
    -   In the relevant React step component (e.g., `src/components/onboarding/steps/BusinessProfileStep.tsx` for `business_registration.website_url`):
        -   If the field belongs to a primary record that is auto-created by the component calling `ensureLinkedRecord` (e.g., `business_registration`):
            -   The input field will be populated from `sessionData` (e.g., `sessionData.step1_businessProfile.businessRegistration[0]?.website_url`).
            -   The UI for this field **MUST** be disabled while the step component's local `isStepInitializing` state is true.
            -   To initialize/ensure the record, the component calls `ensureLinkedRecord` with the correct `tableName`, `linkCondition`, `defaultValues` (including the new field, e.g., `website_url: null`), and `existingRecordFromSession`.
                ```typescript
                // Inside BusinessProfileStep.tsx, after ensureStep1BusinessProfileRecordExists:
                const step1Id = sessionData?.step1_businessProfile?.id;
                if (step1Id && isStepInitializing) { // Assuming isStepInitializing guards this
                  const bizRegData = await ensureLinkedRecord({
                    tableName: 'business_registration',
                    linkCondition: { step_1_id: step1Id },
                    defaultValues: { /* ...other defaults..., */ website_url: null },
                    existingRecordFromSession: sessionData?.step1_businessProfile?.businessRegistration?.[0]
                  });
                  // Update local state if needed, isStepInitializing to false
                }
                ```
        -   If the field is part of a new *child* record (e.g., a new field in the `contacts` form, which is a child of `step_1_business_profile`):
            -   The UI elements to *add* or *edit* these child records (and thus this new field) **MUST** only be enabled when the parent step record ID (e.g., `sessionData.step1_businessProfile.id`) is confirmed to be present in `sessionData` AND the step component's `isStepInitializing` state is false.
    -   The component's logic **MUST** strictly adhere to the contract defined in `03_Frontend_Architecture.md`.
    -   To save the data, the component calls the appropriate generic function from the context. For example, to update the website URL for an existing `business_registration` record:
        ```typescript
        // In BusinessProfileStep.tsx
        const { updateRecord, sessionData } = useOnboarding();
        const businessRegId = sessionData?.step1_businessProfile?.businessRegistration?.[0]?.id;
        
        const handleWebsiteChangeAndSave = async (newWebsiteUrl: string) => {
          if (businessRegId) {
            // Payload type TableUpdate<'business_registration'> is from database.types.ts
            const result = await updateRecord('business_registration', businessRegId, { website_url: newWebsiteUrl });
            // OnboardingContext's updateRecord handles refreshSessionData & toast
            if (!result) { /* handle error appropriately */ }
          }
        };
        ```
    -   If this were a new field in a *new* contact record (child table):
        ```typescript
        // In BusinessProfileStep.tsx, for adding a new contact
        const { createRecord, sessionData } = useOnboarding();
        const step1Id = sessionData?.step1_businessProfile?.id;

        const handleAddNewContactWithNewField = async (contactFormData: Omit<TableInsert<'contacts'>, 'step_1_id'>) => {
          if (step1Id) {
            // Component ensures it provides the step_1_id and the new field
            // contactFormData is assumed to be of a type that includes the new field
            const newContactPayload: TableInsert<'contacts'> = {
              ...contactFormData, // contains the new field
              step_1_id: step1Id
            };
            const result = await createRecord('contacts', newContactPayload);
            if (!result) { /* handle error */ }
          }
        };
        ```

6.  **Step 6: [Frontend] Write Tests.**
    -   Update unit tests for the component's state logic and its interaction with the generic context functions, ensuring correct payloads are passed.
    -   Add/update Storybook stories for the component with the new field.

**Note on Sensitive Fields:** If the new field were sensitive and required encryption (e.g., a new type of credential):
- Follow the database steps to add a `BYTEA` column.
- The `public.update_encrypted_field` SQL function would need to be updated by a backend developer to whitelist the new table and field if it's not already covered.
- The frontend component, after ensuring the parent record exists (via `ensureLinkedRecord`), would **not** use `updateRecord`. Instead, it would call the specific API helper `invokeEncryptAndStoreSensitiveField` (from `src/integrations/supabase/api.ts`) providing the `tableName`, `sessionId`, `fieldName` (for the `BYTEA` column), and the `plainTextValue`. The `OnboardingContext` does not provide a generic wrapper for this specific encryption Edge Function due to its specialized nature and security considerations. After a successful call, `refreshSessionData()` would be needed.

---
## 2. How to Implement a Component with Async Validation

**Goal:** Correctly implement an input field that requires asynchronous validation (e.g., ABN Input in `BusinessProfileStep.tsx`).

1.  **Step 1: Define the Contract.**
    -   Ensure the full contract is in `03_Frontend_Architecture.md`.
    -   Note that the parent record (e.g., `business_registration` for ABN) is ensured to exist by the frontend step component calling `ensureLinkedRecord({ tableName: 'business_registration', ...})` from `src/contexts/OnboardingContext.tsx` during its initialization logic. The UI for the ABN input itself should be disabled while the step is initializing (`isStepInitializing` is true from the component's local state).
    -   The `useAsyncValidation` hook (`src/hooks/useAsyncValidation.ts`) provides a standard state structure (`{ status, message }`).

2.  **Step 2: Implement the State Machine.**
    -   Use the state from `useAsyncValidation`: `const { validationState, runValidation } = useAsyncValidation(...);` which internally manages `status` and `message`.

3.  **Step 3: Implement the Async Call and State Updates.**
    -   As defined, using debounced `useEffect` to call the Edge Function (e.g., `validate-australian-business-number` from `supabase/functions/validate-australian-business-number/index.ts`, likely invoked via a helper in `src/integrations/supabase/api.ts`). The `runValidation` function returned by `useAsyncValidation` should be invoked.

4.  **Step 4: Bind UI to State.**
    -   Conditionally render feedback based on `validationState.status` and `validationState.message`. The input field and form submission should be disabled if `validationState.status === 'validating'` or if the overall step component's `isStepInitializing` state is true.

---
## 3. How to Debug an End-to-End Data Flow

**Goal:** Systematically find the point of failure.

1.  **Tier 1: Browser Network Tab.** (As defined)
2.  **Tier 2: Supabase Edge Function Logs.** (As defined)
3.  **Tier 3: Supabase Database & RLS.**
    -   If RLS is suspected, verify parent records. The frontend orchestration (the `ensureStepX...RecordExists` context functions, and component calls to `ensureLinkedRecord`) *should* create these.
    -   If a parent step record (e.g., `step_1_business_profile`) or a primary data record (e.g., `business_registration`) is unexpectedly missing:
        1.  Check the logic in `src/contexts/OnboardingContext.tsx` (`loadOrCreateSession`, `createNewSession`, and the generic `ensureLinkedRecord` which is called by `ensureStepX...RecordExists` wrappers and directly by components for primary data records).
        2.  Check the relevant step component's `useEffect` hook that calls these context functions (e.g., in `src/components/onboarding/steps/BusinessProfileStep.tsx`, the effect calling `ensureStep1BusinessProfileRecordExists` and then the component's own call to `ensureLinkedRecord` for `business_registration`). Is it firing correctly? Are the correct IDs (`sessionId`, `step_1_id`, etc.) being passed to `linkCondition` and `defaultValues` within the `ensureLinkedRecord` call?
        3.  Use the SQL Editor to verify data as per the existing recipe:
            ```sql
            -- Check parent step record for a given session_id
            SELECT * FROM farms.step_1_business_profile WHERE onboarding_session_id = 'YOUR_SESSION_ID';
            -- Check primary data record, e.g., business_registration, for a given step_1_id
            SELECT * FROM farms.business_registration WHERE step_1_id = 'THE_STEP_1_ID_FROM_ABOVE';
            ```

4.  **Tier 4: The Documentation.** (As defined, especially `03_Frontend_Architecture.md` and `04_Full_Stack_Integration_Guide.md` for sequence diagrams showing interactions with generic context functions).

## Backend Recipes

### Recipe: Adding a New Table with RLS

**Goal:** Add a new table to an existing step (e.g., "Water Sources" to Step 2, as a child of `step_2_farm_operations`).

1.  **[Backend] Define the Schema:** (As defined in a new `supabase/migrations/....sql` file or existing one like `0000_initial_schema.sql`)
2.  **[Backend] Define RLS Policies:** (As defined in `supabase/migrations/....sql`, typically `0001_rls_policies.sql` or a new policy file)
3.  **[Backend] Update Session Data Function:** Add the new table to the `public.get_onboarding_session_data` function in `supabase/migrations/0002_edge_functions.sql` so its data is included in `sessionData` fetched by `OnboardingContext`.
4.  **[Backend] CRITICAL: Regenerate `src/types/database.types.ts`**:
    ```bash
    npx supabase gen types typescript --project-id <your-project-id> --schema farms > src/types/database.types.ts
    ```
    This makes the new table and its types (`TableRow<NewTable>`, `TableInsert<NewTable>`, etc.) available to the generic context functions.
5.  **[Documentation] Update Documentation:**
    -   Update `01_Backend_Architecture.md` (schema diagram, table list, referencing the migration file).
    -   Update `03_Frontend_Architecture.md`. This is crucial for frontend implementation:
        -   Define the new table's UI fields and interaction contracts.
        -   If this new table is a **primary data record** (one-to-one with its step parent, e.g., if a step could only have one "Water Source Profile"): Its creation would be handled by the step component (e.g., `FarmOperationsStep.tsx`) calling `ensureLinkedRecord({ tableName: 'water_source_profiles', linkCondition: { step_2_id: ... }, defaultValues: {step_2_id: ..., /* other defaults */}, ... })` during its initialization.
        -   If this new table is a **child data record** (one-to-many, e.g., multiple "Water Sources"): The step component (`FarmOperationsStep.tsx`) would not auto-create these via `ensureLinkedRecord` for each one. Instead, its UI for adding/managing "Water Sources" (e.g., a form to add a new water source) would only be enabled after `sessionData.step2_farmOperations.id` is confirmed and `isStepInitializing` is false. The form submission would gather data (including the `step_2_id`) and call `createRecord('water_sources', newWaterSourceData)`.
6.  **[Frontend] Implement OnboardingContext Changes:**
    -   **No changes needed for generic functions** (`createRecord`, `updateRecord`, `deleteRecord`, `upsertRecord`, `ensureLinkedRecord`) in `OnboardingContext.tsx`. They will work with the new table name and its types from the regenerated `database.types.ts`.
7.  **[Frontend] Implement UI Component Changes:**
    -   Based on whether it's a primary or child table (see point 5), implement the UI in the step component (e.g., `FarmOperationsStep.tsx`).
    -   If primary: The component calls `ensureLinkedRecord({ tableName: 'new_primary_table', ...})` in its initialization logic. Data is displayed from `sessionData.step2_farmOperations.new_primary_table[0]?` (assuming the structure from `get_onboarding_session_data`). Updates use `updateRecord('new_primary_table', recordId, data)`.
    -   If child: The component provides UI to add new records (e.g., a button opening a modal form). The form submission handler gathers data, includes the `step_2_id` from `sessionData.step2_farmOperations.id`, and calls `createRecord('new_child_table', dataWithStep2Id)`. Existing child records are mapped from `sessionData.step2_farmOperations.new_child_table_collection` (or similar, depending on `get_onboarding_session_data` structure) and can be updated with `updateRecord` or deleted with `deleteRecord`.
    -   Ensure conditional UI enablement based on `isStepInitializing` and parent ID presence.

---

## Full-Stack Recipes

### Recipe: Adding a New Field Requiring an Edge Function
(e.g., validating a "Chemical Handling License Number" for a `licenses` record)

1.  **[Backend] Create the Edge Function:** (As defined, e.g., in `supabase/functions/your-new-function/index.ts`)
2.  **[Frontend] Implement the UI and State:**
    -   Follow the updated "How to Add a New Field to a Form" recipe (Recipe #1 above) for adding the "Chemical Handling License Number" field to the `licenses` table UI. Remember `licenses` is a child table of `step_2_farm_operations`. Adding/editing a license (and its fields) is user-initiated after `step_2_farm_operations.id` is confirmed and Step 2 (e.g. `src/components/onboarding/steps/FarmOperationsStep.tsx`) is initialized. The component will use `createRecord('licenses', ...)` or `updateRecord('licenses', ...)`.
    -   The state for the async validation of this new field will use `useAsyncValidation` as per Recipe #2.
3.  **[Frontend] Trigger the Function:** (As defined - debounced `useEffect` from the component handling the license form, calling the Edge Function from `supabase/functions/your-new-function/index.ts` likely via a helper in `src/integrations/supabase/api.ts`).
4.  **[Documentation] Update the API Reference:** (As defined for `02_Edge_Functions_API.md`).

### Recipe: Adding a New Secure Document Upload

**Goal:** Add file upload for a new entity (e.g., "Grant Application PDF" for a new `grants` table, child of `step_4_agreements`).

1.  **[Backend & Frontend Setup for `grants` table]:**
    -   Follow "Recipe: Adding a New Table with RLS" (Recipe #4 above) to set up the `farms.grants` table (child of `step_4_agreements`) in `supabase/migrations/....sql`, define RLS policies, update `public.get_onboarding_session_data`, and **critically, regenerate `src/types/database.types.ts`**.
    -   Update `03_Frontend_Architecture.md` for the `grants` table fields.
    -   In `src/components/onboarding/steps/AgreementsStep.tsx`, ensure UI for adding/managing grants is enabled only after `sessionData.step_4_agreements.id` is confirmed and `isStepInitializing` is false.
    -   **No new context functions are needed in `OnboardingContext.tsx`.** Components will use generic `createRecord('grants', ...)` and `updateRecord('grants', ...)` for grant data, and `uploadAndFinalizeDocument` for the file.

2.  **[Frontend] Create UI for Grant Form & File Upload:**
    -   In `src/components/onboarding/steps/AgreementsStep.tsx` (or a sub-component), add the form for grant details and the `<input type="file" />`.

3.  **[Frontend] Implement Secure Upload Workflow (using `uploadAndFinalizeDocument` from `OnboardingContext`):**
    a.  User fills grant form. On submit, the component calls `createRecord('grants', newGrantData)` from `OnboardingContext`. `newGrantData` is of type `TableInsert<'grants'>` and **must include** the `step_4_id` obtained from `sessionData.step4_agreements.id`.
    b.  From the result of `createRecord` (which is an array `TableRow<'grants'>[]`), get the `new_grant_id` (e.g., `createdGrants[0].id`).
    c.  Component then calls `uploadAndFinalizeDocument(file, 'grants', new_grant_id)` from the `OnboardingContext`.
        ```typescript
        // In AgreementsStep.tsx or sub-component
        const { createRecord, uploadAndFinalizeDocument, sessionData } = useOnboarding();
        const step4Id = sessionData?.step4_agreements?.id;
        // sessionId is also available from useOnboarding() or sessionData.onboardingSession.id

        const handleSaveGrantAndUpload = async (grantFormData: Omit<TableInsert<'grants'>, 'step_4_id'>, file: File) => {
          if (!step4Id || !sessionData?.onboardingSession?.id || !file) {
            // toast({ title: "Error", description: "Cannot save grant or upload file: Missing critical IDs or file."});
            return;
          }

          const grantPayload: TableInsert<'grants'> = { ...grantFormData, step_4_id: step4Id };
          const createdGrants = await createRecord('grants', grantPayload);

          if (createdGrants && createdGrants.length > 0 && createdGrants[0].id) {
            const newGrantId = createdGrants[0].id;
            // The uploadAndFinalizeDocument function in context will use sessionData.onboardingSession.id
            const uploadResult = await uploadAndFinalizeDocument(file, 'grants', newGrantId);
            if (uploadResult.success) {
              // Document uploaded and logged successfully
              // OnboardingContext's uploadAndFinalizeDocument already calls refreshSessionData
              // toast({ title: "Success", description: "Grant saved and file uploaded."});
            } else {
              // Handle upload error (uploadResult.error will have details)
              // toast({ title: "Upload Error", description: uploadResult.error });
            }
          } else {
            // Handle grant creation error (createRecord returns null on error)
            // toast({ title: "Save Error", description: "Failed to save grant details."});
          }
        };
        ```
    d.  The `uploadAndFinalizeDocument` context function orchestrates the calls to `initiate-secure-file-upload` and `finalize-secure-file-upload` Edge Functions and subsequently calls `refreshSessionData` to update the UI.

4.  **[Frontend] Display Uploaded Document:** (As defined, UI reads from `sessionData.documents` collection, which is updated by `refreshSessionData` after `uploadAndFinalizeDocument` succeeds. The document can be related to the grant via `related_to_entity = 'grants'` and `related_to_id = new_grant_id`). 