# NewTerra Backend Architecture

**Version:** 2.1
**Audience:** Backend Developers, Full-Stack Developers, QA Engineers, System Architects

---

This document provides a comprehensive, implementation-level overview of the V2.1 backend system. It is designed for deep analysis, quality assurance, and architectural review. The primary schema definition resides in `supabase/migrations/0000_initial_schema.sql`.

## 1. System Architecture: A Hierarchical & Session-Centric Approach

The backend is built entirely on Supabase. The architecture is **session-centric**, meaning every piece of data collected is rooted in the `farms.onboarding_sessions` table. This table links directly to the authenticated user (`auth.users`), creating a secure and isolated data container for each user's onboarding journey.

The V2.1 schema introduces a strict **hierarchical structure**. Four parent tables, one for each step of the wizard, are direct children of the session. The frontend application (orchestrated via `src/contexts/OnboardingContext.tsx` using its generic `ensureLinkedRecord` and specific `ensureStepX...` wrapper functions) ensures these parent step records, and any essential primary data records for that step (e.g., `business_registration` for Step 1, also typically managed via `ensureLinkedRecord`), are automatically created or verified upon the user navigating to each respective step. All other data tables are children of their respective step parent, creating a clear, logical data tree that mirrors the UI flow and supports backend RLS policies (defined in `supabase/migrations/0001_rls_policies.sql`).

## 2. Database Schema (`farms`)

The `farms` schema (defined in `supabase/migrations/0000_initial_schema.sql`) is designed to be highly normalized and secure, with data integrity enforced at the database level through constraints and foreign keys. All tables include `created_at` and `updated_at` timestamps, automatically managed by the `public.trigger_set_timestamp` trigger. All Foreign Key relationships are indexed to ensure optimal join performance.

### 2.1. V2 Schema Diagram
```mermaid
graph TD
    A[auth.users] --> B(onboarding_sessions);

    subgraph \"Step 1: Business Profile\"
        B --> S1(step_1_business_profile);
        S1 --> C1_1(business_registration);
        S1 --> C1_2(addresses);
        S1 --> C1_3(contacts);
    end

    subgraph \"Step 2: Farm Operations\"
        B --> S2(step_2_farm_operations);
        S2 --> C2_1(activities);
        S2 --> C2_2(licenses);
        S2 --> C2_3(suppliers);
        S2 --> C2_4(contracts);
        S2 --> C2_5(chemical_usage);
    end

    subgraph \"Step 3: Financial Systems\"
        B --> S3(step_3_financial_systems);
        S3 --> C3_1(bookkeeping);
        S3 --> C3_2(payroll);
        S3 --> C3_3(assets);
    end

    subgraph \"Step 4: Agreements\"
        B --> S4(step_4_agreements);
        S4 --> C4_1(data_migration);
        S4 --> C4_2(permissions);
        S4 --> C4_3(agreements);
        S4 --> C4_4(payments);
        S4 --> C4_5(communication_preferences);
    end
    
    B --> D(documents);

    style B fill:#d4edda,stroke:#155724,stroke-width:2px
    style S1 fill:#cce5ff,stroke:#004085,stroke-width:2px
    style S2 fill:#cce5ff,stroke:#004085,stroke-width:2px
    style S3 fill:#cce5ff,stroke:#004085,stroke-width:2px
    style S4 fill:#cce5ff,stroke:#004085,stroke-width:2px
```

### 2.2. V2 Table Architecture & Notes
The following table details key tables (defined in `supabase/migrations/0000_initial_schema.sql`) and the architectural reasoning behind their V2.1 design.

| Table Name | Role & Key Attributes | Architectural Notes & Data Lifecycle |
| :--- | :--- | :--- |
| `onboarding_sessions` | **Root Table.** `user_id` links to `auth.users`. `ON DELETE CASCADE` is enabled. | **Lifecycle:** The `ON DELETE CASCADE` is a critical data privacy feature. If a user is deleted from `auth.users`, their entire onboarding history, including all child records in all step tables, is automatically and completely purged from the database. The frontend (`src/contexts/OnboardingContext.tsx` using its `loadOrCreateSession` logic, which internally create a session) initiates the creation of this record if one doesn't exist for the user. |
| `step_1_business_profile`<br>`step_2_farm_operations`<br>`step_3_financial_systems`<br>`step_4_agreements` | **Parent "Step" Tables.** Each has a `UNIQUE` foreign key to `onboarding_sessions.id` with `ON DELETE CASCADE`. | **Reasoning:** These four tables create the hierarchical structure. They act as parent containers for all the data collected in a specific step of the wizard. The `UNIQUE` key enforces a strict one-to-one relationship with the session, ensuring each session has only one of each step record. **Lifecycle:** The frontend application, via the `OnboardingContext` (`src/contexts/OnboardingContext.tsx`), automatically ensures these records are created when a user first accesses the corresponding step in the wizard if they do not already exist. This is typically handled by `ensureStepX...RecordExists` functions which internally use the generic `ensureLinkedRecord` function. This is foundational for RLS on child tables. |
| `business_registration` | **Child of Step 1.** Stores ABN, ACN, etc. Includes `phone_number TEXT NULL` with `CONSTRAINT chk_br_phone_format CHECK (phone_number ~ '^\+61\d{9}$' OR phone_number IS NULL)`. `step_1_id` has a `UNIQUE` key. | **Reasoning:** The `UNIQUE` constraint on `step_1_id` enforces a one-to-one relationship, as a business can only have one registration profile. **Lifecycle:** The frontend (`src/contexts/OnboardingContext.tsx`), using its generic `ensureLinkedRecord` function (often wrapped by a step-specific helper in the UI component), automatically ensures this record is created when the user accesses Step 1 if it doesn't already exist, after the `step_1_business_profile` record is confirmed. Subsequent updates use the generic `updateRecord` function. |
| `activities` | **Child of Step 2.** `activity_type` is now of type `farms.activity_type_enum` (`('Cropping', 'Livestock', 'Mixed Farming', 'Horticulture', 'Dairy', 'Aquaculture', 'Forestry', 'Other')`). | Stores various farming activities. Creation is user-initiated in Step 2 (`src/components/onboarding/steps/FarmOperationsStep.tsx`) via the generic `createRecord` function from the context. |
| `contracts` | **Child of Step 2.** Includes `contract_type TEXT NULL`. | Stores contract details. Creation is user-initiated in Step 2 via the generic `createRecord` function. Updates use `updateRecord`. |
| `agreements` | **Child of Step 4.** `agreement_type` is now of type `farms.agreement_type_enum` (`('Service Agreement', 'Privacy Policy', 'Direct Debit', 'Terms and Conditions')`). | Stores user agreements and consent. Creation/update is user-initiated in Step 4 (`src/components/onboarding/steps/AgreementsStep.tsx`) typically using the generic `upsertRecord` function from the context, often with `onConflict` on `(step_4_id, agreement_type)`. |
| `payroll` | **Child of Step 3.** `is_access_to_software_granted BOOLEAN NOT NULL DEFAULT false`. Contains `encrypted_access_credentials BYTEA` for encrypted data. `step_3_id` has a `UNIQUE` key. | **Security:** The `access_credentials` field is encrypted via the `encrypt-and-store-sensitive-field` Edge Function (logic in `supabase/functions/encrypt-and-store-sensitive-field/index.ts`). **Lifecycle:** The frontend (`src/contexts/OnboardingContext.tsx`) automatically ensures this record is created with default values (using `ensureLinkedRecord`) when the user accesses Step 3. Updates use `updateRecord`. |
| `bookkeeping`, `payments` | `bookkeeping` is Child of Step 3; `payments` is Child of Step 4. Both contain `BYTEA` fields for encrypted data (`access_credentials` for `bookkeeping`, `bank_account_details` for `payments`). `bookkeeping` has `UNIQUE` key on `step_3_id`. `payments` no longer has `has_signed_direct_debit`. | **Security:** Encrypted fields are handled exclusively by the `encrypt-and-store-sensitive-field` Edge Function. **Lifecycle (bookkeeping):** Auto-created (via `ensureLinkedRecord`) when user accesses Step 3. **Lifecycle (payments):** User-initiated in Step 4, typically using `upsertRecord` from the context. |
| `documents` | **Child of `onboarding_sessions`**. A central, shared table. | **Reasoning:** This table is a direct child of the root session, not a step table. This is a deliberate design choice allowing a document to be associated with any entity from any step of thewizard, providing maximum flexibility for file uploads. Its RLS policy is therefore simpler than other child tables. Document creation is user-initiated via the `uploadAndFinalizeDocument` context function, which internally calls the `finalize-secure-file-upload` Edge Function that then calls the `finalize_document_upload` database function. |

---
## 3. Security Model: Deep Dive

### 3.1. Row-Level Security (RLS)
RLS (policies in `supabase/migrations/0001_rls_policies.sql`) follows a **Default Deny** principle and is **enabled on all tables**.

-   **Core Implementation - Hierarchical Joins:** Security is no longer centralized in a single helper function. Instead, each table\'s policy enforces ownership by performing an `EXISTS` subquery that joins up the data hierarchy.
-   **Policy Structure:**
    -   **Root Table (`onboarding_sessions`):** Grants access based on `user_id = auth.uid()`. A second, more restrictive policy prevents deletion of `completed` sessions.
    -   **Parent "Step" Tables:** Policies on these four tables (`step_1_...`, etc.) verify that the `user_id` on the referenced `onboarding_sessions` record matches `auth.uid()`.
    -   **Child Data Tables:** Policies on tables like `farms.contacts` or `farms.assets` are the most complex. They perform a join from the child table to its parent step table, and then from the parent step table to the root `onboarding_sessions` table to find the `user_id` for the ownership check.
        ```sql
        -- Example RLS Policy for a child table (farms.assets)
        CREATE POLICY \"Assets: Full access for session owners\" 
        ON farms.assets FOR ALL 
        USING (EXISTS (
            SELECT 1 FROM farms.step_3_financial_systems s3 
            JOIN farms.onboarding_sessions os ON s3.onboarding_session_id = os.id 
            WHERE s3.id = step_3_id AND os.user_id = auth.uid()
        ));
        ```

### 3.2. Data Encryption
-   **Workflow:** The `encrypt-and-store-sensitive-field` Edge Function (`supabase/functions/encrypt-and-store-sensitive-field/index.ts`) is the *only* entrypoint. It calls the `public.update_encrypted_field` database function (`supabase/migrations/0002_edge_functions.sql`), which has its own internal safeguards.
-   **Layered Validation:** The database function `update_encrypted_field` contains a hardcoded `ARRAY` of allowed table and column names. For V2.1, these are `v_allowed_tables TEXT[] := ARRAY['bookkeeping', 'payroll', 'payments'];` and `v_allowed_fields TEXT[] := ARRAY['access_credentials', 'bank_account_details', 'encrypted_access_credentials'];`. This is a critical secondary defense; even if an attacker could call this `SECURITY DEFINER` function, they could not use it to write to arbitrary tables or columns. The function intelligently determines the correct parent step table to join through for its ownership check.

---
## 4. PostgreSQL Functions: Implementation Details
(Defined in `supabase/migrations/0002_edge_functions.sql` unless otherwise noted.)

| Function Name | V2.1 Implementation Details & Rationale |
| :--- | :--- |
| `get_onboarding_session_data()` | Defined with `SECURITY DEFINER`. It has been completely rewritten for the V2 schema. It first finds the user\'s session and the IDs of the four step records. It then uses `json_build_object` and a series of nested subqueries to build a single, hierarchical JSON object that perfectly matches the new schema structure. This is highly performant and dynamically picks up new fields from tables. |
| `finalize_document_upload(json)` | Defined with `SECURITY DEFINER`. This function\'s logic is largely unchanged as the `farms.documents` table remains a direct child of the session. It performs its own ownership check before inserting the document metadata. |
| `update_encrypted_field(...)` | Defined with `SECURITY DEFINER`. This function is significantly more intelligent in V2.1. Based on the `p_table_name` argument (e.g., 'bookkeeping', 'payroll', 'payments'), it uses a `CASE` statement to determine the correct parent step table (`step_3_financial_systems` or `step_4_agreements`) to join through for its ownership check before performing the `UPDATE`. It correctly handles updating `farms.payroll.encrypted_access_credentials` when `p_table_name='payroll'` and `p_field_name='encrypted_access_credentials'`. |
| `get_session_id_from_path(text)` | (Defined in `supabase/migrations/0003_storage_setup.sql`) This helper function for Storage RLS is unaffected by the database schema changes, as it operates only on the text of the file path. |

---
## 5. Storage (`farms`)
(Setup and RLS policies in `supabase/migrations/0003_storage_setup.sql`)
-   **Bucket:** A single, **private** bucket named `farms` stores all user-uploaded files.
-   **Path Hierarchy:** `onboarding_sessions/{session_id}/{related_to_entity}/{related_to_id}/{unique_file_name}`
-   **RLS Policies:** The four policies (`SELECT`, `INSERT`, `UPDATE`, `DELETE`) are atomic and stateless. Each file operation is independently authorized by re-evaluating the policy. The policies work by calling `public.get_session_id_from_path(name)` and then performing a subquery to `farms.onboarding_sessions` to find the owner\'s `user_id` and compare it to `auth.uid()`. This logic is unaffected by the V2.1 schema change. 

---
## 6. Suggested Schema Changes & Improvements (as of latest review)

This section lists potential enhancements or changes to the database schema based on ongoing requirements analysis. These are suggestions and should be discussed and approved before implementation.

1.  **Add `phone_number` to `farms.business_registration`:**
    *   **Status: ADDRESSED.**
    *   **Resolution:** Added `phone_number TEXT NULL` and `CONSTRAINT chk_br_phone_format CHECK (phone_number ~ '^\\+61\\d{9}$' OR phone_number IS NULL)` to `farms.business_registration` in `supabase/migrations/0000_initial_schema.sql`.

2.  **Standardize/Enforce `farms.activities.activity_type` Values:**
    *   **Status: ADDRESSED.**
    *   **Resolution:** Changed `activity_type` in `farms.activities` to `farms.activity_type_enum` with values (`'Cropping', 'Livestock', 'Mixed Farming', 'Horticulture', 'Dairy', 'Aquaculture', 'Forestry', 'Other'`) in `supabase/migrations/0000_initial_schema.sql`.

3.  **Add `contract_type` to `farms.contracts`:**
    *   **Status: ADDRESSED.**
    *   **Resolution:** Added `contract_type TEXT NULL` to `farms.contracts` in `supabase/migrations/0000_initial_schema.sql`.

4.  **Standardize/Enforce `farms.agreements.agreement_type` for 'Terms and Conditions':**
    *   **Status: ADDRESSED.**
    *   **Resolution:** Changed `agreement_type` in `farms.agreements` to `farms.agreement_type_enum` which includes `'Terms and Conditions'` (values: `'Service Agreement', 'Privacy Policy', 'Direct Debit', 'Terms and Conditions'`) in `supabase/migrations/0000_initial_schema.sql`.

5.  **Clarify `farms.agreements.signature_data` Purpose:**
    *   **Status: ADDRESSED.**
    *   **Resolution:** The `farms.agreements` table now includes `signature_storage_path TEXT NULL` which is intended to store the path to the signature image file in Supabase Storage after being processed by the `process-digital-signature-and-consent` Edge Function. The `signature_data TEXT NULL` field is kept for potential storage of raw signature data from the input pad.

6.  **Clarify Role of `farms.payments.has_signed_direct_debit`:**
    *   **Status: ADDRESSED.**
    *   **Resolution:** The field `has_signed_direct_debit` was removed from `farms.payments` in `supabase/migrations/0000_initial_schema.sql`. Agreement for Direct Debit is handled by an `agreements` table entry with `agreement_type = 'Direct Debit'`.

7.  **Add `is_access_to_software_granted` to `farms.payroll`:**
    *   **Status: ADDRESSED.**
    *   **Resolution:** Added `is_access_to_software_granted BOOLEAN NOT NULL DEFAULT false` to `farms.payroll` (this was a rename from `is_access_required`) in `supabase/migrations/0000_initial_schema.sql`.