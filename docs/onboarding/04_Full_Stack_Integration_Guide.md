# NewTerra Full-Stack Integration Guide

**Version:** 1.3.1
**Last Reviewed:** October 26, 2023 (Matches current V2.1 generic context patterns)
**Audience:** Full-Stack Developers, Frontend Developers, Backend Developers, QA Engineers

---

This guide illustrates how the frontend (primarily React components in `src/components/` and context in `src/contexts/`) and backend (Supabase schema in `supabase/migrations/` and Edge Functions in `supabase/functions/`) work together. It provides high-granularity sequence diagrams for critical workflows and defines the global error handling strategy. **It should be read in conjunction with the other V2.1 architecture documents.**

## 1. Global Error Handling & Logging Strategy
A robust application anticipates failures. The following strategy **MUST** be implemented.
1.  **React Global Error Boundary:** The root of the React application (e.g., in `src/App.tsx` or `src/main.tsx`) must be wrapped in an Error Boundary component (example: `src/components/auth/ErrorBoundary.tsx`). This component will catch any unhandled JavaScript errors, prevent a white screen of death, log the error, and display a generic "An unexpected error occurred. Please refresh the page." message.
2.  **User-Facing Notifications (Toasts):** For non-critical, recoverable errors (e.g., a failed ABN validation due to an external service outage), a global notification or "toast" system must be used (e.g., `sonner` via `src/components/ui/sonner.tsx` and `src/hooks/use-toast.ts`). These messages should be user-friendly and actionable, as defined in `.cursor/docs/onboarding03_Frontend_Architecture.md`.
3.  **Third-Party Logging:** All caught errors (from the Error Boundary, `.catch` blocks, etc.) **MUST** be logged to a third-party monitoring service (e.g., Sentry, LogRocket). The log payload should include the error stack, the component where the error occurred, and the `onboarding_session_id` to aid in debugging. Consider a utility logger like one in `src/utils/logger.ts`.

---
## 2. Key Integration Patterns & Data Flow

### 2.1. Session Management (Start/Resume)
1.  On wizard load (`src/pages/Onboarding.tsx`), the `OnboardingContext` (`src/contexts/OnboardingContext.tsx`) in its `loadOrCreateSession` method calls the `get_onboarding_session_data()` PostgreSQL function (defined in `supabase/migrations/0002_edge_functions.sql`) via RPC.
2.  This function returns a comprehensive JSON object of the user's session. **Crucially, it dynamically includes new or modified fields from the database schema (e.g., `business_registration.phone_number`, updated ENUM values for `activities.activity_type`) due to its SQL construction (e.g., using `to_jsonb()`).**
3.  If `get_onboarding_session_data` returns data including a valid `onboarding_sessions` record and all four parent step records (`step_1_business_profile`, `step_2_farm_operations`, etc.), the UI state (`sessionData`) is populated to resume the session.
4.  If `get_onboarding_session_data` returns `null` or if any of the core records (the main `onboarding_sessions` record or any of the four parent step records) are missing, the `OnboardingContext`'s `loadOrCreateSession` (or its internal `createNewSession` logic) orchestrates their creation.
    *   It first ensures the `onboarding_sessions` record exists, creating it if necessary (typically a direct Supabase client call within the context if `get_onboarding_session_data` returns nothing for the user).
    *   Then, it ensures the four parent step records (`step_1_business_profile`, `step_2_farm_operations`, `step_3_financial_systems`, `step_4_agreements`) are created by calling their respective `ensureStepX...RecordExists(sessionId)` functions. These `ensureStepX...` functions now internally use the generic `ensureLinkedRecord` function from the context to insert these parent step records into the database if they don't exist, linking them to the `onboarding_session_id`.
5.  After ensuring all core records exist, `refreshSessionData()` (which calls `get_onboarding_session_data` again) is called to populate `sessionData` with a consistent and complete view of all freshly created or existing records.

### 2.2. Secure File Uploads
(See full sequence diagram in Section 4)
1.  The parent record to which the document will be related (e.g., a license in `farms.licenses`, an asset in `farms.assets`) **must be created first**. This is now done by the frontend component calling the generic `createRecord(tableName, data)` or `upsertRecord(tableName, data, options)` function from the `OnboardingContext`. For example, `createRecord('licenses', newLicenseData)`. The component is responsible for providing all necessary data, including foreign keys like `step_2_id` for a license.
2.  The `uploadAndFinalizeDocument` function in `OnboardingContext` is called by the frontend component. Internally, this context function calls the `initiate-secure-file-upload` Edge Function (`supabase/functions/initiate-secure-file-upload/index.ts`) with details of the parent record (e.g., `relatedToEntity: 'licenses'`, `relatedToId: new_license_id`) to get a signed URL.
3.  The client (browser), prompted by `uploadAndFinalizeDocument`, uploads the file directly to the signed URL using a `PUT` request.
4.  After the upload succeeds, the `uploadAndFinalizeDocument` function in `OnboardingContext` then calls the `finalize-secure-file-upload` Edge Function (`supabase/functions/finalize-secure-file-upload/index.ts`). This Edge Function, in turn, calls the `public.finalize_document_upload` PostgreSQL function (`supabase/migrations/0002_edge_functions.sql`) to create the metadata record in `farms.documents`, linking it to the original parent record. `uploadAndFinalizeDocument` then calls `refreshSessionData` upon success.

### 2.3. Storing Sensitive Data
(See full sequence diagram in Section 5)
1.  **Never** send plaintext secrets to the database directly via Supabase client library inserts/updates from the frontend.
2.  Ensure the primary record that will hold the encrypted data (e.g., `farms.payroll` for payroll credentials, `farms.bookkeeping` for bookkeeping system credentials) exists. This is typically handled by the relevant step component (e.g., `FinancialSystemsStep.tsx`) calling `ensureLinkedRecord` from the `OnboardingContext` when the step loads or when the specific section for that data is accessed.
    *   The component retrieves the parent step ID (e.g., `step3Id = sessionData?.step3_financialSystems?.id`).
    *   It then calls `ensureLinkedRecord` with the `tableName` (e.g., `'payroll'`), `linkCondition` (e.g., `{ step_3_id: step3Id }`), `defaultValues` (including `step_3_id` and other defaults), and `existingRecordFromSession` (e.g., `sessionData?.step3_financialSystems?.payroll?.[0]`).
    *   The returned record's ID (e.g., `payrollRecord.id`) is then used.
3.  The frontend component (or a helper it calls) then invokes the `encrypt-and-store-sensitive-field` Edge Function (`supabase/functions/encrypt-and-store-sensitive-field/index.ts`). This is usually done via a wrapper function in `src/integrations/supabase/api.ts` (e.g., `invokeEncryptAndStoreSensitiveField`).
    *   The payload to this function includes `tableName` (e.g., 'payroll'), `sessionId` (the onboarding session ID), `fieldName` (e.g., 'encrypted_access_credentials'), and the `plainTextValue`. The Edge Function uses `sessionId` to find the relevant step record, then the target record (e.g., payroll).
4.  This Edge Function encrypts the value and calls the `public.update_encrypted_field` PostgreSQL database function to store the encrypted `BYTEA` value. The `OnboardingContext` (or the component logic) should call `refreshSessionData` after a successful call to ensure the UI reflects any related state changes.

### 2.4. Data Saving & Updates (Client-Side)
-   Frontend components are now directly responsible for constructing the correct data payloads (including foreign keys like `step_1_id`, `step_2_id`, etc., which are obtained from `sessionData`) and calling the generic context functions from `OnboardingContext`:
    *   `createRecord<T extends TableName>(tableName: T, data: TableInsert<T> | TableInsert<T>[])`: For adding new child records (e.g., a new contact, a new activity). The `data` must conform to `TableInsert<T>` for the specified table.
    *   `updateRecord<T extends TableName>(tableName: T, recordId: string, data: TableUpdate<T>)`: For modifying existing records (e.g., changing a business name, updating an address). The `data` must conform to `TableUpdate<T>` for that specific `recordId`.
    *   `upsertRecord<T extends TableName>(tableName: T, data: TableInsert<T> | TableInsert<T>[], options?: { onConflict?: string })`: For records that have a unique link to a parent (e.g., `farms.data_migration` to `farms.step_4_agreements` based on `step_4_id`), or for updating/inserting agreements based on `(step_4_id, agreement_type)`. The `onConflict` option specifies the column(s) for conflict resolution.
    *   `deleteRecord<T extends TableName>(tableName: T, recordId: string)`: For removing records.
-   All these generic data manipulation functions in `OnboardingContext` automatically call `refreshSessionData()` upon successful completion to update the `sessionData` state and re-render the UI.
-   When new fields are added to a table:
    1.  The `database.types.ts` file **must be regenerated** (`npx supabase gen types typescript ...`). This updates `TableRow<T>`, `TableInsert<T>`, and `TableUpdate<T>` for all tables.
    2.  The data payload passed from the component to `createRecord`, `updateRecord`, or `upsertRecord` must include the new field, conforming to the updated types.
    3.  If using `ensureLinkedRecord` to create this type of record, the `defaultValues` provided by the component should include the new field.

### 2.5. Validation Layers
(Largely unchanged in principle, but frontend interactions with context are now generic)
1.  **Client-Side (UI):** Real-time feedback (e.g., regex for phone `+61XXXXXXXXX`, zod schemas for form objects before calling context functions like `createRecord`).
2.  **Edge Functions (Server-Side):** For external checks (e.g., `validate-australian-business-number`).
3.  **Database (Server-Side):** `CHECK` constraints (ABN, ACN, email, phone formats like `chk_br_phone_format`), `ENUM` types, and Foreign Keys are the final authority for data integrity.

---
## 3. Example Workflow: Adding a New Field (e.g., `phone_number` to `business_registration`)

This example illustrates the full-stack process, highlighting the use of generic context functions by frontend components.

1.  **Database (`supabase/migrations/..._add_phone_to_business_reg.sql`):**
    ```sql
    ALTER TABLE farms.business_registration
    ADD COLUMN phone_number TEXT NULL,
    ADD CONSTRAINT chk_br_phone_format CHECK (phone_number ~ '^\+61\d{9}$' OR phone_number IS NULL);
    -- Also update RLS if needed, though likely not for adding a column to an existing SELECT/UPDATE policy.
    ```
2.  **Backend - RLS (`supabase/migrations/0001_rls_policies.sql`):**
    *   Ensure `SELECT` and `UPDATE` policies for `farms.business_registration` allow access to the new `phone_number` column. (Typically, existing policies granting access to all columns `USING (true)` are sufficient, but verify.)

3.  **Frontend - Type Definitions:**
    *   **`src/types/database.types.ts`:** Regenerate this file:
        `npx supabase gen types typescript --project-id <your-project-id> --schema farms > src/types/database.types.ts`
        This automatically includes `phone_number` in the `business_registration` types: `TableRow<'business_registration'>`, `TableInsert<'business_registration'>`, and `TableUpdate<'business_registration'>`. These types are used by the generic context functions.

4.  **Frontend - Context (`src/contexts/OnboardingContext.tsx`):**
    *   **No changes are needed in the generic context functions themselves** (`ensureLinkedRecord`, `updateRecord`). They adapt based on the `TableName` and the (now updated) `database.types.ts`.
    *   **Initial Record Creation (Handled by Component):** The `BusinessProfileStep.tsx` component (or similar) is responsible for calling `ensureLinkedRecord` to ensure the `business_registration` record exists when Step 1 loads. The `defaultValues` provided by the component in this call should now include `phone_number: null` (or an appropriate default).
        ```typescript
        // Inside BusinessProfileStep.tsx, after ensureStep1BusinessProfileRecordExists has run:
        const step1Id = sessionData?.step1_businessProfile?.id; 
        if (step1Id) {
          // Type for defaultValues will be TableInsert<'business_registration'>
          const bizRegRecord = await ensureLinkedRecord({
            tableName: 'business_registration',
            linkCondition: { step_1_id: step1Id }, 
            defaultValues: { 
              step_1_id: step1Id, 
              full_business_name: '', // Or other sensible defaults
              // ... other fields ...
              phone_number: null, // Add the new field to defaultValues
            },
            existingRecordFromSession: sessionData?.step1_businessProfile?.businessRegistration?.[0] 
          });
          // bizRegRecord (if not null) is the ensured business_registration record.
          // Its ID (bizRegRecord.id) can be used for subsequent updates.
        }
        ```
    *   **Update (Handled by Component):** To update the phone number, the component will call the generic `updateRecord` function from the context:
        ```typescript
        // In BusinessProfileStep.tsx, when saving the phone number:
        const businessRegId = sessionData?.step1_businessProfile?.businessRegistration?.[0]?.id;
        const newPhoneNumber = e.target.value; // from input

        if (businessRegId && newPhoneNumber) {
          try {
            // Type for update payload will be TableUpdate<'business_registration'>
            const updatedRecords = await updateRecord('business_registration', businessRegId, { phone_number: newPhoneNumber });
            if (updatedRecords) {
              // Success, OnboardingContext's updateRecord already called refreshSessionData
            } else {
              // Handle error (e.g., toast notification)
            }
          } catch (error) {
            // Handle error
          }
        }
        ```

5.  **Frontend - UI Component (`src/components/onboarding/steps/BusinessProfileStep.tsx`):**
    *   Add an input field for `phone_number`.
    *   The input should read its value from `sessionData.step1_businessProfile.businessRegistration[0]?.phone_number`.
    *   On change or on save, the component calls `updateRecord('business_registration', recordId, { phone_number: ... })` as shown in the example above, providing the correct `recordId` and payload.

This structured approach, leveraging generic context functions and strong typing from `database.types.ts`, ensures that changes are propagated correctly from the database up to the UI with minimal changes to the context itself. The responsibility for constructing correct data payloads and calling the appropriate generic function shifts to the UI components.

---
## 4. Sequence Diagram: Wizard Initialization & Step Navigation

This diagram illustrates how the wizard initializes and how parent step records and primary data records (like `business_registration`) are ensured using the new generic context functions.

```mermaid
sequenceDiagram
    participant FE_Provider as Frontend (UI/Page)
    participant FE_Context as Frontend (OnboardingContext)
    participant DB as DB (PostgreSQL - Supabase)

    FE_Provider->>FE_Context: App/Wizard mounts, triggers `loadOrCreateSession()`
    activate FE_Context

    FE_Context->>DB: RPC call: `get_onboarding_session_data()`
    DB-->>FE_Context: Returns session data (e.g., `fullData`)

    alt `fullData.onboardingSession` Exists (and presumably step records)
        FE_Context->>FE_Context: Populates `sessionData` state from `fullData`
        FE_Context-->>FE_Provider: Initial data ready
    else `fullData.onboardingSession` is Missing (New User or Incomplete Setup)
        FE_Context->>FE_Context: Calls `createNewSession()`
        activate FE_Context # New activation for createNewSession

        Note over FE_Context: `createNewSession` creates `onboarding_sessions` record if needed (direct Supabase client call). Let `newSessionId` be its ID.
        FE_Context->>DB: INSERT into `onboarding_sessions` (user_id, status, current_step)
        DB-->>FE_Context: Returns `{id: newSessionId}`

        FE_Context->>FE_Context: Calls `ensureStep1BusinessProfileRecordExists(newSessionId)`
        Note right of FE_Context: Internally calls `ensureLinkedRecord(\'step_1_business_profile\', {linkCondition: {onboarding_session_id: newSessionId}, defaultValues: {onboarding_session_id: newSessionId}, existingRecordFromSession: sessionData?.step1_businessProfile})`. `ensureLinkedRecord` will call `refreshSessionData()` if it creates the record.
        FE_Context->>DB: (via `ensureLinkedRecord`) Checks/Inserts `step_1_business_profile`
        DB-->>FE_Context: (Confirms/Returns `step_1_business_profile` record)

        FE_Context->>FE_Context: Calls `ensureStep2FarmOperationsRecordExists(newSessionId)`
        Note right of FE_Context: Internally calls `ensureLinkedRecord(...)` for step 2. `refreshSessionData()` if created.
        FE_Context->>DB: (via `ensureLinkedRecord`) Checks/Inserts `step_2_farm_operations`
        DB-->>FE_Context: (Confirms/Returns `step_2_farm_operations` record)

        FE_Context->>FE_Context: Calls `ensureStep3FinancialSystemsRecordExists(newSessionId)`
        Note right of FE_Context: Internally calls `ensureLinkedRecord(...)` for step 3. `refreshSessionData()` if created.
        FE_Context->>DB: (via `ensureLinkedRecord`) Checks/Inserts `step_3_financial_systems`
        DB-->>FE_Context: (Confirms/Returns `step_3_financial_systems` record)

        FE_Context->>FE_Context: Calls `ensureStep4AgreementsRecordExists(newSessionId)`
        Note right of FE_Context: Internally calls `ensureLinkedRecord(...)` for step 4. `refreshSessionData()` if created.
        FE_Context->>DB: (via `ensureLinkedRecord`) Checks/Inserts `step_4_agreements`
        DB-->>FE_Context: (Confirms/Returns `step_4_agreements` record)
        
        FE_Context->>FE_Context: `createNewSession` calls `refreshSessionData()` (final refresh for consistency)
        FE_Context->>DB: RPC call: `get_onboarding_session_data()`
        DB-->>FE_Context: Returns updated full `sessionData`
        FE_Context->>FE_Context: Populates `sessionData` state
        FE_Context-->>FE_Provider: Initial data ready
        deactivate FE_Context # Deactivate createNewSession
    end
    deactivate FE_Context # Deactivate loadOrCreateSession

    FE_Provider->>FE_Step: User navigates to Business Profile Step (Step 1)
    participant FE_Step as Frontend (e.g., BusinessProfileStep.tsx)
    activate FE_Step

    Note over FE_Step,FE_Context: Component accesses `sessionData` (e.g., `step1Id = sessionData.step1_businessProfile.id`).
    Note over FE_Step,FE_Context: Component ensures its primary data record (e.g., `business_registration`) exists using `ensureLinkedRecord`.
    
    FE_Step->>FE_Context: Calls `ensureLinkedRecord({<br/>    tableName: \'business_registration\',<br/>    linkCondition: { step_1_id: step1Id },<br/>    defaultValues: { step_1_id: step1Id, ... },<br/>    existingRecordFromSession: sessionData?.step1_businessProfile?.businessRegistration?.[0]<br/>})`
    activate FE_Context
    FE_Context->>DB: (via `ensureLinkedRecord`) Checks `existingRecordFromSession`, then DB query if needed.
    alt `business_registration` record does NOT exist
        FE_Context->>DB: INSERT INTO `business_registration` (via Supabase client in `ensureLinkedRecord`)
        DB-->>FE_Context: Returns created record(s)
        FE_Context->>FE_Context: `ensureLinkedRecord` calls `refreshSessionData(true)`
        FE_Context->>DB: RPC `get_onboarding_session_data()`
        DB-->>FE_Context: Returns updated `sessionData`
    else `business_registration` record already exists
        FE_Context->>DB: (No DB call if `existingRecordFromSession` or prior DB query in `ensureLinkedRecord` was sufficient)
        DB-->>FE_Context: (Returns existing `business_registration` record)
    end
    FE_Context-->>FE_Step: Returns the `business_registration` record
    deactivate FE_Context
    deactivate FE_Step
    Note over FE_Provider: UI for `business_registration` fields is now enabled.
```

### Workflow: Auto-Saving Form Progress
This workflow now relies on generic functions from `OnboardingContext`. When a user modifies a field in an existing record (e.g., `business_registration.full_business_name`), the component will typically call `updateRecord('business_registration', recordId, { full_business_name: newValue })`. For new child records (e.g., adding a new contact), `createRecord('contacts', newContactData)` is used, where `newContactData` includes the necessary foreign key (e.g., `step_1_id`). For 1-to-1 linked records that might not exist yet (e.g. `data_migration` linked to `step_4_agreements`), `upsertRecord('data_migration', dataMigrationData, { onConflict: 'step_4_id' })` would be appropriate. All these generic functions in `OnboardingContext` automatically call `refreshSessionData()` upon successful completion to update the UI.

---
## 5. The Complete Secure Document Upload Workflow

```mermaid
sequenceDiagram
    participant User
    participant FE as Frontend (React Component)
    participant FECtx as Frontend (OnboardingContext)
    participant API as Frontend (integrations/supabase/api.ts - specific invoke helpers)
    participant EF_Init as Edge Function (initiate-secure-file-upload)
    participant Storage as Supabase Storage
    participant EF_Final as Edge Function (finalize-secure-file-upload)
    participant DB_Func as DB (public.finalize_document_upload)
    participant DB_Tables as DB (farms.documents table & parent table e.g. farms.licenses)

    User->>FE: Fills parent entity details (e.g., license type, number) and selects file.
    
    Note over FE,FECtx: Step 1: Create parent entity record for the document (e.g., a license).
    FE->>FECtx: Calls `createRecord('licenses', { step_2_id: currentStep2Id, license_type: '...', ... })` 
    activate FECtx
    FECtx->>DB_Tables: (via Supabase client) INSERT into `licenses`
    DB_Tables-->>FECtx: Returns created record(s) e.g. `[{ id: new_license_id, ... }]`
    FECtx->>FECtx: `createRecord` calls `refreshSessionData()`
    FECtx-->>FE: Returns created license record(s) (now available in updated `sessionData`)
    deactivate FECtx
    Note over FE: `new_license_id` is now available from the `sessionData` or the direct return.

    User->>FE: Clicks "Upload Document" button.
    FE->>FECtx: Calls `uploadAndFinalizeDocument(file, 'licenses', new_license_id)`
    activate FECtx

    Note over FECtx,API: Step 2: Get Signed URL (within `uploadAndFinalizeDocument` context function)
    FECtx->>API: `invokeInitiateSecureFileUpload(onboardingSessionId, 'licenses', new_license_id, file.name)`
    activate API
    API->>EF_Init: Invoke Edge Function `initiate-secure-file-upload`
    activate EF_Init
    EF_Init->>Storage: Generate Signed URL for PUT
    Storage-->>EF_Init: Signed URL & file path
    EF_Init-->>API: Returns `{ signedUrl, filePath }`
    deactivate EF_Init
    API-->>FECtx: Returns `{ signedUrl, filePath }`
    deactivate API

    Note over FECtx,Storage: Step 3: Upload file to Storage (within `uploadAndFinalizeDocument`)
    FECtx->>Storage: PUT request to `signedUrl` with file binary
    Storage-->>FECtx: HTTP 200 OK (on success)

    Note over FECtx,API: Step 4: Finalize Upload (within `uploadAndFinalizeDocument`)
    FECtx->>API: `invokeFinalizeSecureUpload(onboardingSessionId, file.name, 'licenses', new_license_id, filePathFromStep2)`
    activate API
    API->>EF_Final: Invoke Edge Function `finalize-secure-file-upload`
    activate EF_Final
    EF_Final->>DB_Func: RPC call `finalize_document_upload(...)` with payload
    activate DB_Func
    DB_Func->>DB_Tables: INSERT into `farms.documents` (metadata for the uploaded file)
    DB_Tables-->>DB_Func: Success (new document record ID)
    DB_Func-->>EF_Final: Success
    deactivate DB_Func
    EF_Final-->>API: Returns `{ success: true, documentId: new_document_id }`
    deactivate EF_Final
    API-->>FECtx: Returns `{ success: true, documentId: new_document_id }`
    deactivate API
    
    FECtx->>FECtx: `uploadAndFinalizeDocument` calls `refreshSessionData()`
    FECtx-->>FE: Returns `{ success: true, documentId: new_document_id }`
    deactivate FECtx
    FE->>User: Shows success/failure message. Document appears in list (UI reads from updated `sessionData`).
```

## 6. Sequence Diagram: Storing Sensitive Data (e.g., Payroll Credentials)

```mermaid
sequenceDiagram
    participant User
    participant FE_Comp as Frontend (UI Component e.g., in FinancialSystemsStep.tsx)
    participant FE_Context as Frontend (OnboardingContext)
    participant API_Helper as Frontend (integrations/supabase/api.ts - invokeEncryptAndStoreSensitiveField)
    participant EF_Encrypt as Edge Function (encrypt-and-store-sensitive-field)
    participant DB_UpdateFunc as DB (public.update_encrypted_field)
    participant DB_Table as DB (e.g., farms.payroll table)

    User->>FE_Comp: Is on Step 3 (Financial Systems), viewing Payroll section.
    activate FE_Comp
    
    Note over FE_Comp, FE_Context: Component ensures `payroll` record exists for the current `step_3_id`.
    FE_Comp->>FE_Context: `ensureLinkedRecord({ tableName: 'payroll', linkCondition: { step_3_id: currentStep3Id }, defaultValues: {step_3_id: currentStep3Id, ...}, ... })`
    activate FE_Context
    FE_Context->>DB_Table: (via Supabase client) Checks/Inserts `payroll` record.
    DB_Table-->>FE_Context: Confirms/Returns `payroll` record(s) (let its ID be `targetPayrollId`).
    FE_Context->>FE_Context: `ensureLinkedRecord` calls `refreshSessionData()` on create.
    FE_Context-->>FE_Comp: `payroll` record available (e.g., from updated `sessionData`).
    deactivate FE_Context

    User->>FE_Comp: Enters payroll credentials (plain text).
    User->>FE_Comp: Clicks "Save Payroll Credentials" button.

    FE_Comp->API_Helper: Calls `invokeEncryptAndStoreSensitiveField({tableName: 'payroll', sessionId: currentSessionId, fieldName: 'encrypted_access_credentials', plainTextValue: plainTextCredentials})`
    deactivate FE_Comp
    activate API_Helper

    API_Helper->>EF_Encrypt: Invoke Edge Function `encrypt-and-store-sensitive-field` with payload.
    activate EF_Encrypt
    EF_Encrypt->>EF_Encrypt: Retrieves PGCRYPTO_KEY, encrypts `plainTextCredentials`.
    EF_Encrypt->>DB_UpdateFunc: RPC call `update_encrypted_field('payroll', currentSessionId, 'encrypted_access_credentials', encryptedValue, targetPayrollIdFromSessionLookup)` 
    activate DB_UpdateFunc
    DB_UpdateFunc->>DB_Table: UPDATE `farms.payroll` SET `encrypted_access_credentials` = encryptedValue WHERE `id` = `targetPayrollIdFromSessionLookup` (with ownership checks).
    DB_Table-->>DB_UpdateFunc: Success.
    DB_UpdateFunc-->>EF_Encrypt: Success.
    deactivate DB_UpdateFunc
    EF_Encrypt-->>API_Helper: `{ success: true, message: "..." }`
    deactivate EF_Encrypt
    
    API_Helper-->>FE_Comp: Returns success/failure. (Component receives this promise)
    deactivate API_Helper
    activate FE_Comp

    alt Successful Encryption
        FE_Comp->>FE_Context: (After API_Helper promise resolves successfully) Calls `refreshSessionData()` to update UI.
        activate FE_Context
        FE_Context->>DB: RPC call: `get_onboarding_session_data()`
        DB-->>FE_Context: Returns updated `sessionData`
        FE_Context->>FE_Context: Populates `sessionData` state
        FE_Context-->>FE_Comp: (Implicitly) UI updates with confirmation/masked data.
        deactivate FE_Context
    end
    FE_Comp->>User: Sees success message.
    deactivate FE_Comp
```

---l;'
## 7. Troubleshooting Common Integration Issues

*   **Data Not Appearing in UI After Save/Update:**
    *   Ensure `get_onboarding_session_data()` is being re-fetched. The generic `createRecord`, `updateRecord`, `upsertRecord`, `deleteRecord`, and `ensureLinkedRecord` (on create) functions in `OnboardingContext` automatically call `refreshSessionData()` upon successful completion.
    *   Verify the Supabase client call logic inside the generic context function is correct for the operation.
    *   **Check Component Logic:**
        *   Is the correct `tableName` being passed to the context function?
        *   Is the `recordId` correct for `updateRecord` / `deleteRecord`?
        *   Is the `data` payload being passed from the component correctly structured according to `TableInsert<T>` or `TableUpdate<T>` for that table (as defined in `src/types/database.types.ts`)?
        *   Are all required fields, especially foreign keys (e.g., `step_1_id`), present in the `data` object with correct values from `sessionData`?
    *   Check RLS policies on the table in Supabase Dashboard; are they preventing reads or updates for the authenticated user?
    *   Inspect network requests in browser dev tools to see the payload sent to Supabase (for direct client calls) or Edge Functions, and the response received.
*   **"Failed to fetch" or Network Errors when calling Edge Functions:**
    *   Ensure your frontend URL (e.g., `http://localhost:5173`) is in the Supabase CORS allow-list (Project Settings -> API -> CORS Configuration).
    *   Check the Edge Function logs in Supabase Dashboard (Functions -> Select Function -> Logs) for any runtime errors or warnings.
*   **Type Errors (TypeScript):**
    *   **Regenerate `database.types.ts`:** This is the most common fix. If you've changed the DB schema, **always** run `npx supabase gen types typescript --project-id <your-project-id> --schema farms > src/types/database.types.ts`.
    *   **Payload Mismatch:** Ensure the data object passed from your component to generic context functions (`createRecord`, `updateRecord`, `upsertRecord`, `ensureLinkedRecord`'s `defaultValues`) strictly matches the `TableInsert<T>` or `TableUpdate<T>` type for the given `TableName T`. Check for missing required fields or incorrect data types.
    *   **Foreign Keys:** Remember that `TableInsert<T>` types (used for `createRecord`, `upsertRecord`, and `ensureLinkedRecord`'s `defaultValues`) will require foreign key fields (e.g., `step_1_id`) to be explicitly part of the data object your component provides.
*   **RLS Issues:**
    *   Double-check the RLS policies (`supabase/migrations/0001_rls_policies.sql`) for the target table and all parent tables in the hierarchy.
    *   Ensure `auth.uid()` is correctly used and that the session, step, and record IDs are correctly passed and linked throughout the data hierarchy for the policy checks.
*   **Edge Function Errors (check function logs in Supabase Dashboard):**
    *   `encrypt-and-store-sensitive-field` fails: Check `PGCRYPTO_KEY` is in Supabase Vault. Ensure `pgsodium` is enabled in your Supabase project (Project Settings -> Database -> Extensions).
*   **Schema Mismatches / Migration Issues:**
    *   If migrations from `supabase/migrations/` don't seem to apply, check the Supabase Dashboard -> Database -> Migrations for errors.
    *   Ensure local Supabase CLI is linked to the correct project: `supabase link --project-ref <your-project-ref>`.
    *   After running `supabase db reset` or `supabase db push`, manually verify critical schema changes and SQL function definitions in the Supabase Dashboard as per `06_Setup_and_Deployment.md`.

### Lead Developer Call to Action:
- **ENUM Value Consistency:** After adding or modifying ENUM types (e.g., `farms.activity_type_enum`, `farms.agreement_type_enum` in `supabase/migrations/0000_initial_schema.sql`), ensure all relevant frontend components (dropdowns, constants, validation logic in files like `src/components/onboarding/steps/FarmOperationsStep.tsx` or `AgreementsStep.tsx`) are updated to use the exact, new set of ENUM values. Also update corresponding string literal union types in `src/types/onboarding.ts` (if custom types are used beyond `database.types.ts`) and ensure `src/types/database.types.ts` is regenerated.