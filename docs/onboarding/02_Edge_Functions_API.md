# NewTerra Edge Functions: API Reference

**Version:** 2.1
**Audience:** Frontend Developers, Full-Stack Developers, QA Engineers, System Architects

---

This document is the official, implementation-level API reference for the Supabase Edge Functions (located in `supabase/functions/`), updated for the V2.1 hierarchical schema. It details the request/response contracts, orchestration logic, and critical failure modes for each function. Underlying SQL functions called by these Edge Functions are primarily defined in `supabase/migrations/0002_edge_functions.sql`.

## 1. Architectural Overview: The Secure Wrapper Pattern
The Edge Functions in this project adhere to a **"Secure Wrapper"** pattern. Their primary role is **not** to contain complex business logic. Instead, they are responsible for:
1.  **CORS & Public Interface:** Providing a secure, publicly accessible HTTP endpoint.
2.  **Authentication:** Validating the user's JWT.
3.  **Privilege Escalation:** Securely accessing secrets (e.g., API keys) from Supabase Vault and using the `service_role` key to call privileged PostgreSQL functions.
4.  **Orchestration:** Executing logic that cannot be performed in PostgreSQL, such as making external API calls or invoking Supabase-specific features (e.g., generating signed URLs).

Heavy business logic, data validation, and database writes are delegated to the underlying PostgreSQL functions, which are the authoritative core of the system.

## 2. Invocation & Error Handling

### 2.1. Generic Invocation Pattern
*(Unchanged from v1.1)*

Edge Functions are invoked from the frontend via helper/wrapper functions defined in `src/integrations/supabase/api.ts`. These wrappers are then typically called by functions within the `OnboardingContext` (`src/contexts/OnboardingContext.tsx`). For example, the `uploadAndFinalizeDocument` context function calls wrappers that invoke `initiate-secure-file-upload` and `finalize-secure-file-upload` Edge Functions.

### 2.2. Standardized Error Response
Edge Functions aim to return errors using a standardized JSON structure. While the ideal is ` { "data": null, "error": { "message": "A user-safe error description." } }`, most functions currently return a simpler structure, typically with a top-level `error` string or a `success: false` field accompanied by an `error` string. The client (usually the `OnboardingContext` or its callers) should always check for an `error` property in the response or a `success: false` state and handle it appropriately (e.g., by showing a toast notification).
```json
// Example 1: Direct error property
{ "error": "A user-safe error description.", "isValid": false }
// Example 2: Success flag with error property
{ "success": false, "error": "Detailed error message about what went wrong." }
```

---
## 3. Core Onboarding & Validation

### `validate-australian-business-number`
(Implementation: `supabase/functions/validate-australian-business-number/index.ts`)
Provides real-time, server-side validation for an Australian Business Number (ABN).

| Failure Mode      | HTTP Code | Cause & Explanation                                                                       | Recommended Client-Side Handling                                                                                         |
| :---------------- | :-------- | :---------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------- |
| Malformed ABN     | `400`     | The `abn` string in the request body failed sanitization or basic format checks.          | Display an immediate, inline validation error: "Please enter a valid ABN format."                                        |
| Unauthorized      | `401`     | The request was made without a valid Supabase JWT.                                        | This indicates a session issue. The user should be redirected to the login page.                                         |
| ABN Not Registered/Active | `404`     | The external ASIC service successfully responded but indicated the ABN is not registered or not currently active. | Display an inline validation error: "This ABN is not registered or not active. Please check the number."               |
| External API Down | `503`     | The function could not connect to the external ASIC service.                              | Display a non-blocking notification: "We can't validate the ABN right now. You can continue, and we'll verify it later." |

-   **Success Response (ABN is Active):** `{ "isValid": true, "businessName": "Example Pty Ltd", "entityType": "Company", "businessState": "NSW" }`
-   **Response (ABN Not Active/Registered - HTTP 404):** `{ "isValid": false, "error": "This ABN is not registered or not active.", "businessName": "Old Business Name Pty Ltd" (if available), "entityType": "Company" (if available), "businessState": "VIC" (if available) }`
-   **Security & Orchestration Notes:** This is a pure orchestration function. It sanitizes the input, retrieves the `ASIC_API_KEY` from Vault, makes the external call, and returns the result. It does not interact with the `farms` database.

### `get_onboarding_session_data` (PostgreSQL Function - Not an Edge Function)
(Definition: `supabase/migrations/0002_edge_functions.sql`)
This is a PostgreSQL function called via RPC from the client (`src/integrations/supabase/api.ts`). It is the primary mechanism for fetching all data related to a user's onboarding session.
-   **V2.1 Notes:** Dynamically reflects schema changes due to its use of `to_jsonb()` and similar constructs. New fields (e.g., `business_registration.phone_number`, `contracts.contract_type`) and ENUM value changes are automatically included in the response without needing direct modification to this SQL function's definition for those specific changes. The `OnboardingContext` (`src/contexts/OnboardingContext.tsx`) relies heavily on this function (via its `refreshSessionData` method) to populate the `sessionData` state used by the entire wizard.
-   **Success Response:** A hierarchical JSON object representing all data for the user's current onboarding session.

### `import-asset-registry-from-csv`
(Implementation: `supabase/functions/import-asset-registry-from-csv/index.ts`)
Parses a CSV file and batch-inserts the data into the `assets` table.
-   **Reason for Edge Function:** Requires a robust CSV parsing library not available in PostgreSQL.
-   **Request Payload:** A `multipart/form-data` request containing the CSV file (typically under a field named 'file'). The `sessionId` or user context is derived from the JWT, not typically required as a separate form field.
-   **Core Logic (V2.1):**
    1.  Verifies the user is authenticated (via JWT) and retrieves their `onboarding_session_id`.
    2.  From the session, it finds the `id` of the related `step_3_financial_systems` record (`step_3_id`). The frontend orchestration (`src/contexts/OnboardingContext.tsx` using its `ensureStep3FinancialSystemsRecordExists` and `ensureLinkedRecord` functions) ensures this `step_3_financial_systems` record is created when the user navigates to Step 3.
    3.  Uses a CSV parsing library to parse the file stream.
    4.  For each row, validates data and constructs a new asset object, linking it to the retrieved `step_3_id`.
    5.  Performs a batch `INSERT` into `farms.assets`.
-   **Responses:**
    -   **Full Success:** `{ "success": true, "importedCount": 25, "errorCount": 0 }`
    -   **Partial Success/Validation Errors:** `{ "success": false, "importedCount": 10, "errorCount": 2, "errors": ["Row 5: Invalid asset category", "Row 12: Renewal date format incorrect"] }`
    -   **General Processing Error:** `{ "success": false, "error": "Failed to connect to database" }` (HTTP 500)
    -   **Authentication/Authorization Error:** `{ "error": "Unauthorized" }` (HTTP 401/403)
    -   **No Session/Step 3 Record:** `{ "error": "No active onboarding session or related Step 3 record found" }` (HTTP 404)

### `process-digital-signature-and-consent`
(Implementation: `supabase/functions/process-digital-signature-and-consent/index.ts`)
Handles the digital signature workflow for service agreements.
-   **Reason for Edge Function:** Orchestrates a multi-step workflow involving file handling and database interaction.
-   **Request Payload:**
    ```json
    {
      "sessionId": "...",
      "agreementType": "Service Agreement", // Or other values from farms.agreement_type_enum
      "signatureData": "data:image/png;base64,iVBORw0KGgo..." 
    }
    ```
-   **Core Logic (V2.1):**
    1.  Verifies user ownership and retrieves the `step_4_id` from the `step_4_agreements` table (linked to `sessionId`). The frontend orchestration (`src/contexts/OnboardingContext.tsx` using `ensureStep4AgreementsRecordExists` and `ensureLinkedRecord`) ensures this `step_4_agreements` record is created when the user navigates to Step 4.
    2.  Converts the base64 `signatureData` into a binary blob.
    3.  Uploads the signature image to a unique path in the `farms` bucket (e.g., `onboarding_sessions/{session_id}/agreements/{uuid}/signature.png`).
    4.  On successful upload, performs an `UPSERT` on the `farms.agreements` table. This is typically done by the `OnboardingContext`'s `upsertRecord('agreements', ...)` function, which would be called after this Edge Function successfully processes the signature and returns the storage path. The `upsertRecord` call would set `is_agreed = true` and store the signature's file path in `signature_storage_path`, using a composite `onConflict` key of `(step_4_id, agreement_type)`.
-   **Success Response:** `{ "success": true, "message": "Service Agreement signed and recorded successfully", "signatureStoragePath": "onboarding_sessions/.../signature.png" }`

### `encrypt-and-store-sensitive-field`
(Implementation: `supabase/functions/encrypt-and-store-sensitive-field/index.ts`)
Encrypts a plaintext value and stores it in a `BYTEA` database column via the `public.update_encrypted_field` SQL function (`supabase/migrations/0002_edge_functions.sql`).

-   **V2.1 Notes:** 
    -   `tableName` in the payload **MUST** be one of: `"bookkeeping"`, `"payroll"`, `"payments"`.
    -   `fieldName` in the payload **MUST** be one of: `"access_credentials"` (for bookkeeping), `"encrypted_access_credentials"` (for payroll), `"bank_account_details"` (for payments).
    -   The backend RPC call `update_encrypted_field` uses the `p_table_name` and `p_field_name` parameters directly to update the specified column in the specified table after performing ownership checks via the appropriate step parent table. For `tableName: 'payroll'` and `fieldName: 'encrypted_access_credentials'`, the function will update the `encrypted_access_credentials` column in the `farms.payroll` table.
-   **Request Payload (JSON):**
    ```json
    {
      "tableName": "payroll", // e.g., bookkeeping, payroll, payments
      "sessionId": "...",
      "fieldName": "encrypted_access_credentials", // e.g., access_credentials, encrypted_access_credentials, bank_account_details
      "plainTextValue": "sensitive data here"
    }
    ```
-   **Success Response:** `{ "success": true, "message": "Successfully encrypted and stored sensitive data in fieldName" }`
-   **Security & Orchestration Notes:** This function orchestrates the retrieval of the `ENCRYPTION_KEY` (now `PGCRYPTO_KEY`) from Vault and the encryption of the `plainTextValue`. It then calls the `public.update_encrypted_field` PostgreSQL function, which performs its own critical validation of the target table/column (allowed tables: `bookkeeping`, `payroll`, `payments`; allowed fields: `access_credentials`, `bank_account_details`, `encrypted_access_credentials`) and ownership before executing the `UPDATE`. The frontend orchestration (`src/contexts/OnboardingContext.tsx` using `ensureLinkedRecord` for primary data tables like `bookkeeping` or `payroll`) ensures the relevant parent step record (e.g., `step_3_financial_systems` for `bookkeeping` or `payroll`) and the primary data record itself (e.g., the specific `bookkeeping` or `payroll` row) are established before this function is called.

---
## 4. Secure File & Data Handling

### `initiate-secure-file-upload`
(Implementation: `supabase/functions/initiate-secure-file-upload/index.ts`)
**Step 1 of the secure upload process.**

-   **V2.1 Notes:** The `relatedToEntity` field **MUST** now use the new, simplified table names (e.g., `"licenses"`, `"assets"`). The frontend is responsible for providing the correct entity name.
-   **Request Payload (JSON):**
    ```json
    {
      "sessionId": "UUID of the onboarding session",
      "fileName": "my-license.pdf",
      "relatedToEntity": "licenses",
      "relatedToId": "UUID of the license record"
    }
    ```
-   **Success Response:** `{ "signedUrl": "...", "filePath": "..." }`
-   **Security & Orchestration Notes:** This function acts as a temporary credential generator. It constructs the full, unique storage path and uses the Supabase Admin SDK to create a short-lived signed URL for a `PUT` request. **The frontend client is responsible for making the `PUT` request to this URL.** The `filePath` returned by this function is a critical piece of data that **must** be sent to `finalize-secure-file-upload` in Step 3. The frontend orchestration (e.g., in `src/contexts/OnboardingContext.tsx` using its generic `createRecord` or `updateRecord` for the parent entity like `licenses`, and `ensureStepX...` functions for parent step records) ensures that the parent record (e.g., `licenses` identified by `relatedToId`) and its corresponding step parent record (e.g., `step_2_farm_operations`) are created before this function is called, which is essential for data integrity and RLS downstream.

### `finalize-secure-file-upload`
(Implementation: `supabase/functions/finalize-secure-file-upload/index.ts`)
**Step 3 of the secure upload process.** This is called **after** the client has successfully uploaded the file to the signed URL obtained from `initiate-secure-file-upload`.

-   **V2.1 Notes:** The `relatedToEntity` field must match the one sent to the `initiate-secure-file-upload` function. This Edge Function wraps a call to the `public.finalize_document_upload` PostgreSQL function, which inserts the metadata into the central `farms.documents` table.
-   **Request Payload (JSON):**
    ```json
    {
      "sessionId": "UUID of the onboarding session",
      "documentName": "my-license.pdf",
      "relatedToEntity": "licenses", // e.g., licenses, assets
      "relatedToId": "UUID of the license record",
      "filePath": "onboarding_sessions/{sessionId}/{relatedToEntity}/{relatedToId}/{uuid}-{fileName}"
    }
    ```
-   **Success Response:** `{ "success": true, "documentId": "UUID of the new document record", "message": "Document upload finalized successfully." }`
-   **Failure Modes:**
    -   `400 Bad Request`: Missing required fields in the payload.
    -   `401 Unauthorized`: Missing or invalid JWT.
    -   `403 Forbidden`: User does not own the session.
    -   `404 Not Found`: Session not found.
    -   `500 Internal Server Error`: Error during RPC call to the database function or other unexpected server error.
-   **Security & Orchestration Notes:** This Edge Function authenticates the user, validates session ownership, and then calls the `public.finalize_document_upload` PostgreSQL function (defined in `supabase/migrations/0002_edge_functions.sql`) using an admin client. The SQL function performs the final, authoritative ownership check and `INSERT`s the metadata record into `farms.documents`.

---
## 6. Scheduled Functions

### `cleanup-orphan-storage-files`
A scheduled function for system hygiene.
-   **Trigger:** Cron schedule: daily at 2 AM UTC (`0 2 * * *`).
-   **Security & Orchestration Notes:** This function runs with a Supabase Admin client to bypass RLS, allowing it to see all files and all database records. Its logic is self-contained: list all files, query `farms.documents` for each, and delete any storage object that is older than 24 hours and does not have a corresponding database entry. This process is critical for preventing storage bloat from failed or abandoned upload attempts. 

---
## 7. High-Priority Backlog Items & Considerations

- **Review `finalize-secure-file-upload` wrapper:** Confirm if a dedicated Edge Function wrapper exists or if the frontend calls the `public.finalize_document_upload` SQL function directly via RPC. If a wrapper exists, document its path (e.g., `supabase/functions/finalize-secure-file-upload/index.ts`). **Status: ADDRESSED (Edge Function wrapper `supabase/functions/finalize-secure-file-upload/index.ts` created and documented above)**
- **Error handling for `encrypt-and-store-sensitive-field`:** Ensure client-side accurately distinguishes between a failed encryption/DB update versus a successful one, especially for UI feedback. **Status: OPEN**
- **Idempotency for `import-asset-registry-from-csv`:** Consider adding checks or strategies if a user accidentally uploads the same CSV twice. Currently, it would likely duplicate asset entries. **Status: OPEN** 