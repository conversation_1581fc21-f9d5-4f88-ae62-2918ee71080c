**Version:** 1.1
**Note:** This document outlines the key data attributes required for the NewTerra onboarding wizard from a business perspective. For the detailed technical database schema, please refer to `supabase/migrations/0000_initial_schema.sql` and subsequent migration files.

**Comprehensive Business Onboarding Workflow (Web App):**

    Develop a multi-step, user-friendly "wizard secure online form process" within the app for businesses to register their farm details after account creation.

    Collect Comprehensive Client Information: The onboarding process will gather detailed information across several categories. Storing this in our Supabase backend provides control and a central reference. The key categories include:

**Client Basic Information:**

- Full Business Name (as registered with ABN/ASIC),
- Trading Name,
- ABN,
- ACN (if applicable),
- GST Registered status,
- Main Contact Person (name, title),
- Contact Email(s) (Accounts, Admin, Personal),
- Contact Phone Number(s) (including the primary business phone stored in `business_registration.phone_number`),
- Postal Address, Property Address(es),
- Business Structure.`

**Farm-Specific Information:**

    - Main Farming Activities (e.g., orange cultivation; these are now categorized using a predefined list via `farms.activity_type_enum` in the database),
    - Names of Key Staff,
    - Licenses Held (e.g., chemical permits, machinery licenses, food safety certs),
    - Chemical and Fertiliser Usage details (),
    - Livestock Numbers (approximate, for potential future mixed farming),
    - Types of Crops (primarily oranges, note varieties),
    - Water Licences/Access.

**Financial & Bookkeeping Details:**

    - Current Bookkeeping Software,
    - Access to Bookkeeping Software (details of which are captured, including whether access has been granted, e.g., `payroll.is_access_to_software_granted` for payroll systems),
    - Bank Feeds Set Up status (in current system),
    - Accountant's & BAS Agent's details,
    - BAS Lodgement Frequency,
    - Payroll Processing needs (Yes/No, number of employees, software used, status of access granted to payroll software).

**Administration and Compliance:**

    - Vehicle/Equipment list with Rego Renewal Dates,
    - Insurance Policies (Public Liability, Vehicle, Workers Comp, Crop/Livestock) with Renewal Dates (and policy numbers/providers?),
    - Licence Expiry Dates (Chemical Handling, Heavy Vehicle, Quad Bike, Water Licences),
    - Key Contract Expiry Dates (Land/Equipment leases, Agistment, Sales contracts; the type of contract is also captured, e.g., `contracts.contract_type`), Suppliers List.

**Document & File Access (for VA migration):**

    - Details of current Cloud Storage (Google Drive, Dropbox, etc.),
    - Permissions Granted for VA access,
    - Filing System Preference (to understand current setup for migration).
    -

**Service Agreement & Consent:**

    - Confirmation of Signed Service Agreement (Digital signature in-app; captured as an agreement type in `farms.agreements` via `farms.agreement_type_enum`),
    - Privacy Consent Form (for data use, future benchmarking; captured as an agreement type),
    - Direct Debit Authority (if applicable; captured as an agreement type in `farms.agreements` using the `farms.agreement_type_enum`, rather than the previous `payments.has_signed_direct_debit` field which has been removed),
    - Acceptance of Terms and Conditions (in-app tick box; captured as a specific agreement type in `farms.agreements` via `farms.agreement_type_enum`).

**Communication Preferences:**

    - Preferred Communication Method (Email, Phone, SMS, WhatsApp – Note: MVP comms outside app),
    - Preferred Contact Times,
    - Reporting Frequency preferences (e.g., Weekly Admin Summary, Fortnightly Bookkeeping Update, Monthly Financial Reports - Contents to be defined by service offering).

Support document upload capability during the onboarding process for necessary initial documents (e.g., proof of business, key licenses if required at this stage).

Implement progress saving for the multi-step onboarding form, allowing users to save their progress and resume later.

**Core Data Integration & Handoff:**

    Establish a reliable connection and data flow to Supabase as the primary backend for storing user profiles and all collected onboarding data.

    Create secure API endpoints (Supabase Edge Functions) for all frontend-to-backend data transmission.

    Implement the data handoff process: Onboarding data and any uploaded documents are securely pushed to a designated, structured area in SharePoint for the operations team to access and begin manual service fulfillment.
