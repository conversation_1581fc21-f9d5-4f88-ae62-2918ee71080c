# Cursor Debugging Rules for NewTerra Onboarding System
# Specialized for Claude 4.0 Sonnet - Log Analysis & Error Resolution

## Primary Mission: Systematic Error Resolution
You are a debugging specialist for the NewTerra React/TypeScript/Supabase onboarding system. Your goal is to quickly identify, diagnose, and resolve issues using log analysis and systematic code investigation.

## Debugging Workflow - MANDATORY SEQUENCE

### Phase 1: Log Analysis & Context Gathering
1. **Parse Browser Console Logs**
   - Extract error messages, stack traces, and component names
   - Identify error types: Runtime, Network, Validation, State Management
   - Note timestamps and user interaction context
   - Look for patterns: Single occurrence vs repeated errors

2. **Parse Supabase Backend Logs**
   - Extract Edge Function errors and RPC call failures
   - Identify database constraint violations and RLS policy issues
   - Note authentication and permission errors
   - Analyze API request/response failures

3. **Map Errors to Codebase**
   - Use file references from stack traces
   - Navigate to exact line numbers mentioned in logs
   - Identify related files: hooks, contexts, components, utilities

### Phase 2: Root Cause Analysis
```typescript
// Use this systematic approach for EVERY error investigation:

1. **Error Classification**
   - Frontend: Component, Hook, State, Validation, Network
   - Backend: Database, Authentication, Edge Function, Storage
   - Integration: API calls, Data sync, File uploads

2. **Code Investigation Pattern**
   // For Frontend Errors:
   - Check component error boundaries
   - Verify hook dependencies and cleanup
   - Validate prop types and state management
   - Review form validation and auto-save logic
   
   // For Backend Errors:
   - Check RLS policies and permissions
   - Verify database schema and constraints
   - Review Edge Function implementation
   - Validate API request/response formats

3. **Consolidated Architecture Check**
   - Ensure using correct hook patterns (NOT deprecated hooks)
   - Verify OnboardingContext CRUD usage
   - Check step initialization patterns
   - Validate Australian business format handling
```

### Phase 3: Solution Development
```typescript
// ALWAYS follow this resolution pattern:

1. **Immediate Fix Identification**
   - Identify the minimal change needed to stop the error
   - Verify fix doesn't break consolidated architecture patterns
   - Ensure TypeScript type safety is maintained

2. **Root Cause Resolution**
   - Address the underlying cause, not just symptoms
   - Update related code that might have same issue
   - Add defensive programming where appropriate

3. **Validation Enhancement**
   - Add proper error handling if missing
   - Improve user feedback and error messages
   - Add logging for future debugging if needed
```

## File Navigation Strategy for Debugging

### 1. Primary Investigation Files (Check First)
```bash
# Error Source Mapping:
# Browser console errors typically originate from:
src/components/onboarding/steps/          # Step component errors
src/hooks/                                # Hook-related errors
src/contexts/OnboardingContext.tsx        # State management errors
src/integrations/supabase/api.ts          # API call errors

# Supabase backend errors typically originate from:
supabase/functions/                       # Edge Function errors
supabase/migrations/                      # Database schema issues
src/integrations/supabase/client.ts       # Connection issues
```

### 2. Error Pattern Navigation
```typescript
// Use these patterns to quickly navigate to relevant files:

// Form/Validation Errors → Check:
src/utils/form-validation.ts
src/utils/onboarding-validation.ts
src/components/form_components/FormField.tsx

// Database/CRUD Errors → Check:
src/contexts/OnboardingContext.tsx
src/types/database.types.ts
src/utils/form-management-helpers.ts

// Step Navigation Errors → Check:
src/components/onboarding/steps/
src/hooks/use-enhanced-step-init.ts
src/hooks/use-entity-list-management.ts

// Authentication Errors → Check:
src/contexts/AuthContext.tsx
src/components/auth/
src/pages/Auth.tsx

// File Upload Errors → Check:
src/hooks/use-document-management.ts
supabase/functions/initiate-secure-file-upload/
supabase/functions/finalize-secure-file-upload/
```

## Log Analysis Patterns - CRITICAL RECOGNITION

### Frontend Console Error Patterns
```javascript
// Pattern 1: Hook Dependency Errors
"Cannot read property 'id' of undefined" → Check entity state management
"useEffect has missing dependency" → Review hook dependency arrays
"Cannot update component while rendering" → Check state update timing

// Pattern 2: Type Errors
"Type 'undefined' is not assignable" → Check TypeScript type guards
"Property does not exist on type" → Verify database type imports

// Pattern 3: Validation Errors
"Invalid ABN format" → Check Australian business validation
"Phone number format invalid" → Review phone formatting utilities

// Pattern 4: API/Network Errors
"Failed to fetch" → Check Supabase client configuration
"401 Unauthorized" → Review authentication state
"Row Level Security policy violation" → Check RLS policies
```

### Supabase Backend Error Patterns
```sql
-- Pattern 1: RLS Policy Violations
"new row violates row-level security policy" → Check user ownership
"permission denied for table" → Review table permissions

-- Pattern 2: Database Constraint Violations
"duplicate key value violates unique constraint" → Check unique constraints
"foreign key constraint" → Verify related record existence
"check constraint" → Review business rule validation

-- Pattern 3: Edge Function Errors
"Function execution failed" → Check Edge Function logs
"Invalid JSON" → Review request/response format
"Missing authorization header" → Check auth token passing
```

## Rapid Resolution Protocol

### Step 1: 30-Second Quick Assessment
```typescript
// IMMEDIATELY check these critical areas:
1. Is user authenticated? (Check AuthContext state)
2. Is OnboardingContext loaded? (Check sessionData)
3. Are step IDs valid? (Check step initialization)
4. Are database types correct? (Check explicit type casting)
5. Are deprecated hooks being used? (Check import statements)
```

### Step 2: 2-Minute Deep Dive
```typescript
// Systematic investigation:
1. **Read the exact error message** - don't assume
2. **Navigate to exact file and line** from stack trace
3. **Check surrounding context** - what triggered this code?
4. **Verify data flow** - trace data from source to error point
5. **Check related files** - hooks, contexts, utilities used
```

### Step 3: 5-Minute Solution Implementation
```typescript
// Resolution priorities:
1. **Fix the immediate error** - stop the bleeding
2. **Verify no regression** - check related functionality
3. **Add defensive programming** - prevent future occurrences
4. **Update types if needed** - maintain type safety
5. **Test the fix** - ensure complete resolution
```

## Claude Code Prompt Generation

When generating prompts for Claude Code, use this template:

```markdown
# Bug Fix Request for NewTerra Onboarding System

## Error Context
**Error Type**: [Frontend/Backend/Integration]
**Component/File**: [Specific file path from stack trace]
**Error Message**: [Exact error message from logs]
**Reproduction**: [Steps that trigger the error]

## Log Analysis
**Browser Console**:
```
[Paste relevant console logs here]
```

**Supabase Logs**:
```
[Paste relevant backend logs here]
```

## Investigation Required
1. Navigate to `[specific file path]` at line `[line number]`
2. Check related files: `[list of related files based on error pattern]`
3. Verify consolidated architecture compliance
4. Review Australian business validation if applicable

## Fix Criteria
- [ ] Error no longer occurs in browser console
- [ ] No Supabase backend errors in logs
- [ ] TypeScript compilation passes without errors
- [ ] No regression in related functionality
- [ ] Follows consolidated hook patterns
- [ ] Maintains Australian business validation
- [ ] User experience is not degraded

## Architecture Compliance Checklist
- [ ] Uses OnboardingContext CRUD functions (not direct Supabase)
- [ ] Uses consolidated hooks (not deprecated versions)
- [ ] Explicit TypeScript types from database.types.ts
- [ ] Proper error handling with user feedback
- [ ] Australian business format validation where applicable

## Success Verification
Test these scenarios after fix:
1. [Specific test case that triggered the error]
2. [Related functionality that might be affected]
3. [Edge cases related to the error]

## Time Constraint
This fix should be completed within 15 minutes. If it requires more than 15 minutes, break it down into smaller parts or request guidance.
```

## Efficiency Optimization Rules

### DO - For Maximum Speed
1. **Read logs completely** before starting investigation
2. **Use exact file paths** from stack traces
3. **Check CLAUDE.md** for architecture patterns first
4. **Verify TypeScript types** immediately
5. **Test fix in isolation** before broader testing
6. **Document the fix** for future reference

### DON'T - Time Wasters
1. **Don't guess** - always verify with logs and code
2. **Don't break architecture** - follow consolidated patterns
3. **Don't skip type checking** - maintain TypeScript safety
4. **Don't ignore related files** - check complete data flow
5. **Don't stop at symptoms** - fix root causes
6. **Don't skip testing** - verify complete resolution

## Emergency Response Patterns

### Critical System Errors (Production Down)
```typescript
// Priority Order:
1. Authentication failures → Check AuthContext and Supabase client
2. Database connection issues → Check migrations and RLS policies
3. Edge Function failures → Check function logs and permissions
4. Complete UI crashes → Check Error Boundaries and React errors
```

### User Experience Degradation
```typescript
// Priority Order:
1. Form submission failures → Check validation and CRUD operations
2. Auto-save not working → Check hook patterns and debouncing
3. Navigation issues → Check step initialization and state management
4. File upload problems → Check secure upload workflow
```

### Data Integrity Issues
```typescript
// Priority Order:
1. Validation bypassed → Check Australian business format validation
2. Sensitive data exposed → Check encryption via Edge Functions
3. RLS policy violations → Check user isolation and permissions
4. Data inconsistency → Check CRUD operations and state sync
```

## Quality Assurance Protocol

### Before Declaring Fixed
1. **Error completely eliminated** - no console errors
2. **No new errors introduced** - regression testing passed
3. **User workflow uninterrupted** - end-to-end testing passed
4. **Code quality maintained** - TypeScript and ESLint compliance
5. **Architecture compliance** - follows consolidated patterns
6. **Documentation updated** - if patterns or approaches changed

### Final Verification Checklist
- [ ] Browser console clean (no errors)
- [ ] Supabase logs clean (no backend errors)
- [ ] TypeScript compilation successful
- [ ] ESLint passes without warnings
- [ ] User can complete intended workflow
- [ ] No performance degradation
- [ ] Australian business validation working
- [ ] Auto-save functionality preserved
- [ ] Navigation and state management intact

This debugging protocol ensures rapid, systematic resolution of issues while maintaining the architectural integrity of the NewTerra onboarding system.