import { describe, it, expect, vi } from 'vitest';
import { render, waitFor } from '@testing-library/react';
import React from 'react';
import { BusinessProfileStep } from '../src/components/onboarding/steps/BusinessProfileStep';
import { OnboardingContext, type OnboardingContextType } from '../src/contexts/OnboardingContext';

const noop = async (): Promise<void> => {
  return Promise.resolve();
};

describe('BusinessProfileStep initialization', () => {
  it('calls ensure functions only once on mount', async () => {
    const ensureStep = vi.fn().mockResolvedValue({ id: 'step1' });
    const ensureLinked = vi.fn().mockResolvedValue({ id: 'reg1' });

    const contextValue: OnboardingContextType = {
      sessionData: null,
      loading: false,
      currentStep: 1,
      error: null,
      updateCurrentStep: noop,
      refreshSessionData: noop,
      sessionId: 'session1',
      createRecord: async () => null,
      updateRecord: async () => null,
      deleteRecord: async () => false,
      upsertRecord: async () => null,
      ensureLinkedRecord: ensureLinked,
      ensureStep1BusinessProfileRecordExists: ensureStep,
      ensureStep2FarmOperationsRecordExists: noop,
      ensureStep3FinancialSystemsRecordExists: noop,
      ensureStep4AgreementsRecordExists: noop,
      invokeImportAssetsCSV: noop,
      invokeProcessDigitalSignature: noop,
      uploadAndFinalizeDocument: noop,
      setPermissions: async () => false,
      getSignedDocumentUrl: noop,
    };

    const { rerender } = render(
      <OnboardingContext.Provider value={contextValue}>
        <BusinessProfileStep />
      </OnboardingContext.Provider>
    );
    await waitFor(() => {
      expect(ensureStep).toHaveBeenCalledTimes(1);
    });

    rerender(
      <OnboardingContext.Provider value={{ ...contextValue, sessionData: { step1_businessProfile: { id: 'step1' } } }}>
        <BusinessProfileStep />
      </OnboardingContext.Provider>
    );

    await waitFor(() => {
      expect(ensureStep).toHaveBeenCalledTimes(1);
      expect(ensureLinked).toHaveBeenCalledTimes(1);
    });
  });
});
